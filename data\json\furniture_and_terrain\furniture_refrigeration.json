[{"type": "furniture", "id": "f_fridge", "name": "refrigerator", "symbol": "{", "description": "A standard household refrigerator.  If powered, it will cool its contents to extend the time before they spoil.  Deconstruct it if you want to connect it to the grid.", "color": "light_cyan", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "fridge", "count": 1}, {"item": "apartment_freezer", "count": 1}]}, "max_volume": "433 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "apartment_fridge_destroyed"}, {"group": "apartment_freezer_destroyed"}]}}, {"type": "furniture", "id": "f_minifridge", "name": "minifridge", "looks_like": "f_fridge", "symbol": "{", "color": "light_cyan", "description": "A small fridge, often used in places where full-size fridge would be too big or too much a hassle to transport, or when you only need a small amount of items chilled.  Deconstruct it if you want to connect it to the grid.", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "minifridge"}, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "minifridge", "count": 1}]}, "max_volume": "96 L", "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "minifridge_destroyed"}]}}, {"type": "furniture", "id": "f_glass_fridge", "name": "single glass door fridge", "looks_like": "f_fridge", "symbol": "{", "color": "light_cyan", "description": "A modern refrigerator with a thick sheet of glass for a door, used in stores to show off the goods while keeping them cool.  Deconstruct it if you want to connect it to the grid.", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "glass_fridge"}, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "glass_fridge", "count": 1}]}, "max_volume": "347 L", "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "glass_fridge_destroyed"}]}}, {"type": "furniture", "id": "f_glass_fridge_double", "name": "double glass door fridge", "looks_like": "f_fridge", "symbol": "{", "color": "light_cyan", "description": "A modern refrigerator with two glass doors, to fit twice as much goods for consumers.  Deconstruct it if you want to connect it to the grid.", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "glass_fridge_double"}, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "glass_fridge_double", "count": 1}]}, "max_volume": "991 L", "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "glass_fridge_double_destroyed"}]}}, {"type": "furniture", "id": "f_heavy_duty_fridge", "name": "heavy-duty fridge", "looks_like": "f_fridge", "symbol": "{", "color": "light_cyan", "description": "A heavy, bulky refrigerator, used in professional kitchens.  Deconstruct it if you want to connect it to the grid.", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "heavy_duty_fridge"}, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "heavy_duty_fridge", "count": 1}]}, "max_volume": "700 L", "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "heavy_duty_fridge_destroyed"}]}}, {"type": "furniture", "id": "f_display_fridge", "name": "display fridge", "looks_like": "f_fridge", "symbol": "{", "color": "light_cyan", "description": "A big glass chamber, designed to display different food in multiple rows, while cooling it at the same time.  Looks gorgeous, but has nothing good to show to you.  Deconstruct it if you want to connect it to the grid.", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "display_fridge"}, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "display_fridge", "count": 1}]}, "max_volume": "600 L", "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "display_fridge_destroyed"}]}}, {"type": "furniture", "id": "f_minifreezer", "name": "minifreezer", "looks_like": "minifreezer", "symbol": "{", "description": "A small upright freezer, used to store a small amount of perishables long term.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 60, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "minifreezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "minifreezer", "count": 1}]}, "max_volume": "90 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "minifreezer_destroyed"}]}}, {"type": "furniture", "id": "f_chest_minifreezer", "name": "chest minifreezer", "looks_like": "minifreezer", "symbol": "{", "description": "A small chest freezer, used to store a small amount of perishables for a long time, if you're willing to dig them out each time that is.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 60, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "chest_minifreezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "chest_minifreezer", "count": 1}]}, "max_volume": "99 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "chest_minifreezer_destroyed"}]}}, {"type": "furniture", "id": "f_freezer", "name": "chest freezer", "looks_like": "f_fridge", "symbol": "{", "description": "A chest freezer.  Big and power efficient, this is the dream for every Cataclysm survivor.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 70, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "chest_freezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "chest_freezer", "count": 1}]}, "max_volume": "350 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "chest_freezer_destroyed"}]}}, {"type": "furniture", "id": "f_heavy_duty_freezer", "name": "heavy-duty freezer", "looks_like": "f_fridge", "symbol": "{", "description": "A heavy-duty upright freezer, one that is used in a professional kitchen.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "heavy_duty_freezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "heavy_duty_freezer", "count": 1}]}, "max_volume": "745 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "heavy_duty_freezer_destroyed"}]}}, {"type": "furniture", "id": "f_glass_freezer", "name": "glass door freezer", "looks_like": "f_fridge", "symbol": "{", "description": "A commercial upright freezer, used to show ice creams, frozen veggies, pizza, or anything else a consumer may want frozen and preserved.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "glass_freezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "glass_freezer", "count": 1}]}, "max_volume": "400 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "glass_freezer_destroyed"}]}}, {"type": "furniture", "id": "f_display_freezer", "name": "display chest freezer", "looks_like": "f_fridge", "symbol": "{", "description": "A commercial chest freezer, used to show off ice creams, frozen veggies, pizza, or anything else a consumer may want frozen and preserved.  Conveniently tall enough for any curious child to gaze in at the sweets.  Deconstruct it if you want to connect it to the grid.", "color": "blue", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "display_freezer"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "NO_SELF_CONNECT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "display_freezer", "count": 1}]}, "max_volume": "320 L", "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"group": "display_freezer_destroyed"}]}}]