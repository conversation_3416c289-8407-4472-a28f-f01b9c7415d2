[{"type": "body_graph", "id": "arm_r", "parent_bodypart": "arm_r", "fill_sym": "#", "fill_color": "white", "rows": ["                                        ", "                                 11111  ", "                              11111111  ", "                             111111111  ", "                             111111111  ", "                             211111111  ", "                            2222221111  ", "                            2222222211  ", "    444                    2222222222   ", "   444444                 2222222222    ", "    444444444            22222222222    ", "       444444444444     22222222222     ", "         4444444444444422222222222      ", "            444444444442222222222       ", "              444444443333322222        ", "                  444333333322          ", "                     3333333            ", "                        33              ", "                                        ", "                                        "], "parts": {"1": {"sub_body_parts": ["arm_shoulder_r"], "select_color": "red"}, "2": {"sub_body_parts": ["arm_upper_r"], "select_color": "red"}, "3": {"sub_body_parts": ["arm_elbow_r"], "select_color": "red"}, "4": {"sub_body_parts": ["arm_lower_r"], "select_color": "red"}}}, {"type": "body_graph", "id": "arm_l", "parent_bodypart": "arm_l", "fill_sym": "#", "fill_color": "white", "mirror": "arm_r", "parts": {"1": {"sub_body_parts": ["arm_shoulder_l"], "select_color": "red"}, "2": {"sub_body_parts": ["arm_upper_l"], "select_color": "red"}, "3": {"sub_body_parts": ["arm_elbow_l"], "select_color": "red"}, "4": {"sub_body_parts": ["arm_lower_l"], "select_color": "red"}}}]