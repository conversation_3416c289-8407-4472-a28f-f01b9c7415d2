[{"type": "body_graph", "id": "torso", "parent_bodypart": "torso", "fill_sym": "#", "fill_color": "white", "rows": ["                                        ", "          2###2               ####2     ", "         #2###2#               222##    ", "    ##1111#222#1111##       2 21##1111  ", "  ###111111111111111###   22 11####111 5", " ####111111111111111####  4 111####111 5", "     111111111111111      4 1111111111 5", "      1111111111111       4 1111111111 5", "      1111111111111       44 11111111 55", "      1111111111111       44 11111111 55", "      1133333333311       44 11111111 55", "      3333333333333       44 33333333 55", "      3333333333333       44 33333333 55", "     666666666666666         66666666   ", "     333333333333333         ##33333##  ", "    ######33333######       ##########  ", "    #################       ##########  ", "    ######## ########       #########   ", "    #######   #######        #######    ", "                                        "], "parts": {"1": {"sub_body_parts": ["torso_upper"], "select_color": "red"}, "2": {"sub_body_parts": ["torso_neck"], "select_color": "red"}, "3": {"sub_body_parts": ["torso_lower"], "select_color": "red"}, "4": {"sub_body_parts": ["torso_hanging_front"], "select_color": "red"}, "5": {"sub_body_parts": ["torso_hanging_back"], "select_color": "red"}, "6": {"sub_body_parts": ["torso_waist"], "select_color": "red"}}}]