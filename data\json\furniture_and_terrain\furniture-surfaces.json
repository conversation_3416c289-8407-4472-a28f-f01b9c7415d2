[{"type": "furniture", "id": "f_counter", "name": "counter", "description": "A plain wooden countertop, either free-standing or affixed to a wall.  Makes a decent surface for storage or crafting.", "symbol": "#", "color": "blue", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["TRANSPARENT", "FLAMMABLE", "ORGANIC", "MOUNTABLE", "SHORT", "FLAT_SURF", "SMALL_HIDE"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": [6, 10]}]}, "crafting_pseudo_item": "medium_surface_pseudo", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_cupboard", "name": "cupboard", "symbol": "#", "description": "Store your cups.  Or other things, if you're feeling wild.", "color": "blue", "move_cost_mod": 1, "coverage": 55, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "CONTAINER", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "FLAT_SURF", "SMALL_HIDE"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "2x4", "count": 3}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": [6, 8]}]}, "bash": {"str_min": 8, "str_max": 30, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [1, 3]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [2, 6]}, {"item": "splinter", "count": 1}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_counter_gate_c", "name": "closed counter gate", "looks_like": "f_counter", "description": "A commercial-quality swinging door made of wood that allows passage behind counters.  It's currently closed.", "symbol": "+", "color": "blue", "move_cost_mod": 0, "required_str": 4, "coverage": 60, "rotates_to": "INDOORFLOOR", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "DOOR", "ORGANIC"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "open": "f_counter_gate_o", "crafting_pseudo_item": "medium_surface_pseudo", "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": 10}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_counter_gate_o", "name": "open counter gate", "description": "A commercial-quality swinging door made of wood that allows passage behind counters.  It's open, allowing passage.", "symbol": ".", "color": "blue", "move_cost_mod": 0, "required_str": 4, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "FLAT", "ROAD", "ORGANIC"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "close": "f_counter_gate_c", "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": 10}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_desk", "name": "desk", "symbol": "#", "description": "A wooden desk with drawers.  You could decorate it with trinkets, eat a meal off it, use it as a workbench, read a book, write in a journal, put a laptop on it, put your feet up, rest your head for a nap if you're sitting down… who knew desks were so useful?", "color": "light_red", "move_cost_mod": 1, "coverage": 45, "required_str": 5, "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "CONTAINER", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "FLAT_SURF", "SMALL_HIDE"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "2x4", "count": 8}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": [16, 24]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 7]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [10, 16]}, {"item": "splinter", "count": [4, 12]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_workbench", "name": "workbench", "description": "A sturdy workbench built out of metal.  It is perfect for crafting large and heavy things.", "symbol": "#", "color": "red", "move_cost_mod": 2, "required_str": 10, "looks_like": "f_lab_bench", "crafting_pseudo_item": "large_surface_pseudo", "flags": ["TRANSPARENT", "PLACE_ITEM", "MOUNTABLE", "FLAT_SURF", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "workbench", "count": 1}]}, "bash": {"str_min": 35, "str_max": 80, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [4, 6]}, {"item": "sheet_metal", "count": [0, 1]}, {"item": "sheet_metal_small", "count": [12, 24]}, {"item": "steel_chunk", "count": [4, 8]}, {"item": "scrap", "count": [12, 24]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.2, "mass": 500000, "volume": "200 L"}}, {"type": "furniture", "id": "f_leather_tarp", "name": "leather tarp", "description": "A large sheet of leather lying on the ground.  You could use it as a picnic blanket, but it's more valuable as a surface for butchering as it does not soak up blood.", "symbol": "D", "bgcolor": "brown", "move_cost_mod": 0, "required_str": 3, "deployed_item": "leather_tarp", "crafting_pseudo_item": "large_surface_pseudo", "flags": ["TRANSPARENT", "SHORT", "FLAT_SURF", "CAN_SIT"], "deconstruct": {"items": [{"item": "leather_tarp", "count": 1}]}, "bash": {"str_min": 5, "str_max": 10, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "leather_tarp", "count": [1, 1]}]}, "examine_action": "workbench", "workbench": {"multiplier": 0.85, "mass": 500000, "volume": "500 L"}}, {"type": "furniture", "id": "f_plastic_groundsheet", "name": "plastic groundsheet", "description": "A large sheet of thick plastic lying on the ground.  It could be a useful surface for butchering, perhaps.", "symbol": "D", "bgcolor": "light_blue", "move_cost_mod": 0, "required_str": 3, "deployed_item": "plastic_sheet", "crafting_pseudo_item": "large_surface_pseudo", "flags": ["TRANSPARENT", "SHORT", "FLAT_SURF", "CAN_SIT"], "deconstruct": {"items": [{"item": "plastic_sheet", "count": 1}]}, "bash": {"str_min": 5, "str_max": 10, "sound": "whuff!", "sound_fail": "crinkle.", "items": [{"item": "plastic_sheet", "count": 1}]}, "examine_action": "workbench", "workbench": {"multiplier": 0.85, "mass": 500000, "volume": "500 L"}}, {"type": "furniture", "id": "f_fiber_mat", "name": "fiber mat", "description": "A large mat woven from fibrous material.  You could use it as a picnic blanket, but it's more valuable as a surface for butchering.  Too thin to be used as a comfortable sleeping mat.", "symbol": "Q", "bgcolor": "yellow", "move_cost_mod": 0, "required_str": 3, "deployed_item": "fiber_mat", "crafting_pseudo_item": "large_surface_pseudo", "flags": ["TRANSPARENT", "SHORT", "FLAT_SURF", "CAN_SIT"], "deconstruct": {"items": [{"item": "fiber_mat", "count": 1}]}, "bash": {"str_min": 5, "str_max": 10, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "fiber_mat", "count": [1, 1]}]}, "examine_action": "workbench", "workbench": {"multiplier": 0.85, "mass": 500000, "volume": "500 L"}}, {"type": "furniture", "id": "f_tourist_table", "name": "folding table", "description": "A small metal folding table, ideal for off-road trips.  Can be used as a workbench in a pinch.", "symbol": "#", "bgcolor": "light_gray", "move_cost_mod": 2, "coverage": 40, "required_str": 5, "deployed_item": "tourist_table", "looks_like": "f_table", "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "MOUNTABLE", "SHORT", "FLAT_SURF", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "tourist_table", "count": 1}]}, "bash": {"str_min": 16, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "pipe", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [4, 8]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.05, "mass": 100000, "volume": "35 L"}}, {"type": "furniture", "id": "f_table", "name": "table", "description": "A standard wooden table.  You could sit down at it to eat like a civilized person.", "symbol": "#", "color": "red", "move_cost_mod": 2, "coverage": 50, "required_str": 5, "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "FLAMMABLE", "ORGANIC", "MOUNTABLE", "SHORT", "FLAT_SURF", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": [6, 8]}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 4]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_table_fancy", "name": "fancy table", "description": "A fancy table for a fancy person.  Functions as both a table and status symbol.", "symbol": "#", "color": "red", "looks_like": "f_table", "move_cost_mod": 2, "coverage": 50, "required_str": 5, "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "FLAMMABLE", "ORGANIC", "MOUNTABLE", "SHORT", "FLAT_SURF", "SMALL_HIDE", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "v_table", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [1, 3]}, {"item": "wood_panel", "prob": 30}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": [3, 6]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_coffee_table", "name": "coffee table", "description": "A low table usually found in living rooms, it makes an ideal surface for playing games, showing off some tasteful books, or presumably for putting coffee cups on.  No one cares if you use a coaster anymore.", "symbol": "#", "color": "red", "looks_like": "f_table", "move_cost_mod": 2, "coverage": 50, "required_str": 5, "flags": ["TRANSPARENT", "FLAMMABLE", "ORGANIC", "MOUNTABLE", "SHORT", "FLAT_SURF", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": 2}, {"item": "wood_panel", "count": 1}, {"item": "nail", "charges": [6, 8]}]}, "bash": {"str_min": 10, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [1, 2]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}, "examine_action": "workbench", "workbench": {"multiplier": 0.85, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_metal_table", "name": "metal table", "description": "A serviceable but simple table made of scrap metal.", "symbol": "T", "color": "light_gray", "move_cost_mod": 4, "coverage": 40, "required_str": 8, "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "MOUNTABLE", "FLAT_SURF", "SHORT", "SMALL_HIDE"], "bash": {"str_min": 20, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "sheet_metal", "count": [0, 1]}, {"item": "pipe", "count": [0, 4]}]}, "deconstruct": {"items": [{"item": "pipe_fittings", "count": 2}, {"item": "scrap", "count": 4}, {"item": "sheet_metal", "count": 1}, {"item": "pipe", "count": 4}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 500000, "volume": "300 L"}}]