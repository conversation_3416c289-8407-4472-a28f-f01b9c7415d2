name: Flake8 Python linting

on:
  push:
    branches:
    - master
    paths:
    - '**.py'
  pull_request:
    branches:
    - master
    paths:
    - '**.py'

permissions:
  contents: read

jobs:
  check:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: install dependencies
      run: sudo apt-get install flake8
    - name: Enable flake8 problem matcher
      run: echo "::add-matcher::build-scripts/problem-matchers/flake8.json"
    - name: run Flake8
      run: make python-check
