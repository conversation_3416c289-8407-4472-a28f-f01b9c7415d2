name: 'Stale issue handler'
on:
  workflow_dispatch:
  schedule:
    - cron: '30 * * * *'
permissions:
  actions: write
  contents: write
  issues: write
  pull-requests: write
jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - name: get-time
        run: |
          echo "time=$(date +%I)" >> $GITHUB_ENV
      - uses: actions/stale@main
        id: stale
        with:
          stale-issue-label: stale
          stale-pr-label: stale
          stale-issue-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions. Please do not bump or comment on this issue unless you are actively working on it. Stale issues, and stale issues that are closed are still considered.'
          stale-pr-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions. Please do not bump or comment on this issue unless you are actively working on it. Stale issues, and stale issues that are closed are still considered.'
          days-before-stale: ${{ contains(from<PERSON><PERSON>('["01", "02", "03", "04", "05", "06"]'), env.time) && 30 || 15 }}
          days-before-close: ${{ contains(from<PERSON><PERSON>('["01", "02", "03", "04", "05", "06"]'), env.time) && 30 || 15 }}
          start-date: '2020-05-07'
          operations-per-run: 110
          exempt-issue-labels: 'Accessibility,<Bug>,<Bugfix>,<Crash / Freeze>,Organization: Bounty,Good First Issue,Help Wanted,(P1 - Critical),(P2 - High),(P3 - Medium),(P4 - Low),(P5 - Long-term),(S2 - Confirmed),0.G String Freeze,0.G Feature Freeze,0.G Content Freeze'
          exempt-pr-labels: '<Bug>,<Bugfix>,<Crash / Freeze>,Organization: Bounty,Good First Issue,(P1 - Critical),(P2 - High),(P3 - Medium),(P4 - Low),(P5 - Long-term),(S2 - Confirmed),0.G String Freeze,0.G Feature Freeze,0.G Content Freeze'
          any-of-labels: ${{ contains(fromJson('["01", "02", "03", "04", "05", "06"]'), env.time) && "" || "<Suggestion / Discussion>" }}
          exempt-all-milestones: true
          exempt-all-assignees: true
      - name: Print outputs
        run: echo ${{ join(steps.stale.outputs.*, ',') }}
