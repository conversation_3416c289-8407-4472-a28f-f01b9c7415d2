[{"type": "body_graph", "id": "foot_r", "parent_bodypart": "foot_r", "fill_sym": "#", "fill_color": "white", "rows": ["                                        ", "                                        ", "                                        ", " ############                           ", " ############                           ", " ############                           ", "  ###########3                          ", "  #######33333                          ", "  3333333333333                         ", "  33333333333333                        ", " 433333333333331111                     ", " 444333333333311111111                  ", "4444433333331111111111111111            ", "44444444444111111111111111111111        ", "5554444441111111111111111111111222222222", " 555555555555555555555555555555222222222", "               55555555555555555555555  ", "                                        ", "                                        ", "                                        "], "parts": {"1": {"sub_body_parts": ["foot_arch_r"], "select_color": "red"}, "2": {"sub_body_parts": ["foot_toes_r"], "select_color": "red"}, "3": {"sub_body_parts": ["foot_ankle_r"], "select_color": "red"}, "4": {"sub_body_parts": ["foot_heel_r"], "select_color": "red"}, "5": {"sub_body_parts": ["foot_sole_r"], "select_color": "red"}}}, {"type": "body_graph", "id": "foot_l", "parent_bodypart": "foot_l", "fill_sym": "#", "fill_color": "white", "mirror": "foot_r", "parts": {"1": {"sub_body_parts": ["foot_arch_l"], "select_color": "red"}, "2": {"sub_body_parts": ["foot_toes_l"], "select_color": "red"}, "3": {"sub_body_parts": ["foot_ankle_l"], "select_color": "red"}, "4": {"sub_body_parts": ["foot_heel_l"], "select_color": "red"}, "5": {"sub_body_parts": ["foot_sole_l"], "select_color": "red"}}}]