name: PR Validator
on:
  pull_request:
    branches:
    - master
    types: [opened, edited, synchronize]
jobs:
  validate:
    name: Validate PR
    runs-on: ubuntu-latest
    steps:
      - name: "Validate SUMMARY"
        uses: CleverRaven/pr-validator@master
        with:
          description-regex: '(\n|^)#### Summary\s+`{0,3}(SUMMARY:\s+)?(None|((Features|Content|Interface|Mods|Balance|Bugfixes|Performance|Infrastructure|Build|I18N) +".+"))`{0,3}\s*(\n|$)'
          description-regex-flags: 'i'
