[{"type": "body_graph", "id": "leg_r", "parent_bodypart": "leg_r", "fill_sym": "#", "fill_color": "white", "rows": ["        111111                          ", "       11111111222                      ", "       111111222222222                  ", "       111222222222222222               ", "        22222222222222222222            ", "          2222222222222222223333        ", "               22222222222223333        ", "                      2222223333        ", "                         444433         ", "                        4444444         ", "                        4444444         ", "                        4444444         ", "                        444444          ", "                         44444          ", "                         4444           ", "                         4444           ", "                          444           ", "                          444           ", "                          ####          ", "                          ######        "], "parts": {"1": {"sub_body_parts": ["leg_hip_r"], "select_color": "red"}, "2": {"sub_body_parts": ["leg_upper_r"], "select_color": "red"}, "3": {"sub_body_parts": ["leg_knee_r"], "select_color": "red"}, "4": {"sub_body_parts": ["leg_lower_r"], "select_color": "red"}}}, {"type": "body_graph", "id": "leg_l", "parent_bodypart": "leg_l", "fill_sym": "#", "fill_color": "white", "mirror": "leg_r", "parts": {"1": {"sub_body_parts": ["leg_hip_l"], "select_color": "red"}, "2": {"sub_body_parts": ["leg_upper_l"], "select_color": "red"}, "3": {"sub_body_parts": ["leg_knee_l"], "select_color": "red"}, "4": {"sub_body_parts": ["leg_lower_l"], "select_color": "red"}}}]