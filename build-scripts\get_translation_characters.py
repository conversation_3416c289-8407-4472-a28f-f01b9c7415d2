#!/usr/bin/env python3

# Use the Unicode Common Locale Data Repository to create code that
# creates ImGui glyph ranges for each locale, so that every locale can
# have properly rendered text.

import sys
from cldr_language_helpers import alphabets


def main():
    print("// generated by get_translation_characters.py; example:")
    print("//   ./build-scripts/get_translation_characters.py en ar cs da de "
          "el es fr hu id is it ja ko nb nl pl pt ru sr tr uk_UA zh_Hans "
          "zh_Hant > src/cldr/imgui-glyph-ranges.cpp\n")
    print("// NOLINTBEGIN(cata-static-declarations,readability-function-size)")
    try:
        for language in sys.argv[1:]:
            print_func(language)
        print("// NOLINTEND(cata-static-declarations,"
              "readability-function-size)")
        return 0
    except KeyError as x:
        print(f"Unknown language code “{x.args[0]}”", file=sys.stderr)
        return 1


def print_func(language):
    print(f"static void AddGlyphRangesFromCLDRFor{language.upper()}("
          "ImFontGlyphRangesBuilder *b) {{")
    print('\n'.join([print_add_char(c)
                     for c in alphabets.ALPHABETS_BY_LANG_MAP[language]]))
    print('\n'.join([print_add_char(c.upper())
                     for c in alphabets.ALPHABETS_BY_LANG_MAP[language]]))
    print('\n'.join([print_add_char(c)
                     for c in alphabets.NUMBERS_BY_LANG_MAP[language]]))
    print('\n'.join([print_add_char(c)
                     for c in alphabets.PUNCTUATION_BY_LANG_MAP[language]]))
    print("}\n")


def print_add_char(c):
    return '\n'.join([f"b->AddChar({hex(ord(c))});" for c in c])


if __name__ == '__main__':
    sys.exit(main())
