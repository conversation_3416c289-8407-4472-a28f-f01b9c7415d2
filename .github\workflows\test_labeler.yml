name: Apply Test Labels

on:
  workflow_run:
    workflows: ["astyle", "JSON Validation", "General build matrix"]
    types: [completed]

jobs:
  labeling:
    if: github.repository == 'CleverRaven/Cataclysm-DDA'
    runs-on: ubuntu-latest
    steps:
    - name: setLabel
      id: setLabel
      run: |
        if [[ "${{ github.event.workflow_run.name }}" == "astyle" ]]; then
          echo "label=astyled" >> $GITHUB_OUTPUT
        fi
        if [[ "${{ github.event.workflow_run.name }}" == "JSON Validation" ]]; then
          echo "label=json-styled" >> $GITHUB_OUTPUT
        fi
        if [[ "${{ github.event.workflow_run.name }}" == "General build matrix" ]]; then
          echo "label=BasicBuildPassed" >> $GITHUB_OUTPUT
        fi
    - name: Download success artifact from basic build
      if: ${{ github.event.workflow_run.name == 'General build matrix' }}
      uses: dawidd6/action-download-artifact@v3.1.4
      with:
        workflow: ${{ github.event.workflow_run.name }}
        run_id: ${{ github.event.workflow_run.id }}
        name: basic-build
    - name: set-basic-build-success
      id: set-basic-build-success
      run: echo "basic-build-success=$( cat basic-build )" >> $GITHUB_OUTPUT
    - name: Download pr id artifact
      uses: dawidd6/action-download-artifact@v3.1.4
      with:
        workflow: ${{ github.event.workflow_run.name }}
        run_id: ${{ github.event.workflow_run.id }}
        name: pull_request_id
    - name: set-pr-id
      id: set-pr-id
      run: echo "pr-id=$( cat pull_request_id )" >> $GITHUB_OUTPUT
    - name: set-label
      if: ${{ github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.conclusion != 'skipped' || steps.set-basic-build-success.outputs.basic-build-success == 'true' }}
      uses: actions/github-script@v7
      with:
        script: |
            if ('${{ steps.set-pr-id.outputs.pr-id }}'.trim().length == 0) {
              console.log("The PR ID artifact does not contain a pull request number.")
            } else {
              github.rest.issues.addLabels({
                issue_number: ${{ steps.set-pr-id.outputs.pr-id }},
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['${{ steps.setLabel.outputs.label }}']
              })
            }
    - name: remove-label
      if: ${{ github.event.workflow_run.conclusion == 'failure' && steps.set-basic-build-success.outputs.basic-build-success != 'true' }}
      uses: actions/github-script@v7
      with:
        script: |
            if ('${{ steps.set-pr-id.outputs.pr-id }}'.trim().length == 0) {
              console.log("The PR ID artifact does not contain a pull request number.")
            } else {
              try {
                github.rest.issues.removeLabel({
                  issue_number: ${{ steps.set-pr-id.outputs.pr-id }},
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  name: '${{ steps.setLabel.outputs.label }}'
                })
              } catch (e) {
                console.log(e)
              }
            }
            
