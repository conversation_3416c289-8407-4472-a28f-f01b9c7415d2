[{"id": "f_tatami", "type": "furniture", "name": "tatami mat", "description": "A type of mat used as a flooring material in traditional Japanese-style rooms.", "symbol": "#", "color": "brown", "move_cost_mod": 0, "comfort": 2, "floor_bedding_warmth": -1500, "required_str": 7, "bash": {"str_min": 8, "str_max": 30, "sound": "crunch.", "sound_fail": "whump.", "items": [{"item": "straw_pile", "count": [5, 8]}, {"item": "cotton_patchwork", "count": [2, 4]}, {"item": "string_36", "count": [1, 2]}]}, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "ORGANIC"]}, {"type": "furniture", "id": "f_pillow_fort", "name": "pillow fort", "symbol": "^", "description": "A comfy place to hide from the world.  Not very defensible, though.", "color": "white", "move_cost_mod": 3, "coverage": 90, "comfort": 4, "floor_bedding_warmth": 1000, "required_str": -1, "deconstruct": {"items": [{"item": "pillow", "count": 19}, {"item": "blanket", "count": 3}]}, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "REDUCE_SCENT", "EASY_DECONSTRUCT", "HIDE_PLACE", "NO_SIGHT"], "bash": {"str_min": 1, "str_max": 1, "sound": "paf!", "sound_fail": "poof.", "items": [{"item": "pillow", "count": 19}, {"item": "blanket", "count": 3}]}}, {"type": "furniture", "id": "f_cardboard_fort", "name": "cardboard fort", "symbol": "^", "description": "A fort built by tipping a cardboard box on its side, lining it with blankets, and partly weather-sealing it with a plastic sheet.", "color": "brown", "move_cost_mod": 3, "coverage": 95, "comfort": 4, "floor_bedding_warmth": 700, "required_str": 3, "deconstruct": {"items": [{"item": "box_large", "count": 1}, {"item": "plastic_sheet", "count": 1}, {"item": "blanket", "count": 2}, {"item": "pillow", "count": 4}]}, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "REDUCE_SCENT", "EASY_DECONSTRUCT", "HIDE_PLACE", "NO_SIGHT", "BLOCK_WIND"], "bash": {"str_min": 4, "str_max": 15, "sound": "crumple!", "sound_fail": "thud.", "items": [{"item": "paper", "count": [50, 100]}]}}, {"type": "furniture", "id": "f_cardboard_wall", "name": "cardboard wall", "symbol": "#", "color": "brown", "move_cost_mod": -1, "coverage": 100, "description": "A pile of cardboard boxes that have been filled with rags and junk and stacked together like bricks to form a wall.", "required_str": -1, "flags": ["NOITEM", "BLOCKSDOOR", "FLAMMABLE_ASH", "ORGANIC", "EASY_DECONSTRUCT", "BLOCK_WIND"], "deconstruct": {"items": [{"item": "box_large", "count": 1}, {"item": "box_medium", "count": 2}, {"item": "box_small", "count": 4}, {"item": "sheet_cotton", "count": [6, 7]}, {"item": "cotton_patchwork", "count": [7, 12]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": 20}, {"item": "plastic_sheet", "count": 2}]}, "bash": {"str_min": 6, "str_max": 20, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "box_large", "count": [0, 1]}, {"item": "box_medium", "count": [0, 2]}, {"item": "box_small", "count": [0, 4]}, {"item": "sheet_cotton", "count": [2, 6]}, {"item": "cotton_patchwork", "count": [12, 10]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": [5, 20]}, {"item": "plastic_sheet", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_cardboard_floor", "name": "cardboard floor", "symbol": "_", "color": "brown", "move_cost_mod": 0, "description": "A flat floor made of sheets of cardboard duct-taped together, covering a plastic sheet on the ground.", "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "FLAT", "ORGANIC", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "cardboard", "count": 10}, {"item": "paper", "count": 50}, {"item": "plastic_sheet", "count": 2}]}, "bash": {"str_min": 1, "str_max": 10, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "cardboard", "count": [5, 9]}, {"item": "paper", "count": [10, 40]}, {"item": "plastic_sheet", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_cardboard_roof", "name": "cardboard roof", "symbol": "_", "color": "brown", "move_cost_mod": 0, "description": "A sloped roof made of cardboard boxes, stuffed with scrap cardboard and paper and layered atop each other.  Sheets of plastic are carefully arranged on top to shed water.  It looks like it could last a season or two at least before crumbling.", "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "FLAT", "ORGANIC", "EASY_DECONSTRUCT", "ALLOW_ON_OPEN_AIR"], "deconstruct": {"items": [{"item": "box_small", "count": 10}, {"item": "cardboard", "count": 10}, {"item": "sheet_cotton", "count": 1}, {"item": "cotton_patchwork", "count": 2}, {"item": "paper", "count": 50}, {"item": "plastic_sheet", "count": 2}]}, "bash": {"str_min": 1, "str_max": 10, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "box_small", "count": [0, 10]}, {"item": "cardboard", "count": [5, 9]}, {"item": "paper", "count": [10, 40]}, {"item": "sheet_cotton", "count": [0, 1]}, {"item": "cotton_patchwork", "count": [0, 10]}, {"item": "plastic_sheet", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_cardboard_door_o", "name": "cardboard door", "symbol": ".", "color": "brown", "move_cost_mod": 1, "coverage": 100, "description": "A pile of cardboard boxes that have been filled with rags and junk and stacked together like bricks to form a wall.  The largest box has been cut open and folded to form a makeshift door, which is open.", "required_str": -1, "rotates_to": "INDOORFLOOR", "flags": ["NOITEM", "BLOCKSDOOR", "FLAMMABLE_ASH", "ORGANIC", "EASY_DECONSTRUCT", "BLOCK_WIND", "DOOR"], "deconstruct": {"items": [{"item": "box_large", "count": 1}, {"item": "box_medium", "count": 2}, {"item": "box_small", "count": 4}, {"item": "sheet_cotton", "count": [6, 7]}, {"item": "cotton_patchwork", "count": [10, 12]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": 20}, {"item": "plastic_sheet", "count": 2}]}, "bash": {"str_min": 4, "str_max": 12, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "box_large", "count": [0, 1]}, {"item": "box_medium", "count": [0, 2]}, {"item": "box_small", "count": [0, 4]}, {"item": "sheet_cotton", "count": [2, 6]}, {"item": "cotton_patchwork", "count": [4, 10]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": [5, 20]}, {"item": "plastic_sheet", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_cardboard_door", "name": "cardboard door", "symbol": "+", "color": "brown", "move_cost_mod": -1, "coverage": 100, "description": "A pile of cardboard boxes that have been filled with rags and junk and stacked together like bricks to form a wall.  The largest box has been cut open and folded to form a makeshift door, which is closed.", "required_str": -1, "rotates_to": "INDOORFLOOR", "flags": ["NOITEM", "FLAMMABLE_ASH", "ORGANIC", "EASY_DECONSTRUCT", "BLOCK_WIND", "DOOR"], "open": "f_cardboard_door_o", "deconstruct": {"items": [{"item": "box_large", "count": 1}, {"item": "box_medium", "count": 2}, {"item": "box_small", "count": 4}, {"item": "sheet_cotton", "count": [6, 7]}, {"item": "cotton_patchwork", "count": [10, 12]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": 20}, {"item": "plastic_sheet", "count": 2}]}, "bash": {"str_min": 4, "str_max": 13, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "box_large", "count": [0, 1]}, {"item": "box_medium", "count": [0, 2]}, {"item": "box_small", "count": [0, 4]}, {"item": "sheet_cotton", "count": [2, 6]}, {"item": "cotton_patchwork", "count": [12, 10]}, {"item": "paper", "count": [30, 80]}, {"item": "plastic_chunk", "count": [5, 20]}, {"item": "plastic_sheet", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_beaded_door", "name": "beaded curtain", "description": "A curtain of wooden beads, thick enough that you can't see through them.  They could be pulled aside effortlessly.", "symbol": "+", "color": "pink", "looks_like": "f_canvas_door", "move_cost_mod": 6, "coverage": 90, "required_str": -1, "rotates_to": "INDOORFLOOR", "flags": ["NOITEM", "DOOR", "PERMEABLE", "FLAMMABLE_ASH", "MOUNTABLE"], "open": "f_beaded_door_o", "deconstruct": {"items": [{"item": "stick_long", "count": 1}, {"item": "string_36", "count": 20}, {"item": "wooden_bead", "charges": 2000}]}, "bash": {"str_min": 1, "str_max": 1, "sound": "clickity clack… clack… clack", "sound_fail": "clickity clack… clack", "sound_vol": 15, "items": [{"item": "string_36", "count": [3, 15]}, {"item": "stick_long", "count": 1}, {"item": "wooden_bead", "charges": [200, 500]}]}}, {"type": "furniture", "id": "f_beaded_door_o", "name": "open beaded curtain", "description": "A curtain of wooden beads that has been pulled aside.", "symbol": ".", "color": "pink", "looks_like": "f_canvas_door_o", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "FLAT"], "close": "f_beaded_door", "deconstruct": {"items": [{"item": "stick_long", "count": 1}, {"item": "string_36", "count": 20}, {"item": "wooden_bead", "charges": 2000}]}, "bash": {"str_min": 1, "str_max": 1, "sound": "clickity clack… clack… clack!", "sound_fail": "clickity clack… clack", "sound_vol": 15, "items": [{"item": "string_36", "count": [3, 15]}, {"item": "stick_long", "count": 1}, {"item": "wooden_bead", "charges": [200, 500]}]}}, {"type": "furniture", "id": "f_canvas_floor", "name": "canvas floor", "description": "Flooring made out of stretched, waterproof cloth.  Helps keep dirt out of the tent.", "symbol": "#", "color": "white", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE", "PLACE_ITEM"]}, {"type": "furniture", "id": "f_canvas_wall", "name": "canvas wall", "description": "A tent wall made of stretched, waterproof cloth.", "symbol": "#", "color": "blue", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_large_canvas_wall", "name": "canvas wall", "description": "A tent wall made of stretched, heavy-duty, waterproof cloth.", "symbol": "#", "color": "blue", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_skin_wall", "name": "large skin wall", "looks_like": "f_skin_wall", "description": "A large tent wall made out of stretched animal skin.  It'll keep the rain out, but anything trying to get in could rip through it fairly easily.", "symbol": "#", "color": "brown", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_skin_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_skin_groundsheet", "name": "large skin groundsheet", "looks_like": "f_skin_groundsheet", "description": "A large animal-skin groundsheet to keep your feet dry inside the shelter.", "symbol": ";", "color": "brown", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_skin_center_groundsheet"]}}, {"type": "furniture", "id": "f_skin_center_groundsheet", "name": "large skin groundsheet", "looks_like": "f_skin_groundsheet", "description": "A large animal-skin groundsheet to keep your feet dry inside the shelter.", "symbol": ";", "color": "brown", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "examine_action": "portable_structure", "deployed_item": "large_shelter_kit", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "items": [{"item": "large_damaged_shelter_kit"}], "tent_centers": ["f_skin_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_skin_door", "name": "large animalskin flap", "looks_like": "f_skin_door", "description": "A large animal-skin flap that can be pulled aside.", "symbol": "+", "color": "white", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "open": "f_large_skin_door_o", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_skin_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_skin_door_o", "name": "open animalskin flap", "description": "A large animal-skin flap that has been pulled aside.", "looks_like": "f_skin_door_o", "symbol": ".", "color": "brown", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "close": "f_large_skin_door", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_skin_center_groundsheet"]}}, {"type": "furniture", "id": "f_canvas_door", "name": "canvas flap", "description": "A canvas flap door that can be pulled aside.", "symbol": "+", "color": "blue", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "rotates_to": "INDOORFLOOR", "flags": ["FLAMMABLE_HARD", "NOITEM", "DOOR", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "open": "f_canvas_door_o", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_canvas_door_o", "name": "open canvas flap", "description": "A canvas flap door that has been pulled aside.", "symbol": ".", "color": "blue", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "close": "f_canvas_door", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet", "f_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_canvas_door", "name": "canvas flap", "description": "A heavy canvas flap door that can be pulled aside.", "symbol": "+", "color": "blue", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "rotates_to": "INDOORFLOOR", "flags": ["FLAMMABLE_HARD", "NOITEM", "DOOR", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "open": "f_large_canvas_door_o", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_center_groundsheet"]}}, {"type": "furniture", "id": "f_large_canvas_door_o", "name": "open canvas flap", "description": "A heavy canvas flap door that has been pulled aside.", "symbol": ".", "color": "blue", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "close": "f_large_canvas_door", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_center_groundsheet"]}}, {"type": "furniture", "id": "f_groundsheet", "name": "groundsheet", "description": "A plastic groundsheet to keep your feet dry inside the tent.", "symbol": ";", "color": "green", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "examine_action": "portable_structure", "deployed_item": "tent_kit", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "items": [{"item": "broketent"}], "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_large_groundsheet", "name": "groundsheet", "description": "A large plastic groundsheet to keep your feet dry inside the tent.", "symbol": ";", "color": "green", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "tent_centers": ["f_center_groundsheet"]}}, {"type": "furniture", "id": "f_center_groundsheet", "name": "groundsheet", "description": "A plastic groundsheet to keep your feet dry inside the tent.", "symbol": ";", "color": "green", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "examine_action": "portable_structure", "deployed_item": "large_tent_kit", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "collapse_radius": 2, "items": [{"item": "largebroketent"}], "tent_centers": ["f_center_groundsheet"]}}, {"type": "furniture", "id": "f_fema_groundsheet", "name": "groundsheet", "description": "A government-issue plastic groundsheet to keep your feet dry, it was clearly made by the lowest bidder.", "symbol": ";", "color": "green", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "ORGANIC", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_skin_wall", "name": "animalskin wall", "symbol": "#", "description": "A tent wall made out of stretched animal skin.  It'll keep the rain out, but anything trying to get in could rip through it fairly easily.", "color": "brown", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_skin_door", "name": "animalskin flap", "description": "An animal-skin flap that can be pulled aside.", "symbol": "+", "color": "white", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["FLAMMABLE_HARD", "NOITEM", "BLOCK_WIND", "SUN_ROOF_ABOVE"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "open": "f_skin_door_o", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_skin_door_o", "name": "open animalskin flap", "description": "An animal-skin flap that has been pulled aside.", "symbol": ".", "color": "white", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT"], "connect_groups": "CANVAS_WALL", "connects_to": "CANVAS_WALL", "close": "f_skin_door", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_skin_groundsheet", "name": "animalskin floor", "description": "An animal-skin groundsheet to keep your feet dry inside the shelter.", "symbol": ";", "color": "brown", "move_cost_mod": 0, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "INDOORS", "NOCOLLIDE", "SUN_ROOF_ABOVE"], "examine_action": "portable_structure", "deployed_item": "shelter_kit", "bash": {"str_min": 1, "str_max": 8, "sound": "rrrrip!", "sound_fail": "slap!", "sound_vol": 8, "items": [{"item": "damaged_shelter_kit"}], "tent_centers": ["f_groundsheet", "f_fema_groundsheet", "f_skin_groundsheet"]}}, {"type": "furniture", "id": "f_rubble", "name": "pile of rubble", "symbol": "^", "description": "A pile of various metals, bricks, and other building materials.  You could clear it with a shovel.", "color": "light_gray", "move_cost_mod": 6, "max_volume": "750 L", "required_str": -1, "flags": ["TRANSPARENT", "UNSTABLE", "ROUGH", "PLACE_ITEM", "MOUNTABLE", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "TINY", "RUBBLE"], "examine_action": "rubble"}, {"type": "furniture", "id": "f_rubble_rock", "name": "pile of rocky rubble", "description": "A loose pile of rocks.  Useless?", "symbol": "^", "color": "dark_gray", "move_cost_mod": 6, "max_volume": "750 L", "required_str": -1, "flags": ["TRANSPARENT", "UNSTABLE", "ROUGH", "PLACE_ITEM", "MOUNTABLE", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "SHORT", "RUBBLE"], "examine_action": "rubble"}, {"type": "furniture", "id": "f_rubble_landfill", "name": "pile of trashy rubble", "description": "A pile of trash topped with dirt and grass.  It smells gross, but one man's trash…", "symbol": "#", "color": "green", "move_cost_mod": 8, "max_volume": "750 L", "required_str": -1, "flags": ["TRANSPARENT", "UNSTABLE", "ROUGH", "PLACE_ITEM", "MOUNTABLE", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "SMALL_HIDE", "SHORT", "RUBBLE"], "examine_action": "rubble"}, {"type": "furniture", "id": "f_wreckage", "name": "metal wreckage", "description": "A pile of various bent and twisted metals.  Some of it looks pretty rusty, you wouldn't want to cut yourself on this.", "symbol": "#", "color": "cyan", "move_cost_mod": 6, "max_volume": "750 L", "required_str": -1, "crafting_pseudo_item": "fake_sharp_metal_wreckage", "flags": ["TRANSPARENT", "UNSTABLE", "ROUGH", "SHARP", "PLACE_ITEM", "MOUNTABLE", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "SHORT", "SMALL_HIDE", "RUBBLE"], "examine_action": "rubble"}, {"type": "furniture", "id": "f_reactor_meltdown", "name": "radioactive slag", "description": "A pile of melted slag from a destroyed nuclear reactor.  What are you doing looking at it?  Get the hell out of here, you nutcase!", "looks_like": "f_wreckage", "symbol": "#", "color": "light_green", "move_cost_mod": 6, "max_volume": "750 L", "required_str": -1, "emissions": ["emit_rad_cloud"], "flags": ["TRANSPARENT", "UNSTABLE", "ROUGH", "SHARP", "PLACE_ITEM", "MOUNTABLE", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "SMALL_HIDE", "SHORT", "RUBBLE"]}, {"type": "furniture", "id": "f_ash", "name": "pile of ash", "symbol": "#", "description": "A pile of ash, from wood or possibly corpses.", "color": "light_gray", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "NOCOLLIDE", "CONTAINER", "SEALED", "PLACE_ITEM", "RUBBLE", "SMALL_HIDE"], "examine_action": "rubble"}, {"type": "furniture", "id": "f_boulder_small", "name": "small boulder", "description": "A big rock.  Should be easy to roll around, but too heavy to carry with you.  It can be used as a primitive anvil, or broken into a bunch of smaller rocks.", "symbol": "o", "color": "light_gray", "move_cost_mod": 3, "coverage": 30, "required_str": 10, "crafting_pseudo_item": "boulder_anvil", "flags": ["TRANSPARENT", "MINEABLE", "UNSTABLE", "MOUNTABLE", "TINY"], "bash": {"str_min": 16, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [16, 32]}, {"item": "sharp_rock", "count": [0, 6]}]}}, {"type": "furniture", "id": "f_boulder_medium", "name": "medium boulder", "description": "A pretty big boulder, but still small enough to see and climb over.  It would be a struggle to move it.  It can be used as a primitive anvil, or broken into a bunch of smaller rocks.", "symbol": "0", "color": "light_gray", "move_cost_mod": 6, "coverage": 45, "required_str": 16, "crafting_pseudo_item": "boulder_anvil", "flags": ["NOITEM", "TRANSPARENT", "MINEABLE", "UNSTABLE", "MOUNTABLE", "SHORT"], "bash": {"str_min": 32, "str_max": 80, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [35, 50]}, {"item": "sharp_rock", "count": [3, 7]}]}}, {"type": "furniture", "id": "f_boulder_large", "name": "large boulder", "description": "A boulder that's taller than you.  Now how are you going to move this?", "symbol": "O", "color": "light_gray", "move_cost_mod": -1, "coverage": 65, "required_str": 32, "flags": ["NOITEM", "MINEABLE", "BLOCK_WIND"], "bash": {"str_min": 64, "str_max": 160, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [65, 85]}, {"item": "sharp_rock", "count": [5, 9]}, {"item": "material_limestone", "charges": [2, 5], "prob": 30}, {"item": "material_rocksalt", "count": [0, 1], "prob": 10}, {"item": "material_rhodonite", "count": [0, 1], "prob": 1}, {"item": "material_zincite", "count": [0, 5], "prob": 2}]}}, {"type": "furniture", "id": "f_street_light", "name": "street light", "looks_like": "f_utility_pole", "description": "A raised light source which used to illuminate streets and their surrounding area, but it's useless without electricity.", "symbol": "Y", "color": "light_gray", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "rotates_to": "PAVEMENT", "flags": ["TRANSPARENT", "NO_SELF_CONNECT"], "bash": {"str_min": 30, "str_max": 100, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 7]}, {"item": "scrap", "count": [3, 12]}, {"item": "amplifier", "count": [1, 3]}, {"item": "light_bulb", "count": [0, 3]}, {"item": "cable", "charges": [1, 2]}, {"item": "plastic_chunk", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_traffic_light", "name": "traffic light", "looks_like": "f_utility_pole", "description": "A signaling device positioned at road intersections and pedestrian crossings to control the flow of traffic, but it's useless without electricity.", "symbol": "P", "color": "white", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "rotates_to": "PAVEMENT", "flags": ["TRANSPARENT", "NO_SELF_CONNECT"], "bash": {"str_min": 30, "str_max": 100, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 5]}, {"item": "amplifier", "count": [1, 3]}, {"item": "light_bulb", "count": [1, 3]}, {"item": "plastic_chunk", "count": [1, 3]}, {"item": "cable", "charges": [1, 3]}, {"item": "scrap", "count": [3, 12]}]}}, {"type": "furniture", "id": "f_bike_rack", "name": "bike rack", "looks_like": "t_metal_railing", "description": "A loop of metal in the shape of an upside down U used to securely park bikes.", "symbol": "n", "color": "light_gray", "move_cost_mod": -1, "coverage": 10, "required_str": -1, "rotates_to": "PAVEMENT", "flags": ["TRANSPARENT", "THIN_OBSTACLE"], "deconstruct": {"items": [{"item": "pipe", "count": [2, 3]}]}, "bash": {"str_min": 6, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_flagpole", "name": "flagpole", "description": "A 6-meter tall metal flagpole.  You could hoist up a flag here.", "symbol": "F", "color": "light_gray", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "max_volume": "2 L", "bash": {"str_min": 30, "str_max": 100, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "steel_chunk", "count": [6, 15]}, {"item": "scrap", "count": [5, 20]}, {"item": "rope_6", "count": [1, 3]}]}}, {"type": "furniture", "id": "f_wooden_flagpole", "name": "flagpole", "description": "A 5-meter tall wooden flagpole.  You could hoist up a flag here.", "looks_like": "f_flagpole", "symbol": "F", "color": "brown", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "max_volume": "2 L", "bash": {"str_min": 15, "str_max": 50, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 6]}, {"item": "splinter", "count": [10, 20]}, {"item": "rope_6", "count": [0, 2]}, {"item": "string_6", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_utility_pole", "name": "utility pole", "description": "A long wooden post which used to support overhead power lines and other public utilities, but it doesn't work anymore.", "symbol": "i", "color": "brown", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "bash": {"str_min": 20, "str_max": 100, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 10]}, {"item": "splinter", "count": [10, 20]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "cable", "charges": [0, 1]}, {"item": "scrap", "count": [3, 5]}]}}, {"type": "furniture", "id": "f_scrap_bridge", "name": "scrap metal bridge", "description": "A simple bridge made of riveted sheet metal.", "move_cost_mod": 0, "symbol": "]", "color": "light_gray", "required_str": -1, "flags": ["TRANSPARENT", "NOCOLLIDE", "BRIDGE"], "deconstruct": {"items": [{"item": "scrap", "count": 10}, {"item": "sheet_metal", "count": 2}, {"item": "rebar", "count": 2}, {"item": "lc_wire", "count": 2}]}, "bash": {"str_min": 25, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "sheet_metal", "count": [0, 1]}, {"item": "rebar", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_room_divider", "name": "room divider", "description": "A tall piece of furniture used to divide rooms into separate areas.", "move_cost_mod": -1, "required_str": 8, "coverage": 80, "symbol": "#", "color": "light_gray", "looks_like": "f_curtain", "flags": ["THIN_OBSTACLE", "TRANSPARENT", "CLIMBABLE", "BLOCKSDOOR", "NOITEM", "PERMEABLE"], "examine_action": "chainfence", "deconstruct": {"items": [{"item": "pipe", "count": 2}, {"item": "scrap", "count": 4}, {"item": "plastic_chunk", "count": 8}]}, "bash": {"str_min": 8, "str_max": 30, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "pipe", "count": [0, 2]}, {"item": "scrap", "count": [1, 3]}, {"item": "plastic_chunk", "count": [1, 6]}]}}, {"type": "furniture", "id": "f_dirtmound_shallow", "name": "short mound of dirt", "symbol": "#", "color": "brown", "looks_like": "t_dirtmound", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped dirt, not easily traversable.  If examined more closely, it's quite favorable for planting seeds and the like.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "PLANTABLE"], "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_soil", "count": [170, 200]}]}}, {"type": "furniture", "id": "f_dirtmound_shallow_seed", "name": "short mound of dirt with seed", "symbol": "^", "color": "brown", "looks_like": "f_dirtmound_shallow_seed", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped dirt, not easily traversable.  Someone has planted a seed here.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_shallow", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [1, 2]}]}, "plant_data": {"transform": "f_dirtmound_shallow_seedling", "base": "f_dirtmound_shallow"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_shallow_seedling", "name": "short mound of dirt with seedling", "symbol": "^", "color": "green", "looks_like": "f_dirtmound_shallow_seed", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped dirt, not easily traversable.  There is a seed here, that has just begun to sprout its first roots.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_SEEDLING"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_shallow", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [1, 2]}]}, "plant_data": {"transform": "f_dirtmound_shallow_mature", "base": "f_dirtmound_shallow"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_shallow_mature", "name": "short mound of dirt with mature plant", "symbol": "#", "color": "green", "looks_like": "f_dirtmound_shallow_seed", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped dirt, not easily traversable.  There is a matured plant here, that should be ready for harvest before long.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_MATURE", "SMALL_HIDE"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_shallow", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 16]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_dirtmound_shallow_harvest", "base": "f_dirtmound_shallow"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_shallow_harvest", "name": "short mound of dirt with harvestable plant", "symbol": "#", "color": "light_green", "looks_like": "f_dirtmound_shallow_seed", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped dirt, not easily traversable.  There is a fully grown plant, and will need closer examination to harvest.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_HARVEST", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_shallow", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 1]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_plant_unharvested_overgrown", "base": "f_dirtmound_shallow"}, "examine_action": "harvest_plant_ex"}, {"type": "furniture", "id": "f_dirtmound_pile", "name": "mound of dirt", "symbol": "#", "color": "brown", "looks_like": "t_dirtmound", "move_cost_mod": 5, "coverage": 60, "description": "An area of dirt heaped up to your shoulders, not easily traversable.  If examined more closely, it's quite favorable for planting seeds and the like.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "PLANTABLE"], "deconstruct": {"items": [{"item": "material_soil", "count": 200}], "furn_set": "f_dirtmound_shallow"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_soil", "count": [170, 200]}], "furn_set": "f_dirtmound_shallow"}}, {"type": "furniture", "id": "f_dirtmound_pile_seed", "name": "mound of dirt with seed", "symbol": "^", "color": "brown", "looks_like": "f_dirtmound_pile_seed", "move_cost_mod": 5, "coverage": 40, "description": "An area of dirt heaped up to your shoulders, not easily traversable.  Someone has planted a seed here.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_pile", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [1, 2]}]}, "plant_data": {"transform": "f_dirtmound_pile_seedling", "base": "f_dirtmound_pile"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_pile_seedling", "name": "mound of dirt with seedling", "symbol": "^", "color": "green", "looks_like": "f_dirtmound_pile_seed", "move_cost_mod": 5, "coverage": 40, "description": "An area of dirt heaped up to your shoulders, not easily traversable.  There is a seed here, that has just begun to sprout its first roots.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_SEEDLING"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_pile", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [1, 2]}]}, "plant_data": {"transform": "f_dirtmound_pile_mature", "base": "f_dirtmound_pile"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_pile_mature", "name": "mound of dirt with mature plant", "symbol": "#", "color": "green", "looks_like": "f_dirtmound_pile_seed", "move_cost_mod": 5, "coverage": 40, "description": "An area of dirt heaped up to your shoulders, not easily traversable.  There is a matured plant here, that should be ready for harvest before long.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_MATURE", "SMALL_HIDE"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_pile", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 16]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_dirtmound_pile_harvest", "base": "f_dirtmound_pile"}, "//": "Needs planting support"}, {"type": "furniture", "id": "f_dirtmound_pile_harvest", "name": "mound of dirt with harvestable plant", "symbol": "#", "color": "light_green", "looks_like": "f_dirtmound_pile_seed", "move_cost_mod": 5, "coverage": 40, "description": "An area of dirt heaped up to your shoulders, not easily traversable.  There is a fully grown plant, and will need closer examination to harvest.", "required_str": -1, "flags": ["PLANT", "TRANSPARENT", "EASY_DECONSTRUCT", "NOITEM", "THIN_OBSTACLE", "PERMEABLE", "TINY", "DONT_REMOVE ROTTEN", "SEALED", "CONTAINER", "GROWTH_HARVEST", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "material_soil", "count": 200}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_dirtmound_pile", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 1]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_plant_unharvested_overgrown", "base": "f_dirtmound_pile"}, "examine_action": "harvest_plant_ex"}, {"type": "furniture", "id": "f_dirtmound_tall", "name": "tall mound of dirt", "symbol": "#", "color": "brown", "looks_like": "t_dirtmound", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of dirt heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "material_soil", "count": 200}], "furn_set": "f_dirtmound_pile"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_soil", "count": [170, 200]}], "furn_set": "f_dirtmound_pile"}}, {"type": "furniture", "id": "f_sand_mound_short", "name": "short mound of sand", "symbol": "#", "color": "yellow", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped sand, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "connect_groups": "SANDPILE", "connects_to": "SANDPILE", "deconstruct": {"items": [{"item": "material_sand", "count": 4000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_sand", "count": [3400, 4000]}]}}, {"type": "furniture", "id": "f_sand_mound_mid", "name": "mound of sand", "symbol": "#", "color": "yellow", "move_cost_mod": 5, "coverage": 60, "description": "An area of sand heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "connect_groups": "SANDPILE", "connects_to": "SANDPILE", "deconstruct": {"items": [{"item": "material_sand", "count": 4000}], "furn_set": "f_sand_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_sand", "count": [3400, 4000]}], "furn_set": "f_sand_mound_short"}}, {"type": "furniture", "id": "f_sand_mound_tall", "name": "tall mound of sand", "symbol": "#", "color": "yellow", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of sand heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "connect_groups": "SANDPILE", "connects_to": "SANDPILE", "deconstruct": {"items": [{"item": "material_sand", "count": 4000}], "furn_set": "f_sand_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_sand", "count": [3400, 4000]}], "furn_set": "f_sand_mound_mid"}}, {"type": "furniture", "id": "f_gravel_mound_short", "name": "short mound of gravel", "symbol": "#", "color": "light_gray", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped gravel, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "connect_groups": "GRAVELPILE", "connects_to": "GRAVELPILE", "deconstruct": {"items": [{"item": "material_gravel", "count": 4000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "material_gravel", "count": [3400, 4000]}]}}, {"type": "furniture", "id": "f_gravel_mound_mid", "name": "mound of gravel", "symbol": "#", "color": "light_gray", "move_cost_mod": 5, "coverage": 60, "description": "An area of gravel heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "connect_groups": "GRAVELPILE", "connects_to": "GRAVELPILE", "deconstruct": {"items": [{"item": "material_gravel", "count": 4000}], "furn_set": "f_gravel_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "material_gravel", "count": [3400, 4000]}], "furn_set": "f_gravel_mound_short"}}, {"type": "furniture", "id": "f_gravel_mound_tall", "name": "tall mound of gravel", "symbol": "#", "color": "light_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of gravel heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "connect_groups": "GRAVELPILE", "connects_to": "GRAVELPILE", "deconstruct": {"items": [{"item": "material_gravel", "count": 4000}], "furn_set": "f_gravel_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "material_gravel", "count": [3400, 4000]}], "furn_set": "f_gravel_mound_mid"}}, {"type": "furniture", "id": "f_pebble_mound_short", "name": "short mound of pebbles", "symbol": "#", "color": "light_gray", "move_cost_mod": 3, "coverage": 40, "description": "An area of heaped pebbles, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "pebble", "count": 4000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "pebble", "count": [3400, 4000]}]}}, {"type": "furniture", "id": "f_pebble_mound_mid", "name": "mound of pebbles", "symbol": "#", "color": "light_gray", "move_cost_mod": 5, "coverage": 60, "description": "An area of pebbles heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "pebble", "count": 4000}], "furn_set": "f_pebble_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "pebble", "count": [3400, 4000]}], "furn_set": "f_pebble_mound_short"}}, {"type": "furniture", "id": "f_pebble_mound_tall", "name": "tall mound of pebbles", "symbol": "#", "color": "light_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of pebbles heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "pebble", "count": 4000}], "furn_set": "f_pebble_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "pebble", "count": [3400, 4000]}], "furn_set": "f_pebble_mound_mid"}}, {"type": "furniture", "id": "f_clay_mound_short", "name": "short mound of clay", "symbol": "#", "color": "brown", "move_cost_mod": 4, "coverage": 40, "description": "An area of heaped clay, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "clay_lump", "count": 4000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "clay_lump", "count": [3400, 4000]}]}}, {"type": "furniture", "id": "f_clay_mound_mid", "name": "mound of clay", "symbol": "#", "color": "brown", "move_cost_mod": 5, "coverage": 60, "description": "An area of clay heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "clay_lump", "count": 4000}], "furn_set": "f_clay_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "clay_lump", "count": [3400, 4000]}], "furn_set": "f_clay_mound_short"}}, {"type": "furniture", "id": "f_clay_mound_tall", "name": "tall mound of clay", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of clay heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "clay_lump", "count": 4000}], "furn_set": "f_clay_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "clay_lump", "count": [3400, 4000]}], "furn_set": "f_clay_mound_mid"}}, {"type": "furniture", "id": "f_glass_mound_short", "name": "short mound of broken glass", "symbol": "#", "color": "light_cyan", "move_cost_mod": 4, "coverage": 40, "description": "An area of heaped glass shards, not safely traversable.  Just looking at it feel like you'll get tetanus.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ROUGH", "SHARP"], "deconstruct": {"items": [{"item": "glass_shard", "count": 4096}]}, "bash": {"str_min": 10, "str_max": 24, "sound": "whack!", "sound_fail": "glass breaking!", "items": [{"item": "glass_shard", "count": [3491, 4096]}]}}, {"type": "furniture", "id": "f_glass_mound_mid", "name": "mound of broken glass", "symbol": "#", "color": "light_cyan", "move_cost_mod": 5, "coverage": 60, "description": "An area of broken glass shards heaped up to your shoulders, not safely traversable.  Just looking at it feel like you'll get tetanus.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ROUGH", "SHARP"], "deconstruct": {"items": [{"item": "glass_shard", "count": 4096}], "furn_set": "f_glass_mound_short"}, "bash": {"str_min": 10, "str_max": 24, "sound": "whack!", "sound_fail": "glass breaking!", "items": [{"item": "glass_shard", "count": [3481, 4096]}], "furn_set": "f_glass_mound_short"}}, {"type": "furniture", "id": "f_glass_mound_tall", "name": "tall mound of broken glass", "symbol": "#", "color": "light_cyan", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of broken glass heaped well above your head, unsafe to climb.  Just looking at it feel like you'll get tetanus.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE", "ROUGH", "SHARP"], "deconstruct": {"items": [{"item": "glass_shard", "count": 4096}], "furn_set": "f_glass_mound_mid"}, "bash": {"str_min": 10, "str_max": 24, "sound": "whack!", "sound_fail": "glass breaking!", "items": [{"item": "glass_shard", "count": [3491, 4096]}], "furn_set": "f_glass_mound_mid"}}, {"type": "furniture", "id": "f_rock_mound_short", "name": "short mound of rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": 4, "coverage": 40, "description": "An area of heaped rocks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rock", "count": 4000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock", "count": [3400, 4000]}]}}, {"type": "furniture", "id": "f_rock_mound_mid", "name": "mound of rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": 5, "coverage": 60, "description": "An area of rocks heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rock", "count": 4000}], "furn_set": "f_rock_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock", "count": [3400, 4000]}], "furn_set": "f_rock_mound_short"}}, {"type": "furniture", "id": "f_rock_mound_tall", "name": "tall mound of rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of rocks heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "rock", "count": 4000}], "furn_set": "f_rock_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock", "count": [3400, 4000]}], "furn_set": "f_rock_mound_mid"}}, {"type": "furniture", "id": "f_rock_large_mound_short", "name": "short mound of large rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": 4, "coverage": 40, "description": "An area of heaped large rocks, not easily traversable", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rock_large", "count": 166}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock_large", "count": [141, 166]}]}}, {"type": "furniture", "id": "f_rock_large_mound_mid", "name": "mound of large rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": 5, "coverage": 60, "description": "An area of large rocks heaped up to your shoulders, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rock_large", "count": 166}], "furn_set": "f_rock_large_mound_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock_large", "count": [141, 166]}], "furn_set": "f_rock_large_mound_short"}}, {"type": "furniture", "id": "f_rock_large_mound_tall", "name": "tall mound of large rocks", "symbol": "#", "color": "dark_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "An area of large rocks heaped well above your head, difficult to climb over.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "rock_large", "count": 166}], "furn_set": "f_rock_large_mound_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "rock_large", "count": [141, 166]}], "furn_set": "f_rock_large_mound_mid"}}, {"type": "furniture", "id": "f_log_stack_short", "name": "short stack of logs", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of logs, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "log", "count": 20}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "log", "count": [17, 20]}]}}, {"type": "furniture", "id": "f_log_stack_mid", "name": "stack of logs", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of logs as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "log", "count": 20}], "furn_set": "f_log_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "log", "count": [17, 20]}], "furn_set": "f_log_stack_short"}}, {"type": "furniture", "id": "f_log_stack_tall", "name": "tall stack of logs", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Logs stacked well above your head.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "log", "count": 20}], "furn_set": "f_log_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "log", "count": [17, 20]}], "furn_set": "f_log_stack_mid"}}, {"type": "furniture", "id": "f_plank_stack_short", "name": "short stack of planks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of planks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "2x4", "count": 242}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "2x4", "count": [205, 242]}]}}, {"type": "furniture", "id": "f_plank_stack_mid", "name": "stack of planks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of planks as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "2x4", "count": 242}], "furn_set": "f_plank_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "2x4", "count": [205, 242]}], "furn_set": "f_plank_stack_short"}}, {"type": "furniture", "id": "f_plank_stack_tall", "name": "tall stack of planks", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Planks stacked well above your head.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "2x4", "count": 242}], "furn_set": "f_plank_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "2x4", "count": [205, 242]}], "furn_set": "f_plank_stack_mid"}}, {"type": "furniture", "id": "f_stick_stack_short", "name": "short stack of sticks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of sticks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick", "count": 400}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick", "count": [340, 400]}]}}, {"type": "furniture", "id": "f_stick_stack_mid", "name": "stack of sticks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of sticks as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick", "count": 400}], "furn_set": "f_stick_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick", "count": [340, 400]}], "furn_set": "f_stick_stack_short"}}, {"type": "furniture", "id": "f_stick_stack_tall", "name": "tall stack of sticks", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Sticks stacked well above your head.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick", "count": 400}], "furn_set": "f_stick_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick", "count": [340, 400]}], "furn_set": "f_stick_stack_mid"}}, {"type": "furniture", "id": "f_stick_long_stack_short", "name": "short stack of long sticks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of long sticks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick_long", "count": 200}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick", "count": [170, 200]}]}}, {"type": "furniture", "id": "f_stick_long_stack_mid", "name": "stack of long sticks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of long sticks as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick_long", "count": 200}], "furn_set": "f_stick_long_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick_long", "count": [170, 200]}], "furn_set": "f_stick_long_stack_short"}}, {"type": "furniture", "id": "f_stick_long_stack_tall", "name": "tall stack of long sticks", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Long sticks stacked well above your head.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE", "ORGANIC", "FLAMMABLE_ASH"], "deconstruct": {"items": [{"item": "stick_long", "count": 200}], "furn_set": "f_stick_long_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crunch!", "items": [{"item": "stick_long", "count": [170, 200]}], "furn_set": "f_stick_long_stack_mid"}}, {"type": "furniture", "id": "f_brick_stack_short", "name": "short stack of bricks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of bricks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "brick", "count": 2000}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "brick", "count": [1700, 2000]}]}}, {"type": "furniture", "id": "f_brick_stack_mid", "name": "stack of bricks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of bricks as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "brick", "count": 2000}], "furn_set": "f_brick_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "brick", "count": [1700, 2000]}], "furn_set": "f_brick_stack_short"}}, {"type": "furniture", "id": "f_brick_stack_tall", "name": "tall stack of bricks", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Bricks stacked well above your head.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "brick", "count": 2000}], "furn_set": "f_brick_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "whump!", "sound_fail": "crash!", "items": [{"item": "brick", "count": [1700, 2000]}], "furn_set": "f_brick_stack_mid"}}, {"type": "furniture", "id": "f_adobe_stack_short", "name": "short stack of adobe bricks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of adobe bricks, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "adobe_brick", "count": 1333}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "bash!", "sound_fail": "crash!", "items": [{"item": "adobe_brick", "count": [1033, 1333]}]}}, {"type": "furniture", "id": "f_adobe_stack_mid", "name": "stack of adobe bricks", "symbol": "#", "color": "brown", "move_cost_mod": 2, "coverage": 60, "description": "A stack of adobe bricks as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "adobe_brick", "count": 1333}], "furn_set": "f_adobe_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "bash!", "sound_fail": "crash!", "items": [{"item": "adobe_brick", "count": [1033, 1333]}], "furn_set": "f_adobe_stack_short"}}, {"type": "furniture", "id": "f_adobe_stack_tall", "name": "tall stack of adobe bricks", "symbol": "#", "color": "brown", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Adobe bricks stacked well above your head.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "adobe_brick", "count": 1333}], "furn_set": "f_adobe_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "bash!", "sound_fail": "crash!", "items": [{"item": "adobe_brick", "count": [1033, 1333]}], "furn_set": "f_adobe_stack_mid"}}, {"type": "furniture", "id": "f_rebar_stack_short", "name": "short stack of rebar", "symbol": "#", "color": "light_gray", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of rebar, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rebar", "count": 6289}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "rebar", "count": [5345, 6289]}]}}, {"type": "furniture", "id": "f_rebar_stack_mid", "name": "stack of rebar", "symbol": "#", "color": "light_gray", "move_cost_mod": 2, "coverage": 60, "description": "A stack of rebar as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "rebar", "count": 6289}], "furn_set": "f_rebar_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "rebar", "count": [5345, 6289]}], "furn_set": "f_rebar_stack_short"}}, {"type": "furniture", "id": "f_rebar_stack_tall", "name": "tall stack of rebar", "symbol": "#", "color": "light_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Rebar stacked well above your head.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "rebar", "count": 6289}], "furn_set": "f_rebar_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "rebar", "count": [5345, 6289]}], "furn_set": "f_rebar_stack_mid"}}, {"type": "furniture", "id": "f_pipe_stack_short", "name": "short stack of pipes", "symbol": "#", "color": "light_gray", "move_cost_mod": 2, "coverage": 40, "description": "A short stack of pipes, not easily traversable.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "pipe", "count": 2702}]}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "pipe", "count": [2296, 2702]}]}}, {"type": "furniture", "id": "f_pipe_stack_mid", "name": "stack of pipes", "symbol": "#", "color": "light_gray", "move_cost_mod": 2, "coverage": 60, "description": "A stack of pipes as high as your shoulders.", "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "THIN_OBSTACLE", "PERMEABLE"], "deconstruct": {"items": [{"item": "pipe", "count": 2702}], "furn_set": "f_pipe_stack_short"}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "pipe", "count": [2296, 2702]}], "furn_set": "f_pipe_stack_short"}}, {"type": "furniture", "id": "f_pipe_stack_tall", "name": "tall stack of pipes", "symbol": "#", "color": "light_gray", "move_cost_mod": -1, "examine_action": "chainfence", "coverage": 100, "description": "Pipes stacked well above your head.", "required_str": -1, "flags": ["NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "PERMEABLE", "CLIMBABLE"], "deconstruct": {"items": [{"item": "pipe", "count": 2702}], "furn_set": "f_pipe_stack_mid"}, "bash": {"str_min": 50, "str_max": 100, "sound": "clang!", "sound_fail": "metal screeching!", "items": [{"item": "pipe", "count": [2296, 2702]}], "furn_set": "f_pipe_stack_mid"}}]