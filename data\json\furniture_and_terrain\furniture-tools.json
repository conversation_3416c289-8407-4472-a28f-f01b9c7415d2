[{"type": "furniture", "id": "f_console_broken", "name": "broken console", "looks_like": "t_console_broken", "description": "A standalone computer terminal.  It doesn't seem to be working.  It's the broken screen and shattered circuit boards telling you that.", "symbol": "6", "color": "light_gray", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "flags": ["TRANSPARENT", "NOITEM", "INDOORS", "SHORT", "PERMEABLE"], "connect_groups": ["COUNTER", "INDOORFLOOR"], "connects_to": "COUNTER", "deconstruct": {"skill": {"skill": "electronics", "multiplier": 0.5, "min": 1, "max": 4}, "furn_set": "f_counter", "items": [{"item": "processor", "count": [1, 2]}, {"item": "RAM", "count": [4, 8]}, {"item": "cable", "charges": [4, 6]}, {"item": "large_lcd_screen", "count": 1}, {"item": "e_scrap", "count": [10, 16]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [2, 4]}, {"item": "amplifier", "count": [2, 4]}, {"item": "plastic_chunk", "count": [10, 12]}, {"item": "scrap", "count": [6, 8]}]}, "bash": {"str_min": 16, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "furn_set": "f_counter", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "large_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "furniture", "id": "f_console", "name": "computer console", "looks_like": "t_console", "description": "A standalone computer terminal, the screen glowing with dim artificial light.  If examined closely, you can read what's on the screen and perform any allowed functions.  It might even be possible to hack it, given the skills.", "symbol": "6", "color": "blue", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "light_emitted": 10, "flags": ["TRANSPARENT", "CONSOLE", "NOITEM", "INDOORS", "SHORT", "PERMEABLE"], "connect_groups": ["COUNTER", "INDOORFLOOR"], "connects_to": "COUNTER", "deconstruct": {"skill": {"skill": "electronics", "min": 1, "max": 8}, "furn_set": "f_counter", "items": [{"item": "processor", "count": [1, 2]}, {"item": "RAM", "count": [4, 8]}, {"item": "cable", "charges": [4, 6]}, {"item": "large_lcd_screen", "count": 1}, {"item": "e_scrap", "count": [10, 16]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [2, 4]}, {"item": "amplifier", "count": [2, 4]}, {"item": "plastic_chunk", "count": [10, 12]}, {"item": "scrap", "count": [6, 8]}]}, "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "furn_set": "f_console_broken", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "large_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "furniture", "id": "f_console_broken_table", "name": "broken console", "looks_like": "t_console_broken", "description": "A standalone computer terminal.  It doesn't seem to be working.  It's the broken screen and shattered circuit boards telling you that.", "symbol": "6", "color": "light_gray", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "NOITEM", "INDOORS", "SHORT", "PERMEABLE"], "deconstruct": {"skill": {"skill": "electronics", "min": 1, "max": 8}, "furn_set": "f_table", "items": [{"item": "processor", "count": [1, 2]}, {"item": "RAM", "count": [4, 8]}, {"item": "cable", "charges": [4, 6]}, {"item": "large_lcd_screen", "count": 1}, {"item": "e_scrap", "count": [10, 16]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [2, 4]}, {"item": "amplifier", "count": [2, 4]}, {"item": "plastic_chunk", "count": [10, 12]}, {"item": "scrap", "count": [6, 8]}]}, "bash": {"str_min": 16, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "furn_set": "f_table", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "large_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "furniture", "id": "f_forge", "name": "forge", "looks_like": "f_fireplace", "description": "A metalworking station typically used in combination with an anvil.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "fake_forge", "flags": ["TRANSPARENT", "SEALED", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "AMMOTYPE_RELOAD"], "deconstruct": {"items": [{"item": "char_forge", "count": 1}]}, "examine_action": "reload_furniture", "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "char_forge", "count": 1}]}}, {"type": "furniture", "id": "f_forge_artisan_0", "name": "<PERSON>'s forge", "looks_like": "f_fireplace", "description": "A metalworking station typically used in combination with an anvil.  You can see pipes running into the walls.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "fake_forge", "flags": ["TRANSPARENT", "SEALED", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "AMMOTYPE_RELOAD"], "deconstruct": {"items": [{"item": "char_forge", "count": 1}]}, "examine_action": "reload_furniture", "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "char_forge", "count": 1}]}}, {"type": "furniture", "id": "f_forge_artisan_1", "name": "<PERSON>'s forge", "looks_like": "f_fireplace", "description": "A metalworking station typically used in combination with an anvil.  You can see pipes running into the walls.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "fake_forge", "flags": ["TRANSPARENT", "SEALED", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "AMMOTYPE_RELOAD"], "deconstruct": {"items": [{"item": "char_forge", "count": 1}]}, "examine_action": "reload_furniture", "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "char_forge", "count": 1}]}}, {"type": "furniture", "id": "f_anvil", "name": "anvil", "looks_like": "f_boulder", "description": "A heavy block of oddly-shaped metal with a pritchel hole and hardy hole.  It's used for metalworking.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 30, "required_str": 10, "crafting_pseudo_item": "fake_anvil", "deconstruct": {"items": [{"item": "anvil", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "anvil", "count": 1}]}}, {"type": "furniture", "id": "f_anvil_bronze", "name": "bronze anvil", "looks_like": "f_anvil", "description": "A heavy block of oddly-shaped bronze with a pritchel hole and hardy hole.  It's used for metalworking.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 30, "required_str": 10, "crafting_pseudo_item": "fake_anvil", "deconstruct": {"items": [{"item": "anvil_bronze", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "anvil_bronze", "count": 1}]}}, {"type": "furniture", "id": "f_anvil_heavy", "name": "heavy anvil", "looks_like": "f_anvil", "description": "An enormously heavy block of oddly-shaped metal with all the accoutrements a blacksmith could ask for.  It's used for heavy-duty metalworking.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 30, "required_str": 20, "crafting_pseudo_item": "fake_anvil_heavy", "deconstruct": {"items": [{"item": "anvil_heavy", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "anvil_heavy", "count": 1}]}}, {"type": "furniture", "id": "f_anvil_crude", "name": "small anvil", "looks_like": "f_anvil", "description": "A small, handcrafted anvil.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 30, "required_str": 10, "crafting_pseudo_item": "fake_anvil", "deconstruct": {"items": [{"item": "anvil_crude", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "anvil_crude", "count": 1}]}}, {"type": "furniture", "id": "f_still", "name": "still", "looks_like": "f_standing_tank", "description": "An essential component for brewing and chemistry that allows for refining liquid mixtures.", "symbol": "^", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "still", "deconstruct": {"items": [{"item": "still", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "still", "count": 1}]}}, {"type": "furniture", "id": "f_screw_press", "name": "screw press", "description": "A large screw press for manually pressing oil out of fruit or seed paste.  The paste is placed in wetted bags and pressed down by the force of the screw mechanism.", "symbol": "T", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "screw_press", "deconstruct": {"items": [{"item": "screw_press_tool", "count": 1}]}, "flags": ["TRANSPARENT", "NOITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 4, "str_max": 8, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "wood_beam", "count": [0, 2]}, {"item": "2x4", "count": [2, 6]}, {"item": "splinter", "count": [5, 20]}]}}, {"type": "furniture", "id": "f_kiln_empty", "name": "charcoal kiln", "looks_like": "f_fireplace", "description": "A primitive rock kiln designed to burn wood and organic material into charcoal in the absence of oxygen.  It isn't especially efficient compared to a modern metal variant.", "symbol": "U", "color": "brown", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "examine_action": "kiln_empty", "max_volume": "250 L", "crafting_pseudo_item": "fake_char_kiln", "flags": ["CONTAINER", "FIRE_CONTAINER", "PLACE_ITEM", "EASY_DECONSTRUCT", "MINEABLE"], "deconstruct": {"items": [{"item": "rock", "count": [35, 40]}]}, "bash": {"str_min": 25, "str_max": 180, "sound": "crash!", "sound_fail": "whump!", "items": [{"item": "rock", "count": [15, 30]}]}}, {"type": "furniture", "id": "f_kiln_full", "name": "filled charcoal kiln", "looks_like": "f_kiln_empty", "description": "A primitive rock kiln designed to burn wood and organic material into charcoal in the absence of oxygen.  It isn't especially efficient compared to a modern metal variant.", "symbol": "U", "color": "brown_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "examine_action": "kiln_full", "flags": ["NOITEM", "SEALED", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM", "EASY_DECONSTRUCT", "MINEABLE"], "deconstruct": {"items": [{"item": "rock", "count": [30, 30]}]}, "bash": {"str_min": 25, "str_max": 180, "sound": "crash!", "sound_fail": "whump!", "items": [{"item": "rock", "count": [15, 30]}]}}, {"type": "furniture", "id": "f_kiln_metal_empty", "name": "metal charcoal kiln", "looks_like": "f_kiln_empty", "description": "A metal kiln designed to burn wood and organic material into charcoal in the absence of oxygen.  Much more efficient than its archaic rock counterpart.", "symbol": "U", "color": "blue", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "examine_action": "kiln_empty", "max_volume": "250 L", "crafting_pseudo_item": "fake_char_kiln", "flags": ["CONTAINER", "FIRE_CONTAINER", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "metal_tank", "count": [1, 4]}, {"item": "pipe", "count": [2, 4]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "pipe", "count": [0, 4]}]}}, {"type": "furniture", "id": "f_kiln_metal_full", "name": "filled metal charcoal kiln", "looks_like": "f_kiln_metal_empty", "description": "A metal kiln designed to burn wood and organic material into charcoal in the absence of oxygen.  Much more efficient than its archaic rock counterpart.", "symbol": "U", "color": "blue_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "examine_action": "kiln_full", "flags": ["NOITEM", "SEALED", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "metal_tank", "count": [1, 4]}, {"item": "pipe", "count": [2, 4]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "pipe", "count": [0, 4]}]}}, {"type": "furniture", "id": "f_arcfurnace_empty", "name": "arc furnace", "looks_like": "f_arc_furnace", "description": "An arc furnace designed to burn a powdery mix of coke and limestone to create calcium carbide.", "symbol": "U", "color": "blue", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "max_volume": "200 L", "flags": ["CONTAINER", "FIRE_CONTAINER", "PLACE_ITEM"], "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "arc_furnace"}, "deconstruct": {"items": [{"item": "metal_tank", "count": [1, 4]}, {"item": "pipe", "count": [2, 4]}]}, "bash": {"str_min": 18, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "pipe", "count": [0, 4]}]}, "crafting_pseudo_item": "fake_arc_furnace"}, {"type": "furniture", "id": "f_arcfurnace_full", "name": "filled arc furnace", "looks_like": "f_arcfurnace_empty", "description": "An arc furnace designed to burn a powdery mix of coke and limestone to create calcium carbide.", "symbol": "U", "color": "blue_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "examine_action": "arcfurnace_full", "flags": ["NOITEM", "SEALED", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "metal_tank", "count": [1, 4]}, {"item": "pipe", "count": [2, 4]}]}, "bash": {"str_min": 18, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "pipe", "count": [0, 4]}]}}, {"type": "furniture", "id": "f_smoking_rack", "name": "smoking rack", "looks_like": "f_rack", "description": "A wooden rack designed to smoke food for better preservation and taste.  Works as a charcoal smoker in crafting recipes.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": -1, "flags": ["TRANSPARENT", "SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE"], "crafting_pseudo_item": "fake_char_smoker", "examine_action": "smoker_options", "deconstruct": {"items": [{"item": "rock", "count": 8}, {"item": "stick", "count": [16, 16]}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "rock", "count": 8}, {"item": "stick", "count": [8, 12]}]}}, {"type": "furniture", "id": "f_spinwheel", "name": "spinning wheel", "looks_like": "f_table", "description": "A large wooden device with a mechanical wheel used to combine fibers together into a stronger material.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": 11, "coverage": 40, "flags": ["TRANSPARENT", "NOITEM"], "crafting_pseudo_item": "spinwheelitem", "deconstruct": {"items": [{"item": "string_36", "count": 4}, {"item": "2x4", "count": 20}, {"item": "nail", "charges": 80}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "string_36", "count": [2, 4]}, {"item": "2x4", "count": [10, 20]}, {"item": "nail", "charges": [55, 80]}]}}, {"type": "furniture", "id": "f_spinwheel_cordage", "name": "spinning wheel", "looks_like": "f_table", "description": "A large wooden device with a mechanical wheel used to combine fibers together into a stronger material.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": 11, "coverage": 40, "flags": ["TRANSPARENT", "NOITEM"], "crafting_pseudo_item": "spinwheelitem", "deconstruct": {"items": [{"item": "cordage_36", "count": 4}, {"item": "2x4", "count": 20}, {"item": "nail", "charges": 80}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "cordage_36", "count": [2, 4]}, {"item": "2x4", "count": [10, 20]}, {"item": "nail", "charges": [55, 80]}]}}, {"type": "furniture", "id": "f_spinwheel_leather", "name": "spinning wheel", "looks_like": "f_table", "description": "A large wooden device with a mechanical wheel used to combine fibers together into a stronger material.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": 11, "coverage": 40, "flags": ["TRANSPARENT", "NOITEM"], "crafting_pseudo_item": "spinwheelitem", "deconstruct": {"items": [{"item": "cordage_36_leather", "count": 4}, {"item": "2x4", "count": 20}, {"item": "nail", "charges": 80}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "cordage_36_leather", "count": [2, 4]}, {"item": "2x4", "count": [10, 20]}, {"item": "nail", "charges": [55, 80]}]}}, {"type": "furniture", "id": "f_scutch_breaker", "name": "stationary scutcher", "looks_like": "f_rack", "description": "A bulky, simple-hinged wooden device to break up the hard parts of fiber plants, with nail combs on the back for separating them out.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": 11, "coverage": 40, "flags": ["TRANSPARENT", "NOITEM"], "crafting_pseudo_item": "scutcheritem", "deconstruct": {"items": [{"item": "2x4", "count": 8}, {"item": "nail", "charges": 20}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [10, 20]}]}}, {"type": "furniture", "id": "f_smoking_rack_active", "name": "active smoking rack", "looks_like": "f_smoking_rack", "description": "A wooden rack designed to smoke food for better preservation and taste.  It is lit and smoking.", "symbol": "=", "bgcolor": "brown", "move_cost_mod": 2, "required_str": -1, "flags": ["TRANSPARENT", "SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE"], "crafting_pseudo_item": "fake_char_smoker", "examine_action": "smoker_options", "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "rock", "count": 8}, {"item": "stick", "count": [8, 12]}]}}, {"type": "furniture", "id": "f_metal_smoking_rack", "name": "metal smoking rack", "looks_like": "f_smoking_rack", "description": "A metal rack designed to smoke food for better preservation and taste.  Works as a charcoal smoker in crafting recipes.", "symbol": "=", "bgcolor": "light_gray", "move_cost_mod": 2, "required_str": -1, "deployed_item": "metal_smoking_rack", "flags": ["TRANSPARENT", "SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE"], "crafting_pseudo_item": "fake_char_smoker", "examine_action": "smoker_options", "deconstruct": {"items": [{"item": "metal_smoking_rack", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": 4}, {"item": "pipe", "count": [3, 5]}]}}, {"type": "furniture", "id": "f_metal_smoking_rack_active", "name": "active metal smoking rack", "looks_like": "f_metal_smoking_rack", "description": "A metal rack designed to smoke food for better preservation and taste.", "symbol": "=", "bgcolor": "light_gray", "move_cost_mod": 2, "required_str": -1, "deployed_item": "metal_smoking_rack", "flags": ["TRANSPARENT", "SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE"], "crafting_pseudo_item": "fake_char_smoker", "examine_action": "smoker_options", "deconstruct": {"items": [{"item": "metal_smoking_rack", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": 4}, {"item": "pipe", "count": [3, 5]}]}}, {"type": "furniture", "id": "f_forge_rock", "name": "rock forge", "looks_like": "f_fireplace", "description": "A metalworking station made of rock, typically used in combination with an anvil.  Works as a charcoal forge in crafting recipes.", "symbol": "U", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "fake_forge", "flags": ["SEALED", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "AMMOTYPE_RELOAD"], "deconstruct": {"items": [{"item": "rock", "count": 40}]}, "examine_action": "reload_furniture", "bash": {"str_min": 18, "str_max": 50, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "rock", "count": [20, 30]}]}}, {"type": "furniture", "id": "f_clay_kiln", "name": "clay kiln", "looks_like": "f_fireplace", "description": "A kiln designed to bake clay pottery and bricks.", "symbol": "U", "color": "light_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "crafting_pseudo_item": "fake_clay_kiln", "flags": ["SEALED", "CONTAINER", "NOITEM", "EASY_DECONSTRUCT", "MINEABLE", "AMMOTYPE_RELOAD"], "deconstruct": {"items": [{"item": "rock", "count": 40}]}, "examine_action": "reload_furniture", "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "rock", "count": [20, 30]}]}}, {"type": "furniture", "id": "f_ladder", "name": "wooden stepladder", "looks_like": "t_ladder_up", "description": "A short, foldable wooden ladder.  Can help you climb to a rooftop, or maybe slow something down.", "symbol": "H", "color": "brown", "move_cost_mod": 3, "required_str": 6, "flags": ["LADDER", "TRANSPARENT", "SEEN_FROM_ABOVE", "EXAMINE_FROM_ABOVE", "DIFFICULT_Z", "ALLOW_ON_OPEN_AIR"], "examine_action": "deployed_furniture", "deployed_item": "stepladder", "rotates_to": "WALL", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 10]}, {"item": "nail", "charges": [10, 30]}, {"item": "splinter", "count": [5, 10]}]}}, {"type": "furniture", "id": "f_aluminum_stepladder", "name": "aluminum stepladder", "description": "A short, foldable aluminum ladder.  Can help you climb to a rooftop, or maybe slow something down.", "symbol": "H", "color": "light_gray", "move_cost_mod": 3, "required_str": 6, "flags": ["LADDER", "TRANSPARENT", "SEEN_FROM_ABOVE", "EXAMINE_FROM_ABOVE", "DIFFICULT_Z", "ALLOW_ON_OPEN_AIR"], "examine_action": "deployed_furniture", "looks_like": "f_ladder", "deployed_item": "aluminum_stepladder", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "scrap_aluminum", "count": [15, 285]}]}}, {"type": "furniture", "id": "f_arc_furnace", "name": "industrial arc furnace", "looks_like": "f_machinery_heavy", "description": "Not the kind of furnace you'd heat your house with, this is a device for heating things to extreme temperatures as part of industrial fabrication processes.  Can be used in recipes instead of a forge, when supplied with power from a UPS.", "symbol": "0", "color": "white_red", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "flags": ["CONTAINER", "FIRE_CONTAINER", "PLACE_ITEM"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "steel_plate", "count": [1, 2]}]}, "deconstruct": {"items": [{"item": "cable", "charges": [4, 8]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "steel_plate", "count": [2, 4]}, {"item": "sheet_metal", "count": [4, 6]}, {"item": "element", "count": [10, 25]}, {"item": "scrap", "count": [12, 16]}]}, "crafting_pseudo_item": "fake_arc_furnace"}, {"type": "furniture", "id": "f_beverly_shear", "name": "<PERSON> shear", "looks_like": "f_machinery_light", "description": "A massive bench-mounted device with a short guillotine blade mounted to a long lever.  Designed for making straight or curved cuts through sheet metal up to 3 millimeters thick.", "//": "Based on the Beverly B-2 Throatless Shear, 10 gauge capacity", "symbol": "7", "color": "yellow_red", "move_cost_mod": -1, "coverage": 40, "required_str": 10, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "pipe", "count": [0, 1]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "steel_lump", "count": [1, 2]}, {"item": "scrap", "count": [2, 4]}]}, "deconstruct": {"items": [{"item": "beverly_shear", "count": 1}]}, "crafting_pseudo_item": "fake_beverly_shear"}, {"type": "furniture", "id": "f_drill_press", "name": "drill press", "looks_like": "f_machinery_light", "description": "A powerful drill mounted on a slide that lets it drop precisely down.  Useful in all kinds of projects from industrial fabrication to home woodworking.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_red", "move_cost_mod": -1, "coverage": 40, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "drill_press"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "drill_press", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_firefly_terrarium", "name": "firefly terrarium", "description": "While many survivors would have focused on trying to bring back electric lights, whoever lives here has apparently decided to make use of the changes wrought by the Cataclysm to provide light.  This vivarium contains soil, strange plants you don't recognize immediately, and most strikingly, a giant mutant firefly.", "symbol": "#", "color": "light_green", "move_cost_mod": 3, "coverage": 30, "required_str": 6, "light_emitted": 25, "flags": ["TRANSPARENT", "NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 20, "str_max": 30, "sound_fail": "smash!", "items": [{"item": "glass_shard", "count": [8, 12]}, {"item": "alien_fern", "count": [0, 3]}, {"item": "mutant_meat", "count": [2, 4]}, {"item": "material_soil", "count": [2, 10]}, {"item": "mutant_bug_hydrogen_sacs", "count": [0, 2]}, {"item": "egg_firefly", "count": [0, 1]}, {"item": "mutant_bug_organs", "count": [0, 5]}]}}, {"type": "furniture", "id": "f_tablesaw", "name": "table saw", "looks_like": "f_machinery_light", "description": "A rotating sawblade set into a large, flat table, for making straight measured cuts.  It's a key tool in any carpenter's arsenal.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_red", "move_cost_mod": 8, "coverage": 40, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "tablesaw"}, "flags": ["BLOCKSDOOR", "TRANSPARENT", "MOUNTABLE", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "tablesaw", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_mitresaw", "name": "mitre saw", "looks_like": "f_machinery_light", "description": "A circular sawblade on an arm that can slide and rotate in several directions, this is a staple tool for nearly any carpentry.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_cyan", "move_cost_mod": -1, "coverage": 40, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "mitresaw"}, "flags": ["BLOCKSDOOR", "TRANSPARENT", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "mitresaw", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_bandsaw", "name": "band saw", "looks_like": "f_machinery_light", "description": "A ribbonlike sawblade runs in a single direction in this tool, allowing precise cuts at almost any angle.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_cyan", "move_cost_mod": -1, "coverage": 40, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "bandsaw"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "bandsaw", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_router", "name": "router table", "looks_like": "f_machinery_light", "description": "This table has an inset router, a rotating motor with an exchangeable blade head for cutting specific profiles and grooves and stuff.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_green", "move_cost_mod": 8, "coverage": 35, "required_str": 14, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "router"}, "flags": ["BLOCKSDOOR", "TRANSPARENT", "MOUNTABLE", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "router", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_planer", "name": "planer", "looks_like": "f_machinery_light", "description": "A hefty tool that will take in a board and cut it smooth and flat to a specific width, particularly great if working with raw lumber stock, but also good just for shaving wood down to size.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_white", "move_cost_mod": -1, "coverage": 40, "required_str": 12, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "planer"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "planer", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_jointer", "name": "jointer", "looks_like": "f_machinery_light", "description": "A table-shaped tool with a rotating blade that will cut down, smooth out, and square off a board to make it very smooth and nice indeed.  You can take this down and plug it into your own power grid to use it.", "symbol": "7", "color": "yellow_magenta", "move_cost_mod": 8, "coverage": 40, "required_str": 14, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "jointer"}, "flags": ["BLOCKSDOOR", "TRANSPARENT", "MOUNTABLE", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "jointer", "count": 1}]}, "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_catalytic_cracking_reactor", "name": "disconnected catalytic cracking reactor", "looks_like": "f_machinery_light", "description": "A makeshift extraction system, or as makeshift as you can consider something that was purpose-built in a laboratory by a group of experts.  You can take this down and plug it into your own power grid to use it.", "symbol": "Y", "color": "light_cyan", "move_cost_mod": -1, "coverage": 55, "required_str": 16, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "catalytic_cracking_reactor"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "element", "count": [4, 8]}, {"item": "55gal_drum", "count": [3, 5]}, {"item": "pipe", "count": [2, 5]}, {"item": "thermometer", "count": [1, 4]}, {"item": "well_pump", "count": [0, 1]}, {"item": "laptop", "count": [0, 1]}, {"item": "pump_complex", "count": [0, 3]}, {"item": "cable", "count": [3, 8]}]}, "deconstruct": {"items": [{"item": "catalytic_cracking_reactor", "count": 1}]}}, {"type": "furniture", "id": "f_hydraulic_press", "name": "hydraulic press", "looks_like": "f_machinery_light", "description": "If you really want to squash something a lot, this would be exactly the right industrial tool for you.  You can take this down and plug it into your own power grid to use it.", "symbol": "9", "color": "black_red", "move_cost_mod": -1, "coverage": 55, "required_str": 16, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "hydraulic_press"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "steel_lump", "count": [1, 2]}, {"item": "steel_plate", "count": [1, 2]}]}, "deconstruct": {"items": [{"item": "hydraulic_press", "count": 1}]}}, {"type": "furniture", "id": "f_heavy_lathe", "name": "power lathe", "looks_like": "f_machinery_light", "description": "An industrial-grade lathe, for turning chunks of metal and other hard things into round chunks of metal and other hard things.", "symbol": "4", "color": "cyan_red", "move_cost_mod": -1, "coverage": 40, "required_str": 16, "flags": ["BLOCKSDOOR"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [12, 20]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "steel_lump", "count": [1, 2]}]}, "deconstruct": {"items": [{"item": "cable", "charges": [4, 8]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "steel_lump", "count": [2, 4]}, {"item": "scrap", "count": [12, 16]}, {"item": "pipe", "count": [0, 4]}, {"item": "motor_small", "count": 1}]}}, {"type": "furniture", "id": "f_air_compressor", "name": "air compressor", "looks_like": "f_standing_tank", "description": "This durable tank is topped with a motor that will cram as much air into the tank as possible.  You can take this down and plug it into your own power grid to use it.", "symbol": "8", "color": "black_yellow", "move_cost_mod": -1, "coverage": 80, "required_str": 12, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "air_compressor"}, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "air_compressor", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 1]}, {"item": "e_scrap", "count": [5, 10]}, {"item": "plastic_chunk", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_manual_tire_changer", "name": "manual tire changer", "looks_like": "f_machinery_light", "description": "This floor-mounted machine contains all the levers, clamps and mounts required to remove or fit a tire, plus an integrated foot pump to inflate it.", "//": "Based on the Redline TC500M Manual Tire Changer", "symbol": "7", "color": "black_yellow", "move_cost_mod": -1, "coverage": 30, "required_str": 10, "flags": ["BLOCKSDOOR", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "manual_tire_changer", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "plastic_chunk", "count": [0, 2]}]}, "crafting_pseudo_item": "fake_tire_changer"}, {"type": "furniture", "id": "f_fvat_empty", "name": "fermenting vat", "looks_like": "f_standing_tank", "description": "A sealable 50-liter vat for fermenting vinegar and various alcoholic brews.", "symbol": "O", "color": "brown", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "fvat_empty", "deconstruct": {"items": [{"item": "2x4", "count": 14}, {"item": "nail", "charges": [6, 12]}, {"item": "water_faucet", "count": 1}, {"item": "sheet_metal_small", "count": [4, 10]}, {"item": "scrap", "count": [5, 10]}]}, "bash": {"str_min": 3, "str_max": 45, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 8]}, {"item": "nail", "charges": [4, 8]}, {"item": "water_faucet", "prob": 50}, {"item": "sheet_metal_small", "count": [2, 6]}, {"item": "scrap", "count": [5, 10]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_fvat_full", "name": "filled fermenting vat", "looks_like": "f_fvat_empty", "description": "A sealable 50-liter vat for fermenting vinegar and various alcoholic brews.", "symbol": "O", "color": "brown_cyan", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "fvat_full", "deconstruct": {"items": [{"item": "2x4", "count": 14}, {"item": "nail", "charges": [6, 12]}, {"item": "water_faucet", "count": 1}, {"item": "sheet_metal_small", "count": [4, 10]}, {"item": "scrap", "count": [5, 10]}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 8]}, {"item": "nail", "charges": [4, 8]}, {"item": "water_faucet", "prob": 50}, {"item": "sheet_metal_small", "count": [2, 6]}, {"item": "scrap", "count": [5, 10]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_fvat_wood_empty", "name": "wooden fermenting vat", "looks_like": "f_fvat_empty", "description": "A sealable 50-liter wooden vat for fermenting vinegar and various alcoholic brews.", "symbol": "O", "color": "brown", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "fvat_empty", "deconstruct": {"items": [{"item": "2x4", "count": 14}, {"item": "cordage_6", "count": [150, 180]}, {"item": "splinter", "count": 2}]}, "bash": {"str_min": 3, "str_max": 45, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 8]}, {"item": "cordage_6", "count": [80, 120]}, {"item": "splinter", "count": 2}]}}, {"type": "furniture", "id": "f_fvat_wood_full", "name": "filled wooden fermenting vat", "looks_like": "f_fvat_wood_empty", "description": "A sealable 50-liter wooden vat for fermenting vinegar and various alcoholic brews.", "symbol": "O", "color": "brown_cyan", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "fvat_full", "deconstruct": {"items": [{"item": "2x4", "count": 14}, {"item": "cordage_6", "count": [150, 180]}, {"item": "splinter", "count": 2}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [4, 8]}, {"item": "cordage_6", "count": [80, 120]}, {"item": "splinter", "count": 2}]}}, {"type": "furniture", "id": "f_compost_empty", "name": "anaerobic digester", "looks_like": "f_standing_tank", "description": "A 1000 L anaerobic digester.", "symbol": "O", "color": "brown", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "compost_empty", "crafting_pseudo_item": "fake_digester_tank", "deconstruct": {"items": [{"item": "brick", "count": [200, 400]}, {"item": "pipe", "count": [6, 8]}, {"item": "pipe_fittings", "count": [6, 8]}, {"item": "sheet_metal_small", "count": [10, 20]}, {"item": "hose", "count": 1}]}, "bash": {"str_min": 3, "str_max": 45, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "scrap", "count": [8, 32]}]}}, {"type": "furniture", "id": "f_compost_full", "name": "filled anaerobic digester", "looks_like": "f_standing_tank", "description": "A 1000L anaerobic digester.", "symbol": "O", "color": "brown_cyan", "move_cost_mod": -1, "required_str": -1, "flags": ["NOITEM", "SEALED", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "DONT_REMOVE_ROTTEN"], "examine_action": "compost_full", "crafting_pseudo_item": "fake_digester_tank", "deconstruct": {"items": [{"item": "brick", "count": [200, 400]}, {"item": "pipe", "count": [6, 8]}, {"item": "pipe_fittings", "count": [6, 8]}, {"item": "sheet_metal_small", "count": [10, 20]}, {"item": "hose", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "scrap", "count": [8, 32]}]}}, {"type": "furniture", "id": "f_butcher_rack", "name": "butchering rack", "looks_like": "f_rack", "description": "A wooden butchering rack designed to hang a carcass in the air.", "symbol": "^", "bgcolor": "brown", "move_cost_mod": 2, "required_str": -1, "flags": ["PLACE_ITEM", "TRANSPARENT", "FLAMMABLE_ASH", "MOUNTABLE", "ALLOW_FIELD_EFFECT", "EASY_DECONSTRUCT", "BUTCHER_EQ", "ORGANIC"], "crafting_pseudo_item": "fake_butcher_rack", "deconstruct": {"items": [{"item": "stick_long", "count": 6}]}, "bash": {"str_min": 8, "str_max": 30, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "stick_long", "count": [1, 6]}]}}, {"type": "furniture", "id": "f_metal_butcher_rack", "name": "metal butchering rack", "looks_like": "f_butcher_rack", "description": "A metal butchering rack designed to hang a carcass in the air.  It can be deconstructed and folded for easy transportation.", "symbol": "^", "bgcolor": "light_gray", "move_cost_mod": 2, "required_str": -1, "deployed_item": "metal_butcher_rack", "examine_action": "deployed_furniture", "flags": ["PLACE_ITEM", "TRANSPARENT", "MOUNTABLE", "ALLOW_FIELD_EFFECT", "BUTCHER_EQ"], "crafting_pseudo_item": "fake_butcher_rack", "deconstruct": {"items": [{"item": "metal_butcher_rack", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [1, 3]}, {"item": "pipe", "count": [1, 3]}]}}, {"id": "f_hanging_meathook", "type": "furniture", "name": "hanging meathook", "looks_like": "f_butcher_rack", "description": "A hefty hook suspended from a chain for stringing up corpses.", "symbol": "g", "required_str": -1, "move_cost_mod": 2, "color": "light_gray", "deconstruct": {"items": [{"item": "grip_hook", "count": 1}, {"item": "chain", "count": 1}]}, "crafting_pseudo_item": "fake_butcher_rack", "flags": ["TRANSPARENT", "BUTCHER_EQ"]}, {"type": "furniture", "id": "f_wind_mill", "name": "wind mill", "description": "A small wind-powered mill that can convert starchy products into flour.", "symbol": "T", "bgcolor": "red", "move_cost_mod": 2, "required_str": -1, "examine_action": "quern_examine", "flags": ["SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "wind_mill", "count": 1}]}, "bash": {"str_min": 16, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "pipe", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "rock", "count": [8, 15]}]}}, {"type": "furniture", "id": "f_wind_mill_active", "name": "active wind mill", "looks_like": "f_wind_mill", "description": "A small wind-powered mill that can convert starchy products into flour.  Its brake has been removed and it is turning.", "symbol": "T", "bgcolor": "red", "move_cost_mod": 2, "required_str": -1, "examine_action": "quern_examine", "flags": ["SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "wind_mill", "count": 1}]}, "bash": {"str_min": 16, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "pipe", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "rock", "count": [8, 15]}]}}, {"type": "furniture", "id": "f_water_mill", "name": "water mill", "description": "A small water-powered mill that can convert starchy products into flour.", "symbol": "*", "bgcolor": "red", "move_cost_mod": 2, "required_str": -1, "examine_action": "quern_examine", "flags": ["SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "water_mill", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "nail", "charges": [5, 15]}, {"item": "splinter", "count": [20, 30]}, {"item": "sheet_metal_small", "count": [2, 3]}, {"item": "scrap", "count": [3, 5]}, {"item": "rock", "count": [8, 15]}]}}, {"type": "furniture", "id": "f_water_mill_active", "name": "active water mill", "looks_like": "f_water_mill", "description": "A small water-powered mill that can convert starchy products into flour.  Its brake has been removed and it is turning.", "symbol": "*", "bgcolor": "red", "move_cost_mod": 2, "required_str": -1, "examine_action": "quern_examine", "flags": ["SEALED", "ALLOW_FIELD_EFFECT", "CONTAINER", "NOITEM", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "water_mill", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "nail", "charges": [5, 15]}, {"item": "splinter", "count": [20, 30]}, {"item": "sheet_metal_small", "count": [2, 3]}, {"item": "scrap", "count": [3, 5]}, {"item": "rock", "count": [8, 15]}]}}, {"type": "furniture", "id": "f_aut_gas_console", "name": "automated gas console", "looks_like": "f_console", "description": "An automated gas flow control console.", "symbol": "9", "color": "blue", "move_cost_mod": -1, "coverage": 40, "required_str": 25, "flags": ["SEALED", "ALARMED", "BLOCKSDOOR"], "examine_action": "pay_gas", "bash": {"str_min": 7, "str_max": 30, "sound": "glass breaking!", "sound_fail": "whack!", "furn_set": "f_aut_gas_console_o"}}, {"type": "furniture", "id": "f_aut_gas_console_o", "name": "broken automated gas console", "looks_like": "f_console_broken", "description": "An automated gas flow control console.  It's broken.  This is not a good thing.", "symbol": "9", "color": "dark_gray", "move_cost_mod": -1, "coverage": 40, "required_str": 20, "flags": ["BLOCKSDOOR"], "bash": {"str_min": 5, "str_max": 45, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "hose", "count": 1}, {"item": "pipe_fittings", "count": 1}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_vending_reinforced", "name": "reinforced vending machine", "looks_like": "f_vending_c", "description": "A bit tougher to crack open than a regular vending machine.  That just makes it all the sweeter of a target, doesn't it?", "symbol": "{", "color": "light_red", "move_cost_mod": -1, "coverage": 90, "required_str": 30, "flags": ["SEALED", "PLACE_ITEM", "ALARMED", "CONTAINER", "BLOCKSDOOR", "MINEABLE"], "examine_action": "vending", "bash": {"str_min": 150, "str_max": 520, "sound": "glass breaking!", "sound_fail": "whack!", "furn_set": "f_vending_o", "items": [{"item": "glass_shard", "count": [8, 25]}, {"item": "sheet_metal", "count": [0, 2]}, {"item": "steel_chunk", "count": [1, 5]}, {"item": "money_one", "count": [0, 100]}, {"item": "money_two", "count": [0, 20]}, {"item": "money_five", "count": [0, 100]}, {"item": "money_ten", "count": [0, 100]}, {"item": "money_twenty", "count": [0, 100]}, {"item": "money_fifty", "count": [0, 50]}]}}, {"type": "furniture", "id": "f_vending_reinforced_off", "name": "reinforced vending machine (off)", "looks_like": "f_vending_reinforced", "description": "A bit tougher to crack open than a regular vending machine.  Out of order, for lack of power.  That just makes it all the sweeter of a target, doesn't it?", "symbol": "{", "color": "light_red", "move_cost_mod": -1, "coverage": 90, "required_str": 30, "flags": ["SEALED", "PLACE_ITEM", "CONTAINER", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 150, "str_max": 520, "sound": "glass breaking!", "sound_fail": "whack!", "furn_set": "f_vending_o", "items": [{"item": "glass_shard", "count": [8, 25]}, {"item": "sheet_metal", "count": [0, 2]}, {"item": "steel_chunk", "count": [1, 5]}, {"item": "money_one", "count": [0, 100]}, {"item": "money_two", "count": [0, 20]}, {"item": "money_five", "count": [0, 100]}, {"item": "money_ten", "count": [0, 100]}, {"item": "money_twenty", "count": [0, 100]}, {"item": "money_fifty", "count": [0, 50]}]}}, {"type": "furniture", "id": "f_vending_c", "name": "vending machine", "looks_like": "f_console", "symbol": "{", "description": "An upright metal cabinet with a see-through door.  The goods inside can be yours if you have money on a cash card.", "color": "light_cyan", "move_cost_mod": -1, "coverage": 90, "required_str": 12, "flags": ["SEALED", "PLACE_ITEM", "ALARMED", "CONTAINER", "BLOCKSDOOR", "MINEABLE", "TRANSPARENT"], "examine_action": "vending", "bash": {"str_min": 20, "str_max": 40, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_vending_o", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_vending_c_off", "name": "vending machine (off)", "looks_like": "f_vending_c", "symbol": "{", "description": "An upright metal cabinet with a see-through door.  Out of order, for lack of power.", "color": "light_cyan", "move_cost_mod": -1, "coverage": 90, "required_str": 12, "flags": ["SEALED", "PLACE_ITEM", "CONTAINER", "BLOCKSDOOR", "MINEABLE", "TRANSPARENT"], "bash": {"str_min": 20, "str_max": 40, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_vending_o", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_vending_o", "name": "broken vending machine", "looks_like": "f_console_broken", "description": "An upright metal cabinet with a see-through door.  It's smashed open, so you can take whatever you want for free.", "symbol": "{", "color": "dark_gray", "move_cost_mod": -1, "coverage": 90, "required_str": 12, "flags": ["PLACE_ITEM", "CONTAINER", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 30, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "hose", "count": 1}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}, {"item": "coin_nickel", "count": [0, 100]}, {"item": "coin_dime", "count": [0, 100]}, {"item": "coin_quarter", "count": [0, 100]}, {"item": "coin_dollar", "count": [0, 1]}, {"item": "money_one", "count": [0, 100]}, {"item": "money_two", "count": [0, 10]}, {"item": "money_five", "count": [0, 30]}]}}, {"type": "furniture", "id": "f_rope_up", "name": "grappling hook", "looks_like": "t_rope_up", "description": "A dangling piece of rope, hooked securely to something above you.  You could climb up or take it down.", "symbol": "<", "color": "white", "move_cost_mod": 1, "required_str": -1, "flags": ["CLIMBABLE", "TRANSPARENT", "SEEN_FROM_ABOVE", "EXAMINE_FROM_ABOVE", "DIFFICULT_Z", "ALLOW_ON_OPEN_AIR", "NOCOLLIDE"], "examine_action": "deployed_furniture", "deployed_item": "grapnel", "bash": {"str_min": 3, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "grip_hook", "count": [4, 4]}, {"item": "rope_30", "count": [1, 1]}]}}, {"type": "furniture", "id": "f_vine_up", "name": "vine leading up", "looks_like": "t_rope_up", "description": "A thick vine is rigidly anchored to something above you.  It looks quite alive.  You could climb up or tear it down.", "symbol": "<", "color": "light_green", "move_cost_mod": 1, "required_str": -1, "flags": ["CLIMBABLE", "TRANSPARENT", "SEEN_FROM_ABOVE", "ALLOW_ON_OPEN_AIR", "DIFFICULT_Z", "NOCOLLIDE"], "examine_action": "deployed_furniture", "deployed_item": "vine_30", "bash": {"str_min": 1, "str_max": 10, "sound": "rrrrip!", "sound_fail": "twang.", "items": [{"item": "vine_6", "count": [2, 5]}]}}, {"type": "furniture", "id": "f_web_up", "name": "web leading up", "looks_like": "fd_web", "description": "A thick, dangling strand of slightly sticky web, the other end is tied securely to something above you.  You could use it to climb up.", "symbol": "<", "color": "white", "move_cost_mod": 1, "required_str": -1, "flags": ["CLIMBABLE", "TRANSPARENT", "SEEN_FROM_ABOVE", "ALLOW_ON_OPEN_AIR", "DIFFICULT_Z", "NOCOLLIDE"], "examine_action": "deployed_furniture", "deployed_item": "rope_30", "bash": {"str_min": 1, "str_max": 10, "sound": "rrrrip!", "sound_fail": "twang.", "items": [{"item": "rope_30", "count": [1, 1]}]}}, {"type": "furniture", "id": "f_hoist_rope", "name": "rope hoist", "description": "A loop of rope hangs here, using a sturdy horizontal support as a basic pulley.  While crude, it gives you some mechanical advantage for lifting things.", "symbol": "|", "color": "yellow", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT", "NOCOLLIDE"], "examine_action": "deployed_furniture", "deployed_item": "rope_30", "deconstruct": {"items": [{"item": "rope_30", "count": 1}]}, "crafting_pseudo_item": "fake_lift_light"}, {"type": "furniture", "id": "f_hoist_pulley", "name": "rope and tackle hoist", "looks_like": "f_hoist_rope", "description": "A loop of rope hangs here, looped through a quality tackle to give a significant mechanical advantage while lifting heavy objects.", "symbol": "|", "color": "white", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "NOCOLLIDE"], "deconstruct": {"items": [{"item": "block_and_tackle", "count": 1}, {"item": "rope_30", "count": 1}, {"item": "spike", "count": 1}]}, "crafting_pseudo_item": "fake_lift_medium"}, {"type": "furniture", "id": "f_hoist_chain", "name": "chain hoist", "looks_like": "f_hoist_rope", "description": "A sturdy chain and heavy tackle hang well-supported from the ceiling here, ready to lift most heavy items you could think of using mechanical advantage.", "symbol": "|", "color": "dark_gray", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "NOCOLLIDE"], "deconstruct": {"items": [{"item": "chain", "count": 1}, {"item": "block_and_tackle", "count": 1}, {"item": "nuts_bolts", "charges": 2}]}, "crafting_pseudo_item": "fake_lift_heavy"}]