[{"type": "construction", "id": "constr_deconstruct", "group": "deconstruct_furniture", "category": "OTHER", "required_skills": [["fabrication", 0]], "time": "20 m", "qualities": [[{"id": "PRY", "level": 1}], [{"id": "SCREW", "level": 1}]], "pre_special": "check_deconstruct", "do_turn_special": "do_turn_deconstruct", "post_flags": ["keep_items"], "post_special": "done_deconstruct"}, {"type": "construction", "id": "constr_deconstruct_simple", "group": "deconstruct_simple_furniture", "category": "OTHER", "required_skills": [["fabrication", 0]], "time": "10 s", "pre_note": "Certain terrain and furniture can be deconstructed without any tools.", "pre_flags": "EASY_DECONSTRUCT", "do_turn_special": "do_turn_deconstruct", "post_flags": ["keep_items"], "post_special": "done_deconstruct"}, {"type": "construction", "id": "constr_chop_trunk", "group": "chop_tree_trunk_into_planks", "category": "FARM_WOOD", "required_skills": [["survival", 2]], "time": "60 m", "qualities": [[{"id": "AXE", "level": 2}, {"id": "SAW_W", "level": 2}]], "pre_terrain": "t_trunk", "post_terrain": "t_dirt", "post_special": "done_trunk_plank"}, {"type": "construction", "id": "constr_clear_rubble", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 0]], "time": "4 m", "byproducts": [{"item": "brick", "count": [0, 2]}, {"item": "rock", "count": [0, 2]}, {"item": "splinter", "count": [0, 2]}, {"item": "scrap", "charges": [0, 2]}, {"item": "material_gravel", "charges": [0, 3]}], "pre_terrain": "f_rubble", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_clear_rubble_ash", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 0]], "time": "1 m", "byproducts": [{"item": "ash", "charges": [0, 100]}], "pre_flags": "RUBBLE", "pre_terrain": "f_ash", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_clear_rubble_landfill", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 0]], "time": "4 m", "byproducts": [{"item": "material_soil", "count": [0, 2]}, {"item": "plastic_chunk", "count": [0, 2]}, {"item": "ceramic_shard", "count": [0, 2]}, {"item": "splinter", "count": [0, 2]}, {"item": "scrap_cotton", "count": [0, 2]}], "pre_flags": "RUBBLE", "pre_terrain": "f_rubble_landfill", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_clear_rubble_rock", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 0]], "time": "5 m", "byproducts": [{"item": "rock", "count": [0, 5]}, {"item": "rock_flaking", "count": [0, 2]}, {"item": "flint", "count": [0, 2]}, {"item": "sharp_rock", "count": [0, 2]}, {"item": "pebble", "charges": [0, 4]}], "pre_flags": "RUBBLE", "pre_terrain": "f_rubble_rock", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_clear_rubble_woodchips", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 0]], "time": "2 m", "byproducts": [{"item": "splinter", "count": [0, 5]}], "pre_flags": "RUBBLE", "pre_terrain": "f_woodchips", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_clear_rubble_wreckage", "group": "clear_rubble", "category": "OTHER", "qualities": [[{"id": "DIG", "level": 2}, {"id": "SAW_M", "level": 2}]], "required_skills": [["survival", 0]], "time": "5 m", "byproducts": [{"item": "scrap", "charges": [0, 6]}, {"item": "slag", "charges": [0, 3]}, {"item": "scrap_tin", "count": [0, 1]}, {"item": "scrap_aluminum", "count": [0, 1]}, {"item": "scrap_copper", "count": [0, 1]}], "pre_flags": "RUBBLE", "pre_terrain": "f_wreckage", "post_terrain": "f_null", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_crafting_spot", "group": "make_crafting_spot", "category": "OTHER", "required_skills": [], "time": "0 m", "pre_note": "Mark a spot for crafting.  Crafting tasks next to this tile will automatically use this location instead of attempting to craft in your hands, with the usual crafting speed penalty for working on the ground.  Does not prevent using a proper workbench, if available.  Deconstruct or smash to remove.", "pre_special": "check_empty", "post_flags": ["keep_items"], "dark_craftable": true, "post_terrain": "f_ground_crafting_spot"}, {"type": "construction", "id": "constr_extract_clay", "group": "extract_clay", "//": "Step 1 : extract clay", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "clay_lump", "count": [6, 12]}], "pre_terrain": "t_clay", "post_special": "done_extract_maybe_revert_to_dirt", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_extract_clay_mound", "group": "extract_clay", "//": "Step 2 : extract clay", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "clay_lump", "count": [6, 12]}], "pre_terrain": "t_claymound", "post_special": "done_extract_maybe_revert_to_dirt", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_extract_dirt_mound", "group": "extract_dirt", "//": "Step 1 : extract dirt", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "material_soil", "count": [100, 200]}], "pre_terrain": "t_dirtmoundfloor", "post_terrain": "t_dirt", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_extract_sand", "group": "extract_sand", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "material_sand", "charges": [300, 600]}], "pre_terrain": "t_sand", "post_special": "done_extract_maybe_revert_to_dirt"}, {"type": "construction", "id": "constr_extract_sandmound", "group": "extract_sand", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "material_sand", "charges": [300, 600]}], "pre_terrain": "t_sandmound", "post_special": "done_extract_maybe_revert_to_dirt"}, {"type": "construction", "id": "constr_extract_sand_underground", "group": "extract_sand", "category": "OTHER", "required_skills": [["survival", 0]], "time": "30 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "material_sand", "charges": [300, 600]}], "pre_terrain": "t_sand_underground", "post_special": "done_extract_maybe_revert_to_dirt"}, {"type": "construction", "id": "constr_firewood_source", "group": "mark_firewood_source", "category": "OTHER", "required_skills": [], "time": "0 m", "pre_note": "Firewood or other flammable materials on a nearby tile marked in this way may be used to automatically refuel fires.  This will be done to maintain light during long-running tasks that require it such as crafting or reading, but not (for example) if you are simply waiting nearby.", "pre_special": "check_no_trap", "post_special": "done_mark_firewood", "dark_craftable": true, "post_flags": ["keep_items"], "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "constr_practice_target", "group": "mark_practice_target", "category": "OTHER", "required_skills": [], "time": "0 m", "pre_note": "Mark a spot for target practice.  Firing will automatically target the first practice target location found in gun range, if no enemies are around.", "pre_special": "check_no_trap", "post_special": "done_mark_practice_target", "dark_craftable": true, "post_flags": ["keep_items"], "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "constr_veh", "group": "start_vehicle_construction", "//": "no components required, they are filled in at runtime based on the vehicle parts that can be used to start a vehicle", "category": "OTHER", "required_skills": [["mechanics", 0]], "time": "10 m", "//2": "no components required, they are filled in at runtime based on the vehicle parts that can be used to start a vehicle", "pre_special": "check_empty", "post_special": "done_vehicle", "vehicle_start": true}]