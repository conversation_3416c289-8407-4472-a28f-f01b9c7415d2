[{"type": "furniture", "id": "f_alien_tendril", "name": "glowing tendril", "description": "A willowy tendril growing from the floor, gently waving back and forth.  A faint light spills from it.", "symbol": "i", "color": "blue", "move_cost_mod": 4, "coverage": 10, "light_emitted": 15, "required_str": -1, "flags": ["TRANSPARENT"], "bash": {"str_min": 8, "str_max": 20, "sound": "splorch!", "sound_fail": "whump!", "furn_set": "f_alien_scar", "items": [{"item": "fetid_goop", "count": [3, 5], "prob": 100}]}}, {"type": "furniture", "id": "f_alien_anemone", "name": "wafting anemone", "description": "A fleshy white protuberance growing from the floor, with a cluster of tendrils protruding from it.  It looks almost exactly like a sea anemone, even waving gently as though underwater.", "symbol": "V", "color": "white", "move_cost_mod": 6, "coverage": 30, "required_str": -1, "light_emitted": 3, "flags": ["TRANSPARENT"], "emissions": ["emit_hot_air_migo_seep"], "bash": {"str_min": 13, "str_max": 26, "sound": "splorch!", "sound_fail": "whump!", "furn_set": "f_alien_scar", "items": [{"item": "fetid_goop", "count": [3, 7], "prob": 100}]}}, {"type": "furniture", "id": "f_alien_gasper", "name": "gasping tube", "description": "A meaty green stalactite with a thickened hide like that of a starfish, extending from the floor to the ceiling.  In the center is a series of ports somewhat like mouths, from which pour bursts of a vile-smelling gas.", "symbol": "{", "color": "green", "move_cost_mod": 6, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT"], "emissions": ["emit_migo_atmosphere", "emit_hot_air_migo_blast"], "//": "TODO: Make a custom toxic gas emission that is not visible to the player", "bash": {"str_min": 30, "str_max": 60, "sound": "splorch!", "sound_fail": "whump!", "furn_set": "f_alien_scar", "items": [{"item": "fetid_goop", "count": [15, 25], "prob": 100}]}}, {"type": "furniture", "id": "f_alien_zapper", "name": "twitching frond", "description": "A spine resembling moth antennae juts from the ground, swaying gently in the air.  Every so often, a cascade of energy arcs along it and discharges into the ceiling.", "symbol": "F", "color": "light_blue", "move_cost_mod": 4, "coverage": 20, "required_str": -1, "flags": ["TRANSPARENT"], "emissions": ["emit_glimmer", "emit_shock_burst"], "//": "TODO: Make a custom toxic gas emission that is not visible to the player", "bash": {"str_min": 30, "str_max": 60, "sound": "splorch!", "sound_fail": "whump!", "furn_set": "f_alien_scar"}}, {"type": "furniture", "id": "f_alien_scar", "name": "scarred lump", "description": "A pile of nondescript alien flesh, twitching and belching strange gases out of injured orifices.", "symbol": "{", "color": "green", "move_cost_mod": 6, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT"], "emissions": ["emit_hot_air_migo_seep", "emit_shock_burst"], "//": "TODO: Make a custom toxic gas emission that is not visible to the player", "bash": {"str_min": 250, "str_max": 600, "sound": "splorch!", "sound_fail": "splat!", "items": [{"item": "fetid_goop", "count": [6, 13], "prob": 100}]}}, {"type": "furniture", "id": "f_alien_pod", "name": "slimy pod", "description": "A slick, translucent pod suspended on a thin stalk.  It is covered in a thick mucus, obscuring whatever is floating in the gel-like substance inside.", "symbol": "0", "color": "magenta", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT"], "bash": {"str_min": 25, "str_max": 60, "sound": "splorch!", "sound_fail": "whump.", "items": [{"item": "fetid_goop", "count": [5, 10], "prob": 100}]}}, {"type": "furniture", "id": "f_alien_pod_organ", "name": "organ pod", "description": "A translucent pod suspended on a thin stalk.  Inside you can see the dimly outlined shape of human organs, floating in some kind of preservative goo.", "symbol": "0", "color": "yellow", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "light_emitted": 3, "flags": ["TRANSPARENT"], "bash": {"str_min": 25, "str_max": 60, "sound": "splorch!", "sound_fail": "whump."}}, {"type": "furniture", "id": "f_alien_pod_resin", "name": "resin pod", "description": "A translucent pod suspended on a thin stalk.  Inside is a clean, clear, resinous-looking fluid.  You could fairly easily tear it from the stalk and take it with you.", "symbol": "0", "color": "light_blue", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "flags": ["TRANSPARENT", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "alien_pod_resin", "count": [6, 8]}]}, "bash": {"str_min": 25, "str_max": 60, "sound": "splorch!", "sound_fail": "whump."}}, {"type": "furniture", "id": "f_alien_table", "name": "fleshy altar", "description": "This pulsing protuberance juts from the floor, its sides covered in scaled, oozing skin.  The surface is flat, but undulates softly.  A handful of unidentifiable appendages reach from the sides, suggesting a sort of nightmarish living Autodoc.", "symbol": "n", "color": "pink", "move_cost_mod": -1, "coverage": 40, "required_str": -1, "light_emitted": 12, "flags": ["TRANSPARENT"], "bash": {"str_min": 45, "str_max": 60, "sound": "splorch!", "sound_fail": "whump.", "furn_set": "f_alien_scar"}}, {"type": "furniture", "id": "f_scrap_antenna", "name": "scrap antenna", "looks_like": "t_radio_tower", "description": "This contraption looks like some sort of cobbled-together transmitter or receiver antenna.  It consists of a number of mismatched blinking lights, spinning servos, and dangling wires connected to a hefty metal box.", "symbol": "d", "color": "light_gray", "move_cost_mod": 2, "coverage": 30, "required_str": 11, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 30, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "pipe_fittings", "count": [0, 3]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [2, 10]}, {"item": "antenna", "count": [0, 1]}, {"item": "exodii_module", "count": [0, 1]}, {"item": "exodii_motor", "count": [0, 1]}, {"item": "cable", "charges": [0, 2]}, {"item": "scrap_copper", "count": [2, 12]}]}}, {"type": "furniture", "id": "f_exodii_signpost", "name": "pictographic signpost", "looks_like": "f_sign", "description": "This simple scrap metal sign has a series of pictographs on it depicting what seems to be a trade interaction.  There is a crude but understandable map beneath it.  You should take a closer look.", "symbol": "!", "color": "red", "move_cost_mod": 1, "coverage": 35, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "ORGANIC", "MOUNTABLE", "SIGN"], "deconstruct": {"items": [{"item": "sheet_metal_small", "count": 2}, {"item": "nail", "charges": [2, 5]}]}, "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "sheet_metal_small", "count": [1, 2]}, {"item": "nail", "charges": [2, 4]}]}, "examine_action": {"type": "effect_on_condition", "effect_on_conditions": [{"id": "EOC_EXODII_SIGNPOST_FALSE", "condition": {"u_has_mission": "directions_exodii_signpost"}, "effect": [{"u_message": "You study the signpost.  It shows a similar set of directions to the ones you already have.  You cross-reference them and don't learn anything new.", "popup": true}]}, {"id": "EOC_EXODII_SIGNPOST", "condition": {"not": {"u_has_mission": "directions_exodii_signpost"}}, "effect": [{"u_message": "You study the signpost.  It shows simply-drawn pictograms of a pair of hands holding out an object, and another pair taking the object and offering a pouch in return.  Beneath the pictures there's a crude but understandable map with arrows pointing to what looks like some kind of castle.  You do your best to relate those to landmarks you know about, and mark the likely location on your map.", "popup": true}, {"assign_mission": "directions_exodii_signpost"}]}]}}, {"type": "furniture", "id": "f_exodii_botrack", "name": "rack of robots", "looks_like": "f_server", "description": "A wire cage containing a number of robot parts, from chassis to limbs, firmly clamped onto sturdy hanging rods.  It looks like it would be exceptionally difficult to open it and get at them without damaging them, probably by design.", "symbol": "E", "color": "light_gray", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "max_volume": "1500 L", "flags": ["MOUNTABLE", "BLOCKSDOOR"], "bash": {"str_min": 60, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "lc_wire", "count": [3, 10]}, {"item": "sheet_metal", "count": [0, 2]}, {"item": "sheet_metal_small", "count": [20, 30]}, {"item": "exodii_chassis", "count": [0, 1]}, {"item": "exodii_drone_chassis", "count": [0, 1]}, {"item": "scrap_copper", "count": [6, 25]}, {"item": "e_scrap", "count": [5, 20]}]}}, {"type": "furniture", "id": "f_exodii_scanner", "name": "spinning dish", "looks_like": "t_machinery_light", "description": "A slowly-spinning parabolic dish engraved with an unrecognizable pattern of symbols that might, at a guess, be some sort of circuitry pattern - or maybe a religious incantation.  It is mounted on a completely mismatched electric motor that looks fairly mundane.", "symbol": "W", "color": "light_gray", "move_cost_mod": 2, "coverage": 30, "required_str": 11, "flags": ["TRANSPARENT", "NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 30, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [2, 10]}, {"item": "scrap_copper", "count": [2, 12]}, {"item": "exodii_computer", "count": [0, 1]}, {"item": "exodii_motor", "count": [0, 1]}, {"item": "cable", "charges": [0, 3]}, {"item": "exodii_scanner_dish", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_lamp", "name": "scavenged utility light", "looks_like": "t_utility_light", "description": "The bulbs emit an eerie greenish glow, and some parts of it have a curved appearance as though the metal were grown in a lab, but by and large this isn't a particularly unusual looking area light.", "symbol": "T", "color": "light_gray", "move_cost_mod": 3, "coverage": 30, "required_str": 6, "light_emitted": 90, "flags": ["TRANSPARENT", "NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 20, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [2, 10]}, {"item": "scrap_copper", "count": [2, 12]}, {"item": "amplifier", "count": [0, 5]}, {"item": "power_supply", "count": [0, 1]}, {"item": "cable", "charges": [0, 5]}]}}, {"type": "furniture", "id": "f_exodii_pump", "name": "clanking fluid pump", "looks_like": "t_machinery_heavy", "description": "The huge, sweating pipes and large tank reveal this clanking and tortuous piece of equipment to be some sort of fluid pump.  It is cobbled together from several apparently ill-fitting parts that have been fitted with some very strange-looking but surprisingly clever adapters.", "symbol": "0", "color": "light_gray", "move_cost_mod": -1, "coverage": 80, "required_str": 14, "flags": ["TRANSPARENT", "NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 40, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [2, 4]}, {"item": "55gal_drum", "count": [0, 1]}, {"item": "sheet_metal_small", "count": [2, 10]}, {"item": "scrap_copper", "count": [5, 12]}, {"item": "exodii_module", "count": [0, 1]}, {"item": "exodii_motor", "count": [0, 2]}, {"item": "pipe_fittings", "count": [2, 6]}]}}, {"type": "furniture", "id": "f_exodii_printer_large", "name": "metalloid printer", "looks_like": "t_broken_generator", "description": "This huge device fairly closely resembles a 3D printer, aside from its industrial size.  Huge spools of various thin metallic wires atop it feed into what seems to be some sort of nozzle, and massive electrical cables disappear into the wall behind it.", "symbol": "U", "color": "light_gray", "move_cost_mod": -1, "coverage": 80, "required_str": 14, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 90, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [20, 120]}, {"item": "pipe", "count": [20, 40]}, {"item": "sheet_metal_small", "count": [20, 100]}, {"item": "scrap_copper", "count": [62, 124]}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "exodii_module", "count": [0, 6]}, {"item": "exodii_motor", "count": [0, 8]}, {"item": "exodii_computer", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_exodii_printer_small", "name": "circuit printer", "looks_like": "t_machinery_electronic", "description": "A tall device with a lattice of pipes and nozzles.  Based on the pile of products sitting in the output hopper, it looks like it's used as a printer for complex electronic components.", "symbol": "U", "color": "light_gray", "move_cost_mod": -1, "coverage": 80, "required_str": 14, "flags": ["TRANSPARENT", "NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 90, "str_max": 150, "sound_fail": "clang!", "items": [{"item": "scrap", "count": [20, 120]}, {"item": "pipe", "count": [10, 20]}, {"item": "pipe_fittings", "count": [2, 4]}, {"item": "sheet_metal_small", "count": [10, 30]}, {"item": "scrap_copper", "count": [25, 62]}, {"item": "e_scrap", "count": [20, 40]}, {"item": "exodii_module", "count": [0, 5]}, {"item": "exodii_motor", "count": [0, 6]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_generator_1", "name": "Mavrik 10-101 power core", "description": "There is a faded metallic plaque on the side of this cylindrical device.  The lettering is a strange combination of English and Cyrillic script.  It reads:\nMavrik 10-101\nStabиlиtи\nSaфetи\nA нyuclиar фyutuр ви bilив iн", "symbol": "G", "color": "white", "move_cost_mod": -1, "coverage": 60, "required_str": -1, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 60, "str_max": 400, "explosive": 25, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}, {"item": "plutonium", "count": [0, 3]}, {"item": "lead", "charges": [12, 18]}, {"item": "sheet_metal_small", "count": [10, 30]}, {"item": "scrap_copper", "count": [25, 50]}, {"item": "e_scrap", "count": [20, 40]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [4, 8]}, {"item": "exodii_module", "count": [0, 5]}, {"item": "exodii_motor", "count": [0, 6]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_generator_2", "name": "spherical generator", "description": "This huge steel-plated sphere gives off a soft hum as it, presumably, generates power.  A heavy lead plate bolted to the front might describe what it is, but it is written in something that looks vaguely like cuneiform.", "symbol": "G", "color": "red", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 60, "str_max": 400, "explosive": 25, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}, {"item": "steel_armor", "count": [2, 4]}, {"item": "plutonium", "count": [0, 3]}, {"item": "lead", "charges": [12, 18]}, {"item": "sheet_metal_small", "count": [10, 30]}, {"item": "scrap_copper", "count": [25, 50]}, {"item": "e_scrap", "count": [20, 40]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [4, 8]}, {"item": "exodii_module", "count": [0, 5]}, {"item": "exodii_motor", "count": [0, 6]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_generator_3", "name": "fluidic generator", "description": "Twisting reams of copper and steel pipes wrap around a central core that thrums softly.  Although there are no labels in any language you recognize, the cables connected to the base suggest that this is some sort of power generator.", "symbol": "G", "color": "green", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 60, "str_max": 400, "explosive": 25, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}, {"item": "cu_pipe", "count": [2, 4]}, {"item": "pipe", "count": [0, 3]}, {"item": "pipe_fittings", "count": [0, 3]}, {"item": "plutonium", "count": [0, 3]}, {"item": "lead", "charges": [12, 18]}, {"item": "sheet_metal_small", "count": [10, 30]}, {"item": "scrap_copper", "count": [12, 37]}, {"item": "e_scrap", "count": [10, 20]}, {"item": "circuit", "count": [2, 5]}, {"item": "power_supply", "count": [4, 8]}, {"item": "exodii_module", "count": [0, 5]}, {"item": "exodii_motor", "count": [0, 6]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_generator_4", "name": "rusty generator", "description": "A tall, unremarkable cylinder capped by a wide, flat saucer.  There is heavy rusting at the base.  A series of connected cables suggest that it's some sort of power generator.", "symbol": "G", "color": "yellow_red", "move_cost_mod": -1, "coverage": 60, "required_str": -1, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 60, "str_max": 400, "explosive": 25, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}, {"item": "sheet_metal", "count": [5, 10]}, {"item": "plutonium", "count": [0, 3]}, {"item": "lead", "charges": [12, 18]}, {"item": "sheet_metal_small", "count": [10, 30]}, {"item": "scrap_copper", "count": [12, 37]}, {"item": "e_scrap", "count": [15, 30]}, {"item": "circuit", "count": [2, 5]}, {"item": "power_supply", "count": [2, 8]}, {"item": "exodii_module", "count": [0, 5]}, {"item": "exodii_motor", "count": [0, 6]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_charger", "name": "personal charging station", "description": "The cyborg equivalent of a bed, this is a custom-shaped near-vertical booth designed to mount a heavy metal frame and connect it to a central power supply and nutrient source.", "symbol": "{", "color": "light_green", "move_cost_mod": 6, "coverage": 80, "required_str": -1, "flags": ["TRANSPARENT"], "bash": {"str_min": 18, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "sheet_metal", "count": [0, 1]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "e_scrap", "count": [2, 6]}, {"item": "metal_tank_little", "count": [0, 1]}, {"item": "pipe", "count": [0, 4]}, {"item": "circuit", "count": [2, 5]}, {"item": "amplifier", "count": [2, 5]}, {"item": "power_supply", "count": [2, 8]}, {"item": "exodii_module", "count": [0, 2]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_charger_cheap", "name": "simple charging station", "description": "A simple, unadorned, corroded metal charging and feeding station, with hookups for two units somewhat larger than a human to stand side by side and be recharged and refueled.", "symbol": "Y", "color": "light_green", "move_cost_mod": 6, "coverage": 80, "required_str": -1, "flags": ["TRANSPARENT"], "bash": {"str_min": 19, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "sheet_metal", "count": [0, 1]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "e_scrap", "count": [2, 6]}, {"item": "metal_tank_little", "count": [0, 1]}, {"item": "pipe", "count": [0, 4]}, {"item": "circuit", "count": [2, 5]}, {"item": "amplifier", "count": [2, 5]}, {"item": "power_supply", "count": [2, 8]}, {"item": "large_storage_battery", "count": [0, 1]}, {"item": "exodii_module", "count": [0, 2]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_portal_tower", "name": "shimmering superstructure", "description": "A simple metal superstructure like you might see holding up a radio tower, this one seems to give off just the faintest hint of shimmer, like heat waves.  The metal has a burnt, iridescent pattern.  There is a boxy, unimpressive-looking device at the top of the tower.", "symbol": "H", "color": "light_cyan", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "flags": ["NOITEM", "TRANSPARENT", "MOUNTABLE"], "bash": {"str_min": 45, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "pipe", "count": [0, 4]}, {"item": "circuit", "count": [2, 5]}, {"item": "amplifier", "count": [2, 5]}, {"item": "power_supply", "count": [2, 8]}, {"item": "exodii_module", "count": [0, 2]}, {"item": "exodii_portalizer", "count": [0, 4]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_portal_enclosure", "name": "portal enclosure", "description": "A heavy metal ring attached to dozens of wires, charred by residue that gives off an acrid, unfamiliar smell.", "symbol": "Q", "color": "light_gray", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "flags": ["NOITEM", "TRANSPARENT", "MOUNTABLE"], "bash": {"str_min": 45, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "circuit", "count": [1, 3]}, {"item": "amplifier", "count": [1, 3]}, {"item": "power_supply", "count": [1, 4]}, {"item": "exodii_module", "count": [0, 1]}, {"item": "exodii_portalizer", "count": [0, 2]}, {"item": "exodii_computer", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_exodii_locator_antenna", "name": "locator antenna", "description": "A tall metal pole solidly fixed to the roof.  It does not look activated but there's a small set of holes near its base, maybe something could be plugged in one to reboot the locator.", "symbol": "Z", "color": "light_gray", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "bash": {"str_min": 45, "str_max": 200, "sound": "metal screeching!", "sound_fail": "crash!", "items": [{"item": "scrap", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "circuit", "count": [1, 3]}, {"item": "amplifier", "count": [1, 3]}, {"item": "power_supply", "count": [1, 4]}, {"item": "exodii_module", "count": [0, 1]}]}, "flags": ["NOITEM", "TRANSPARENT", "MOUNTABLE"], "examine_action": {"type": "effect_on_condition", "effect_on_conditions": [{"id": "EOC_EXOWARE_LOCATOR_ACTIVE", "condition": {"math": ["u_rebooted_locator", "==", "1"]}, "effect": [{"u_message": "The locator is already active"}]}, {"id": "EOC_EXOWARE_LOCATOR_FALSE", "condition": {"and": [{"not": {"u_has_item": "warehouse_rebooter"}}, {"math": ["u_rebooted_locator", "==", "0"]}]}, "effect": [{"u_message": "You don't have anything that could reactivate this device"}]}, {"id": "EOC_EXOWARE_LOCATOR", "condition": {"and": [{"u_has_item": "warehouse_rebooter"}, {"math": ["u_rebooted_locator", "==", "0"]}]}, "effect": [{"u_message": "You plug the rebooter in the locator.\n<color_light_green>The locator reactivates.</color>"}, {"u_consume_item": "warehouse_rebooter"}, {"math": ["u_rebooted_locator", "=", "1"]}]}]}}, {"type": "furniture", "id": "f_crystalline_flower", "name": "crystalline flower", "description": "A crystalline structure that resembles a plant.", "symbol": "f", "color": "light_green", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE"], "//": "decorative flora for breather biome.", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_absence", "name": "less than nothing", "description": "There is nothing here.  Not even time or space.", "symbol": "O", "color": "black", "move_cost_mod": -1, "required_str": -1, "flags": ["TRANSPARENT", "FLOATS_IN_AIR"]}, {"type": "furniture", "id": "f_impaled_officer", "name": "impaled officer", "description": "A dead body encased in high-tech armor hanging in the air.  Their form seems twisted and refracted in the light of the strange glowing crystal that has seeped out of the ground and impaled their body.", "symbol": "!", "color": "red", "move_cost_mod": -1, "coverage": 100, "flags": ["TRANSPARENT", "ORGANIC"], "deconstruct": {"items": [{"item": "glass_shard", "count": 1000}]}, "required_str": -1, "bash": {"str_min": 200, "str_max": 400, "sound": "smash!", "sound_fail": "an absence of sound.", "items": [{"item": "glass_shard", "count": [10, 20]}]}, "examine_action": {"type": "effect_on_condition", "effect_on_conditions": [{"id": "EOC_HAUNTING_WARN", "effect": [{"open_dialogue": {"topic": "TALK_XEDRA_MERC_MAIN"}}]}]}}, {"type": "furniture", "id": "f_impaled_soldier", "description": "A dead body encased in military armor hanging in the air.  Their form seems twisted and refracted in the light of the strange glowing crystal that has seeped out of the ground and impaled their body.", "name": "impaled mercenary", "symbol": "!", "color": "brown", "move_cost_mod": -1, "coverage": 100, "deconstruct": {"items": [{"item": "glass_shard", "count": 1000}]}, "required_str": -1, "bash": {"str_min": 100, "str_max": 100, "sound": "smash!", "sound_fail": "an absence of sound.", "items": [{"item": "glass_shard", "count": [10, 20]}]}, "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "ORGANIC"]}, {"type": "furniture", "id": "f_empty_spike", "name": "empty spike", "description": "A strange otherworldly spike that refracts light in strange ways.  The body no longer rests on it.", "symbol": "!", "color": "black", "move_cost_mod": -1, "coverage": 100, "flags": ["TRANSPARENT", "ORGANIC"], "deconstruct": {"items": [{"item": "glass_shard", "count": 1000}]}, "required_str": -1, "bash": {"str_min": 100, "str_max": 100, "sound": "smash!", "sound_fail": "an absence of sound.", "items": [{"item": "glass", "count": [1, 2]}]}, "examine_action": {"type": "effect_on_condition", "effect_on_conditions": [{"id": "EOC_MISSING_BODY", "effect": [{"u_message": "The body is gone but it can't have gone far, you need to destroy it."}]}]}}]