name: "Weekly changelog update"

on:
  schedule:
    - cron: "45 2 * * 1"
  push:
    branches:
      - master
    paths:
      - '.github/workflows/weekly-changelog.yml'

jobs:
  weekly-changelog:
    if: github.repository == 'CleverRaven/Cataclysm-DDA'
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout"
        uses: actions/checkout@v4
      - name: "Get current date"
        uses: josStorer/get-current-time@v2
        id: current-date
        with:
          format: 'YYYYMMDD'
      - name: "Get changes since last week"
        id: getting-changes
        env:
          CURR_DATE: ${{ steps.current-date.outputs.formattedTime }}
        run: |
          YESTERDAY="$(date -d "@$(( $(date +%s -d $CURR_DATE) - (60*60*24*1) ))" +"%Y-%m-%d")"
          LAST_WEEK="$(date -d "@$(( $(date +%s -d $CURR_DATE) - (60*60*24*7) ))" +"%Y-%m-%d")"
          tools/generate_changelog.py -f --by-date /tmp/changelog.txt --end-date "$YESTERDAY" -T "${{ secrets.GITHUB_TOKEN }}" "$LAST_WEEK"
          tac /tmp/changelog.txt > /tmp/changelog_rev.txt
          tools/merge_summaries.sh /tmp/changelog_rev.txt data/changelog.txt
          echo "date=$( date +"%Y-%m-%d" -d $CURR_DATE )" >> $GITHUB_OUTPUT
          echo "old_date=$LAST_WEEK" >> $GITHUB_OUTPUT
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: Weekly Changelog ${{ steps.getting-changes.outputs.old_date }} to ${{ steps.getting-changes.outputs.date }}
          committer: David Seguin <<EMAIL>>
          author: David Seguin <<EMAIL>>
          token: ${{ secrets.TX_PR_CREATOR }}
          branch: changelog-weekly-${{ steps.getting-changes.outputs.date }}
          delete-branch: true
          base: master
          title: Weekly Changelog ${{ steps.getting-changes.outputs.old_date }} to ${{ steps.getting-changes.outputs.date }}
          body: "#### Summary\nNone\n\n#### Additional context\n`Automatically generated as a draft. Please copy-edit before merging.`"
          labels: Organization,<Documentation>
          # create as a draft to allow maintainers to cull the changes before merging
          draft: true

