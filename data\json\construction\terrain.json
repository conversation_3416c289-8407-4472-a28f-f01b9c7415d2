[{"type": "construction", "id": "constr_excavate_forestfloor", "group": "constr_excavate_forestfloor", "category": "CONSTRUCT", "required_skills": [["survival", 1]], "time": "5 m", "qualities": [[{"id": "DIG", "level": 1}]], "byproducts": [{"item": "twig", "count": [2, 10]}, {"item": "leaves", "count": [10, 50]}], "pre_terrain": "t_forestfloor", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_exhume", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "17m", "on_display": true, "qualities": [{"id": "DIG", "level": 2}], "pre_terrain": "t_grave", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_soil_loam_50L", "count": 5}], "post_special": "done_dig_grave", "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_exhume"}, {"type": "construction", "id": "constr_exhume_new", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "17m", "on_display": true, "qualities": [{"id": "DIG", "level": 2}], "pre_terrain": "t_grave_new", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_soil_loam_50L", "count": 5}], "post_special": "done_dig_grave_nospawn", "post_flags": ["keep_items"], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_exhume"}, {"type": "construction", "id": "constr_fill_dirmound", "group": "fill_pit_with_dirt", "//": "Fills a dirt mound with dirt.", "category": "CONSTRUCT", "required_skills": [["fabrication", 0]], "time": "5 m", "qualities": [[{"id": "DIG", "level": 1}]], "dark_craftable": true, "pre_terrain": "t_dirtmound", "post_terrain": "t_dirt", "do_turn_special": "do_turn_shovel", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_fill_pit", "group": "fill_pit_with_dirt", "//": "Fills a deep pit with dirt.", "category": "CONSTRUCT", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "DIG", "level": 1}]], "components": [[["material_soil", 350]]], "dark_craftable": true, "pre_terrain": "t_pit", "post_terrain": "t_pit_shallow", "do_turn_special": "do_turn_shovel", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_fill_pit_shallow", "group": "fill_pit_with_dirt", "//": "Fills a shallow pit with dirt.", "category": "CONSTRUCT", "required_skills": [["fabrication", 0]], "time": "10 m", "qualities": [[{"id": "DIG", "level": 1}]], "components": [[["material_soil", 50]]], "dark_craftable": true, "pre_terrain": "t_pit_shallow", "post_terrain": "t_dirt", "do_turn_special": "do_turn_shovel", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_fill_pit_trap", "group": "fill_pit_with_dirt", "//": "Fills a trap pit with dirt.", "category": "CONSTRUCT", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "DIG", "level": 1}]], "components": [[["material_soil", 50]]], "dark_craftable": true, "pre_flags": "PIT_FILLABLE", "post_terrain": "t_dirt", "do_turn_special": "do_turn_shovel", "activity_level": "EXTRA_EXERCISE"}, {"type": "construction", "id": "constr_fill_recess", "group": "fill_recess_with_dirt", "category": "CONSTRUCT", "required_skills": [["survival", 0]], "time": "60 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["material_soil", 10]]], "pre_terrain": "t_puddle", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_fill_sewage", "group": "fill_shallow_water_with_dirt", "category": "CONSTRUCT", "required_skills": [["survival", 1]], "time": "60 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["material_soil", 60]]], "pre_terrain": "t_sewage", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_fill_swater_sh", "group": "fill_salt_water_with_dirt", "category": "CONSTRUCT", "required_skills": [["survival", 1]], "time": "60 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["material_soil", 60]]], "pre_terrain": "t_swater_sh", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_fill_water_murky", "group": "fill_shallow_water_with_dirt", "category": "CONSTRUCT", "required_skills": [["survival", 1]], "time": "60 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["material_soil", 60]]], "pre_terrain": "t_water_murky", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_fill_water_sh", "group": "fill_shallow_water_with_dirt", "category": "CONSTRUCT", "required_skills": [["survival", 1]], "time": "60 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["material_soil", 60]]], "pre_terrain": "t_water_sh", "post_terrain": "t_dirt"}, {"type": "construction", "id": "constr_improvised_shelter", "group": "build_improvised_shelter", "category": "CONSTRUCT", "required_skills": [["survival", 2]], "time": "60 m", "qualities": [[{"id": "CUT", "level": 1}], [{"id": "HAMMER", "level": 1}]], "components": [[["stick", 12], ["2x4", 6], ["stick_long", 6], ["wood_panel", 1]], [["pine_bough", 24], ["willowbark", 24]]], "pre_terrain": "t_pit_shallow", "post_terrain": "t_improvised_shelter"}, {"type": "construction", "id": "constr_improvised_shelter_filled", "group": "build_improvised_shelter", "category": "CONSTRUCT", "required_skills": [["survival", 2]], "time": "3 m", "qualities": [], "components": [[["withered", 80], ["straw_pile", 80], ["leaves", 80]]], "pre_terrain": "t_improvised_shelter", "post_terrain": "t_improvised_shelter_filled"}, {"type": "construction", "id": "constr_pit_glass", "group": "glass_pit", "category": "DIG", "required_skills": [["survival", 1]], "time": "5 m", "components": [[["glass_shard", 168]]], "pre_terrain": "t_pit", "post_terrain": "t_pit_glass"}, {"type": "construction", "id": "constr_open_air", "skill": "survival", "group": "construct_open_air", "category": "CONSTRUCT", "difficulty": 0, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 2}], "pre_terrain": "t_pit", "post_terrain": "t_open_air", "byproducts": [{"group": "digging_soil_loam_50L", "count": 5}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit", "skill": "survival", "group": "dig_a_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "119m", "on_display": true, "qualities": [{"id": "DIG", "level": 2}], "pre_terrain": "t_pit_shallow", "pre_special": "check_empty_lite", "post_terrain": "t_pit", "byproducts": [{"group": "digging_soil_loam_50L", "count": 35}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit_shallow_sand", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 1}], "pre_flags": ["DIGGABLE", "FLAT"], "pre_special": "check_empty", "pre_terrain": "t_sand", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_sand_50L", "count": 1}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit_shallow_clay", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 1}], "pre_flags": ["DIGGABLE", "FLAT"], "pre_special": "check_empty", "pre_terrain": "t_clay", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_clay_50L", "count": 1}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit_shallow_gravel", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 1}], "pre_flags": ["DIGGABLE", "FLAT"], "pre_special": "check_empty", "pre_terrain": "t_railroad_rubble", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_gravel_50L", "count": 1}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit_shallow", "skill": "survival", "group": "dig_a_shallow_pit", "category": "CONSTRUCT", "difficulty": 0, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 1}], "//": "We specifically require the PLOWABLE flag here so that some diggable terrain will NOT match this construction recipe, and will instead refer to its specific variant (e.g. constr_pit_shallow_sand)", "pre_flags": ["DIGGABLE", "FLAT", "PLOWABLE"], "pre_special": "check_empty", "post_terrain": "t_pit_shallow", "byproducts": [{"group": "digging_soil_loam_50L", "count": 4}, {"group": "digging_topsoil_loam_50L", "count": 1}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_pit_spiked", "group": "spike_pit", "category": "DIG", "required_skills": [["survival", 1]], "time": "5 m", "components": [[["spear_wood", 4], ["pointy_stick", 4], ["pointy_stick_long", 4]]], "pre_terrain": "t_pit", "post_terrain": "t_pit_spiked"}, {"type": "construction", "id": "constr_pit_straw", "group": "pit_straw", "category": "DIG", "required_skills": [["survival", 0]], "time": "3 m", "components": [[["straw_pile", 60], ["withered", 60]]], "dark_craftable": true, "pre_terrain": "t_pit_shallow", "post_terrain": "t_pit_straw"}, {"type": "construction", "id": "constr_pit_underground", "group": "dig_a_pit", "category": "DIG", "qualities": [[{"id": "DIG", "level": 2}]], "required_skills": [["survival", 3]], "time": "300 m", "tools": [[["pickaxe_list", 1, "LIST"], ["jackhammer", 140], ["elec_jackhammer", 1848]]], "byproducts": [{"group": "mining_rock"}], "pre_terrain": "t_rock_floor", "post_terrain": "t_pit", "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}, {"type": "construction", "id": "constr_water_channel", "skill": "survival", "group": "dig_a_water_channel", "category": "CONSTRUCT", "difficulty": 1, "time": "20m", "on_display": true, "qualities": [{"id": "DIG", "level": 1}], "pre_flags": ["DIGGABLE", "FLAT"], "pre_special": ["check_empty", "check_channel"], "post_terrain": "t_water_moving_sh", "byproducts": [{"group": "digging_soil_loam_50L", "count": 5}], "activity_level": "EXTRA_EXERCISE", "do_turn_special": "do_turn_shovel"}]