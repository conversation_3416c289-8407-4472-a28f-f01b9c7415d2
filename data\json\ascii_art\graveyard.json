[{"type": "ascii_art", "id": "ascii_rip_cross", "picture": ["               _______  ___", "              <       `/   |", "               >  _     _ (", "              |  |_) | |_) |", "              |  | \\ | |   |", "   ______.__<color_green>%</color>_|            |________  __", " _/                                \\|  |", "|                                       <", "|                                       |", "|                                       |", "|_____.-._____              _/|_________|", "              |            |", "              |            |", "              |           <", "              |            |", "              |   _        |", "              |__/         |", "             <color_green>%</color> / `--.      |<color_green>%</color>", "         <color_red>*</color> .<color_green>%%</color>|          -< <color_brown>@</color><color_green>%%%</color>", "         `\\<color_green>%</color>`<color_brown>@</color>|            |<color_brown>@@</color><color_green>%</color><color_brown>@</color><color_green>%%</color>", "       .<color_green>%%%</color><color_brown>@@@</color>|<color_green>%</color>     `   <color_green>%</color> <color_brown>@@@</color><color_green>%%</color><color_brown>@</color><color_green>%%%%</color>", "  _.<color_green>%%%%%%</color><color_brown>@@@@@@</color><color_green>%%%</color>__/\\<color_green>%</color><color_brown>@@</color><color_green>%%</color><color_brown>@@@@@@@</color><color_green>%%%%%%</color>"]}, {"type": "ascii_art", "id": "ascii_rip_inverted_cross", "picture": ["              _______  ___", "             |       \\/   |", "             |            |", "             |            |", "             |            |", "             |            |", "             |            |", "             |            |", "             |           <", "             |   _        |", "             |__/         |", "   _____.__%_|            |_________  _", " _/                                 \\| \\", "|                                       <", "|                                       |", "|                                       |", "|____.-._______           __/|__________|", "             <color_green>%</color> / `_-.   _  |<color_green>%</color>", "         <color_red>*</color> .<color_green>%%</color>|  |_) | |_)< <color_brown>@<color_green>%%%</color>", "         `\\<color_green>%</color>`<color_brown>@</color>|  | \\ | |   |<color_brown>@@</color><color_green>%@<color_green>%%</color>", "       .<color_green>%%%</color><color_brown>@@@</color>|<color_green>%</color>     `   <color_green>%</color> <color_brown>@@@</color><color_green>%%</color><color_brown>@</color><color_green>%%%%</color>", "  _.<color_green>%%%%%%</color><color_brown>@@@@@@</color><color_green>%%%</color>__/\\<color_green>%</color><color_brown>@@</color><color_green>%%</color><color_brown>@@@@@@@</color><color_green>%%%%%%</color>"]}, {"type": "ascii_art", "id": "ascii_tombstone", "picture": ["           _________  ____           ", "         _/         `/    \\_         ", "       _/      _     _      \\_.      ", "     _<color_green>%</color>\\      |_) | |_)       \\_     ", "   _/ \\/      | \\ | |           \\_   ", " _/                               \\_ ", "|                                   |", " )                                 < ", "|                                   |", "|                                   |", "|   _                               |", "|__/                                |", " / `--.                             |", "|                                  ( ", "|                                   |", "|                                   |", "|     <color_green>%</color>                         .   |", "|  <color_brown>@</color>`                            <color_green>%%</color> |", "| <color_green>%</color><color_brown>@</color><color_green>%</color><color_brown>@</color><color_green>%</color>\\                <color_red>*</color>      <color_green>%</color>`<color_green>%</color><color_brown>@</color><color_green>%</color>|", "<color_green>%%</color><color_brown>@@@</color>.<color_green>%</color><color_brown>@</color><color_green>%</color>\\<color_green>%%</color>            `\\  <color_green>%%</color>.<color_green>%%</color><color_brown>@@</color><color_green>%</color><color_brown>@</color>", "<color_brown>@</color><color_green>%</color><color_brown>@@</color><color_green>%%%%%</color><color_brown>@@@@@@</color><color_green>%%%%%%%%</color><color_brown>@@</color><color_green>%%</color><color_brown>@@@</color><color_green>%%%</color><color_brown>@</color><color_green>%%</color><color_brown>@</color>"]}, {"type": "ascii_art", "id": "ascii_portal_storm_win", "picture": ["                                         ", "    <color_light_blue>|</color>                                    ", "   <color_light_blue>-.-</color>                                  ", "    <color_light_blue>|</color>                                    ", "                                         ", "                                         ", "                             <color_yellow>|</color>           ", "                            <color_yellow>_._</color>          ", "                             <color_yellow>|</color>           ", "                                         ", "                                         ", "                                         ", "    <color_yellow>|</color>                                    ", "   <color_yellow>-.-</color>                                   ", "    <color_yellow>|</color>                                    ", "                                         ", "                                <color_light_blue>|</color>        ", "                               <color_light_blue>_._</color>       ", "                                <color_light_blue>|</color>        ", "                                         ", "                                         ", "                                         "]}, {"type": "ascii_art", "id": "ascii_mycus_death", "picture": ["                                         ", "                                         ", "                                         ", "                                         ", "                                         ", "    .    ,                     `  `      ", "     .        \\i       ||    . ,  ,  .  ", "     ,    .    \\l     // ` ,      `,    ", " ` ,   .    .   l\\   //  .  `.          ", "         ,  ,  ` l\\//   ,   .  `   ,  ` ", "    .    `  `   .///}\\..       , .      ", " `     .  .    /|/   ||\\  .      ,  .   ", "  `     .  `  l\\\\     |/| `   `    `   ", "  ,  `   , ,   \\\\     |//.    ,   `    ", " `      , .  `  \\\\   //.   .      ,`   ", "    .  .   .    \\|\\//\\i  `  `     `   ", "   .    `    , /./ | | .\\i   `  .   `   ", "      .`  ,  //!/| ||. ||\\l.  . ,       ", "  ,   .    /.//||/ |||  \\\\l        ,   ", "    ,    /./|/  \\  ||   \\\\   ,  ``   .", " .     ./|!/  @.|| @||@   ||\\        .. ", "<color_blue>_____</color>.|!||/|@.@//.@@.\\@@||@||!.@@.<color_blue>______</color>"]}, {"type": "ascii_art", "id": "ascii_marloss_death", "picture": ["                                         ", "                                         ", "                                         ", "                                         ", "            __..--._                     ", "`   ,     .`<color_yellow>OOOOo</color>   '._     .  . ,  `    ", "   .    ,'<color_yellow>oOOOo</color>    '    '_    '  .   `   ", "    .   |  .    .    <color_yellow>oo</color>  .'._      ' .   ", "    ,   '.  <color_yellow>Ooo</color>   .  <color_yellow>OOOo</color>   <color_yellow>o</color>'._         ", "` ,   .   '<color_yellow>OOOOO</color>    <color_yellow>oOOO</color>  , <color_yellow>OO</color>  '._   '  ", "              <color_yellow>O</color>.  ,    <color_yellow>o</color>    <color_yellow>oOo</color>   \\  `  ", "   .           /'.    <color_yellow>o</color>,   . <color_yellow>oOo</color>  .'   ` ", "`     .       , . '.  <color_yellow>ooOO</color>  ' <color_yellow>OO</color>  |  .   ", "   .         .  / / '.  <color_yellow>Oo</color>     <color_yellow>o</color> .'    ` ", " `          , ,. / , /'.__  ,    |  `  . ", " ,  `      |  / ,/ , '    '.___.'    .   ", "  .       ,   ,' / |                `  , ", "     .`  |  /, .  '                      ", " ,   .   | /  .  /                  ,  ` ", "   ,    /  '  /  |                ``    .", ".      . . ,  / \\                   .   ", "______/_/___/__._\\______________________"]}, {"type": "ascii_art", "id": "ascii_has_mutagen_death", "picture": ["      ==============================     ", "      |:.           <color_green>o</color>    |   <color_green>o</color>   .:|     ", "      |:..    <color_green>,</color>   <color_green>.</color>      /     ...:|     ", "      |:.    <color_green>.</color>     <color_green>O</color>     |\\      .:|    ", "      |:.     <color_green>o</color>         /|  \\    .:|    ", "      |:.    <color_green>O</color>                  ..:|     ", "      |:..                    <color_green>O</color>  .:|     ", "      |:.                    <color_green>o</color>   .:|     ", "      |:...                      .:|     ", "      |:..                      ..:|     ", "      |:.                        .:|     ", "      |:.                       ..:|     ", "      |:. <color_green>O</color>                    ...:|     ", "      |:.. <color_green>O</color>    <color_green>.</color>                .:|     ", "      |:. <color_green>o</color>      <color_green>O</color>   <color_green>.O</color>    <color_green>.</color>     .:|     ", "      |:.  <color_green>.      <color_green>o    <color_green>o</color>    <color_green>o</color>    .:|     ", "      |:...    <color_green>,</color>            <color_green>o</color>  ...:|     ", "      |:..    <color_green>.</color>       /    <color_green>O</color>     .:|     ", "      |:.    <color_green>o</color>      \\|      <color_green>O</color>   ..:|    ", "      |:.     <color_green>o</color>       |    <color_green>o</color>     .:|     ", "      |:.    <color_green>O</color>       /    <color_green>.</color>      .:|     ", "      ==============================     "]}, {"type": "ascii_art", "id": "ascii_thresh_medical_death", "picture": ["                                         ", "                  //  \\\\               ", "                 // <color_red>'</color>  \\\\              ", "                //<color_green>_</color><color_red>.</color><color_green>_</color><color_yellow>___</color>\\\\             ", "               //        \\\\            ", "              //     <color_red>.</color>    \\\\           ", "             ((<color_light_blue>_____</color><color_red>/</color>  <color_pink>____</color>))            ", "             \\\\     <color_red>'</color>      //          ", "              \\\\          //           ", "             . \\\\<color_green>-</color><color_red>` ;'</color><color_yellow>---</color>//            ", "               .'\\      //              ", "                 \\\\    //              ", "                  \\\\  //               ", "                   \\\\//                ", "                    //                   ", "                   //\\\\                ", "                  //  \\  <color_red>/</color>              ", "                 //<color_light_blue>_</color><color_red>;</color> <color_pink>_</color>\\<color_red>/</color>               ", "                //   <color_red>_</color>  \\\\             ", "               //<color_green>___</color><color_red>/ ,</color><color_yellow>__</color>\\\\            ", "              //     <color_red>/</color>    \\\\           ", "             ((            ))            "]}, {"type": "ascii_art", "id": "ascii_thresh_lizard_death", "picture": ["                                         ", "                                         ", "                                         ", "                                         ", "                                         ", "                <color_red>^    ^.</color>                  ", "               <color_red>/ \\ |  \\</color>                ", "            <color_red>  ,\\ \\/__/  .^.</color>            ", "             <color_red>, .\\/ <color_white>O</color>  \\/_/</color>             ", "           <color_red>. . .''._<color_white>O</color><color_red>_/</color>  <color_green>'.</color>              ", "              <color_red>/  /\\'.</color>      <color_green>\\</color>           ", "              <color_red>\\/  \\/</color><color_green>\\\\     '.</color>        ", "               <color_red>v   v</color>  <color_green>'.    |</color>           ", "                         <color_green>\\   \\</color>         ", "                          <color_green>\\   \\</color>        ", "                           <color_green>|   |</color>         ", "                           <color_green>|   |</color>         ", "                           <color_green>;  /</color>          ", "                          <color_green>; .'</color>           ", "                         <color_green>/ /</color>             ", "                       <color_green>.'_.</color>              ", "                      <color_green>//</color>                 "]}, {"type": "ascii_art", "id": "ascii_thresh_bird_death", "picture": ["                                         ", "                                         ", "                                         ", "              _.--'''--._                ", "           .,'           ',.             ", "          :: ,           , ::            ", "          `;  ,,,     ,,,  ;`            ", "          |.':_    ,    _:' |            ", "          `/ |#'.  ,  .'#| \\`           ", "           | |##| ,,, |##| |             ", "           | |##| ,,, |##| |             ", "           | |##/ ,,, \\##| |            ", "           '.;#'   ,   '#;.'             ", "            ;             ;              ", "             ,           ,               ", "             :     .     :               ", "              , ..   .  ,                ", "               \\ ... . /                ", "                \\...../                 ", "                 \\.../                  ", "                  \\./                   ", "                   v                     "]}]