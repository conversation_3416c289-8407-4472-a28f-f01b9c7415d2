[{"type": "ascii_art", "id": "atgm_heat", "picture": [" <color_dark_gray>______________", " •============]", "  [====()=====•", "  <color_green>│      -┬─╦╧.", "  │       │ ╠╤'", "  │      -┴╥╫│", "  │        ║║│", "  │<color_white>NOSE END</color>║║│", "  <color_yellow>│<color_dark_gray> ~|╡|~_╡</color>│││", "  │<color_dark_gray> / ││~ │</color>│││</color>", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │   <color_dark_gray>.--.</color> ║║│", "  │  <color_dark_gray>| D  |</color>║║│", "  │  <color_dark_gray>| 'î |</color>║║│", "  │   <color_dark_gray>'--'</color> ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │ <color_white>§§§</color>    ║║│", "  │ <color_white>§§§</color>    ║║│", "  │ <color_white>§§§ -=\\</color>║║├─┐", "  │ <color_white>§§§ -=/</color>║║║ │", "  │ <color_white>§§§</color>    ║║├┬┘", "  │ <color_white>§§§</color>    []=│", "  │   <color_white>§</color>    ║║├┘", "  │   <color_white>§</color>    ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  <color_red>│        │││", "  │        │││</color>", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", "  │        ║║│", " .╧════╤╤══╩╩╧.", " '╤════╧╧════╤'", " <color_dark_gray>~~~~~~~~~~~~~~"]}]