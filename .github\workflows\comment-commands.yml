name: "Comment Commands"

on:
  issue_comment:
    types:
      - created

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  ISSUE_NUMBER: ${{ github.event.issue.number }}

jobs:
  issue-comment-job:
    if: ${{ !github.event.issue.pull_request }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set-up Comvent
        uses: rytswd/comvent@main
        id: comvent
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          config-path: .github/comment-commands.yml

      - if: steps.comvent.outputs.confirm-bug != ''
        name: Handle confirmation command - Label
        run: |
          gh issue edit "$ISSUE_NUMBER" --remove-label "(S1 - Need Confirmation),stale"
          gh issue edit "$ISSUE_NUMBER" --add-label "(S2 - Confirmed)"
          gh issue reopen "$ISSUE_NUMBER"

      - if: steps.comvent.outputs.close-duplicate != ''
        name: Handle duplicate issue command - Label
        run: |
          gh issue edit "$ISSUE_NUMBER" --remove-label "(S1 - Need Confirmation),(S2 - Confirmed),stale"
          gh issue edit "$ISSUE_NUMBER" --add-label "(S3 - Duplicate)"

      - if: steps.comvent.outputs.close-invalid != ''
        name: Handle invalid issue command - Label
        run: |
          gh issue edit "$ISSUE_NUMBER" --remove-label "(S1 - Need Confirmation),(S2 - Confirmed),stale"
          gh issue edit "$ISSUE_NUMBER" --add-label "(S4 - Invalid)"

      - if: steps.comvent.outputs.good-first-issue != ''
        name: Handle good first issue command - Label
        run: |
          gh issue edit "$ISSUE_NUMBER" --remove-label "stale"
          gh issue edit "$ISSUE_NUMBER" --add-label "Good First Issue"
          gh issue reopen "$ISSUE_NUMBER"

      - if: steps.comvent.outputs.help-wanted != ''
        name: Handle help wanted command - Label
        run: |
          gh issue edit "$ISSUE_NUMBER" --remove-label "stale"
          gh issue edit "$ISSUE_NUMBER" --add-label "Help Wanted"
          gh issue reopen "$ISSUE_NUMBER"

  pr-comment-job:
    if: ${{ github.event.issue.pull_request }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set-up Comvent
        uses: rytswd/comvent@main
        id: comvent
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          config-path: .github/comment-commands.yml

      - if: steps.comvent.outputs.help-wanted != ''
        name: Handle help wanted command - Label
        run: |
          gh pr edit "$ISSUE_NUMBER" --remove-label "stale"
          gh pr edit "$ISSUE_NUMBER" --add-label "Help Wanted"
          gh pr reopen "$ISSUE_NUMBER"
