[{"type": "ascii_art", "id": "cashcard", "picture": ["", "", "", "       <color_white>╔═══════════════════╗", "       <color_white>║                   ║", "       <color_white>║</color> <color_yellow>╔═   ╔═╔═╗╔═║ ║</color>   <color_white>║", "       <color_white>║</color> <color_yellow>║═ ┼ ║ ║═║╚╗║═║</color>   <color_white>║", "       <color_white>║</color> <color_yellow>╚═   ╚═║ ║═╝║ ║</color>   <color_white>║", "       <color_white>║                   ║", "       <color_white>║        VISA       ║", "       <color_white>║                   ║", "       <color_white>║                   ║", "       <color_white>║ 555 993 55221 066 ║", "       <color_white>╚═══════════════════╝"]}, {"type": "ascii_art", "id": "mp3", "picture": ["", "", "<color_white>            /\\         /\\", "           /  \\       /  \\", "          /    \\     /    \\", "         /      \\   /      \\", "         ║       \\ /       ║", "      ___║        |        ║___", "     |   /        |        \\   |", "     |__/         |         \\__|", "                  |", "                  |", "                  |", "     ╔═══════════════════════════╗", "     ║  ╔═════════════════════╗  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ║                     ║  ║", "     ║  ╚═════════════════════╝  ║", "     ║                           ║", "     ║         .........         ║", "     ║      ...         ...      ║", "     ║     .     MENU      .     ║", "     ║    .      .....      .    ║", "     ║   .      .     .      .   ║", "     ║   .  ⏮  .   ⏯   .  ⏭  .   ║", "     ║   .      .     .      .   ║", "     ║    .      .....      .    ║", "     ║     .               .     ║", "     ║      ...   VOL   ...      ║", "     ║         .........         ║", "     ║                           ║", "     ╚═╦═════════════════════════╝", "       ║          |", "       \\          |", "        \\        /", "         \\      /", "          \\    /", "           \\  /", "            \\/"]}, {"type": "ascii_art", "id": "lajatang", "picture": ["<color_light_gray>", "     .                  .", "    ..                  ..", "    ..                  ..", "    ...                ...", "    ....              ....", "     ....            ....", "       .....      .....", "         ............", "           ........</color>", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||", "              <color_brown>||</color>", "           <color_light_gray>........", "         ............", "       .....      .....", "     ....            ....", "    ....              ....", "    ...                ...", "    ..                  ..", "    ..                  ..", "     .                  ."]}, {"type": "ascii_art", "id": "wristwatch", "picture": ["", "             _____", "            /     \\", "            │  o  │", "            │     │", "            │  o  │", "            │     │", "            │  o  │", "            │     │", "            │  o  │", "            │     │", "            │  o  │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "           .........", "        ...   12    ...", "       .   11  │   1   .", "      . 10     │      2 .", "     .         │         .", "     . 9       └───     3.", "     .                   .", "      . 8             4 .", "       .   7       5   .", "        ...    6    ...", "           .........", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            │     │", "            └─────┘", "            ║  ║  ║", "            ╚══╩══╝"]}, {"type": "ascii_art", "id": "memory_card", "picture": ["", "", "     <color_white>   ______________", "       /</color><color_yellow>  ╔╗╔╗╔╗╔╗╔╗╔╗</color><color_white>|", "      /</color><color_yellow>╔╗ ╚╝╚╝╚╝╚╝╚╝╚╝</color><color_white>|", "     /</color><color_yellow> ╚╝</color><color_white>             |", "     |                |", "     |                |", "     |                |", "     |                |", "     |                |", "     |</color><color_yellow> MADE IN CHINA</color><color_white>  |", "     |________________|"]}, {"type": "ascii_art", "id": "halberd", "picture": ["", "            <color_white>.", "            .", "            .", "            .", "           ...", "           ...", "           ...", "           ...      ...", "           ...     .......", "           ...    ..........", "          .....  ............", "         .....................", "        .......................", "       ........................", "      .........................", "     ...</color><color_brown>    ..</color><color_white>  ..............", "     .</color><color_brown>      ..</color><color_white>   ............", "     </color><color_brown>       ..</color><color_white>    ..........", "     </color><color_brown>       ..</color><color_white>     .......", "     </color><color_brown>       ..</color><color_white>      ...", "     </color><color_brown>       ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            ..", "            .."]}, {"type": "ascii_art", "id": "1st_aid", "picture": ["", "", "  <color_white>           ╔═══════════╗", "  </color><color_red>  ═════════</color><color_white>║</color><color_red>═══════════</color><color_white>║</color><color_red>════════════╗", "   /</color><color_white>         ║           ║</color><color_red>          / ║", "   ╔═════════╩═══════════╩═════════╗  ║", "   ║                               ║  ║", "   ║</color><color_white>             ╔═══╗</color><color_red>             ║  ║", "   ║</color><color_white>             ║   ║</color><color_red>             ║  ║", "   ║</color><color_white>         ╔═══╝   ╚═══╗</color><color_red>         ║  ║", "   ║</color><color_white>         ║           ║</color><color_red>         ║  ║", "   ║</color><color_white>         ╚═══╗   ╔═══╝</color><color_red>         ║  ║", "   ║</color><color_white>             ║   ║</color><color_red>             ║  ║", "   ║</color><color_white>             ╚═══╝</color><color_red>             ║  ║", "   ║                               ║ /", "   ╚═══════════════════════════════╝"]}, {"type": "ascii_art", "id": "thermometer", "picture": ["", "", "     <color_white>        :::::::::", "          :::         :::", "        ::      ┌─┐      ::", "       :120_____│ │------50:", "      :     ____│ │----     :", "     :      ____│ │----      :", "     :100 ______│ │------ 40 :", "     :      ____│ │----      :", "     :      ____│ │----      :", "     : 80 ______│ │------ 30 :", "     :      ____│ │----      :", "     :      ----│ │----      :", "     :      ----│</color><color_red>█</color><color_white>│------ 20 :", "     : 60 ------│</color><color_red>█</color><color_white>│----      :", "     :      ----│</color><color_red>█</color><color_white>│----      :", "     :      ----│</color><color_red>█</color><color_white>│------ 10 :", "     :      ----│</color><color_red>█</color><color_white>│----      :", "     : 40 ------│</color><color_red>█</color><color_white>│----      :", "     :      ____│</color><color_red>█</color><color_white>│______ 0  :", "     :      ____│</color><color_red>█</color><color_white>│____      :", "     : 20 ______│</color><color_red>█</color><color_white>│____      :", "     :      ____│</color><color_red>█</color><color_white>│____      :", "     :      ____│</color><color_red>█</color><color_white>│------ -10:", "     :      ____│</color><color_red>█</color><color_white>│----      :", "     :  0 ------│</color><color_red>█</color><color_white>│----      :", "     :          │</color><color_red>█</color><color_white>│------ -20:", "     :          │</color><color_red>█</color><color_white>│          :", "     :          │</color><color_red>█</color><color_white>│          :", "     :  °F      │</color><color_red>█</color><color_white>│      °C  :", "     :          │</color><color_red>█</color><color_white>│          :", "     :          │</color><color_red>█</color><color_white>│          :", "     :::::::::::::::::::::::::"]}, {"type": "ascii_art", "id": "umbrella", "picture": ["", "", "                </color><color_white>:::::::::::</color><color_black>", "           </color><color_white>:::::</color><color_yellow>###########</color><color_white>:::::</color><color_black>", "         </color><color_white>::</color><color_light_red>####</color><color_white>:</color><color_yellow>###########</color><color_white>:</color><color_light_green>####</color><color_white>::</color><color_black>", "       </color><color_white>::</color><color_light_red>#####</color><color_white>:</color><color_yellow>#############</color><color_white>:</color><color_light_green>#####</color><color_white>::</color><color_black>", "      </color><color_white>:</color><color_light_red>######</color><color_white>:</color><color_yellow>###############</color><color_white>:</color><color_light_green>######</color><color_white>:</color><color_black>", "     </color><color_white>:</color><color_light_red>#######</color><color_white>:</color><color_yellow>###############</color><color_white>:</color><color_light_green>#######</color><color_white>:</color><color_black>", "    </color><color_white>:</color><color_light_red>#######</color><color_white>:</color><color_yellow>#################</color><color_white>:</color><color_light_green>#######</color><color_white>:</color><color_black>", "   </color><color_white>:</color><color_light_red>##</color><color_white>:::::::</color><color_yellow>#####</color><color_white>:::::::</color><color_yellow>#####</color><color_white>:::::::</color><color_light_green>##</color><color_white>:</color><color_black>", "  </color><color_white>:</color><color_light_red>#</color><color_white>::</color><color_black>       </color><color_white>::</color><color_yellow>#</color><color_white>::</color><color_black>  </color><color_brown>I|I</color><color_black>  </color><color_white>::</color><color_yellow>#</color><color_white>::</color><color_black>       </color><color_white>::</color><color_light_green>#</color><color_white>:</color><color_black>", "  </color><color_white>:::</color><color_black>         </color><color_white>:::</color><color_black>   </color><color_brown>I|I</color><color_black>   </color><color_white>:::</color><color_black>         </color><color_white>:::</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                    </color><color_brown>I|I</color><color_black>", "                </color><color_brown>J</color><color_black>   </color><color_brown>JJJ</color><color_black>", "                </color><color_brown>JJJJJJJ</color><color_black>", "                 </color><color_brown>JJJJJ</color><color_black>"]}, {"type": "ascii_art", "id": "badge_deputy", "picture": ["", "                   ⚪", "                  / \\", "                 /   \\", "                /     \\", "      ⚪ _______/       \\_______ ⚪", "       \\                        /", "        \\                      /", "         \\    ════════════    /", "          \\      DEPUTY      /", "         /       SHERIFF     \\", "        /     ════════════    \\", "       /                       \\", "      /_______           _______\\", "     ⚪         \\       /         ⚪", "                \\     /", "                 \\   /", "                  \\ /", "                   ⚪"]}, {"type": "ascii_art", "id": "textbook_chemistry", "picture": ["", "   <color_blue>/════════════════════════════════════╗</color><color_black>", "  </color><color_blue>/</color><color_white>───────────────────────────────────┐│</color><color_blue>║</color><color_black>", " </color><color_blue>/</color><color_white>───────────────────────────────────┐││</color><color_blue>║</color><color_black>", "</color><color_blue>╔═══════════════════════════════════╗</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║</color><color_black>  </color><color_yellow>    Textbook by K.M. Pozdro    </color><color_black>  </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║</color><color_black></color>     ╔═╗╦ ╦╔═╗╔╦╗╦╔═╗╔╦╗╦═╗╦ ╦     <color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║</color><color_black></color>     ║  ╠═╣║╣ ║║║║╚═╗ ║ ╠╦╝╚╦╝     <color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║</color><color_black></color>     ╚═╝╩ ╩╚═╝╩ ╩╩╚═╝ ╩ ╩╚═ ╩      <color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                        </color><color_white>_____</color><color_black>     </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                             </color><color_white>\\</color><color_yellow>\\</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                              </color><color_white>\\</color><color_yellow>\\</color><color_black>  </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                </color><color_white>/</color><color_yellow>/</color><color_black>            </color><color_white>/</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>               </color><color_white>/</color><color_yellow>/</color><color_black>            </color><color_white>/</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>\\</color><color_black>             </color><color_white>\\</color><color_black>             </color><color_white>\\</color><color_yellow>\\</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>\\</color><color_black>             </color><color_white>\\_____</color><color_black>        </color><color_white>\\</color><color_yellow>\\</color><color_black>  </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>/</color><color_black>       </color><color_yellow>/</color><color_white>/</color><color_yellow>/</color><color_black>   </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>/</color><color_black>       </color><color_yellow>/</color><color_white>/</color><color_yellow>/</color><color_black>   </color><color_white>/</color><color_black>       </color><color_white>\\</color><color_black>     </color><color_white>/</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>      </color><color_yellow>/</color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>\\_____/</color><color_black>       </color><color_white>\\____</color><color_yellow>/</color><color_white>/</color><color_black>       </color><color_white>\\</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>             </color><color_white>/</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>/</color><color_black>       </color><color_white>\\_____/</color><color_black>        </color><color_white>_____/</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_yellow>/</color><color_black>    </color><color_white>\\</color><color_black>             </color><color_white>\\</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>\\_____/</color><color_yellow>/</color><color_black>      </color><color_white>\\_____</color><color_black>        </color><color_white>\\</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>        </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>    </color><color_yellow>\\</color><color_white>\\</color><color_black>       </color><color_white>/</color><color_yellow>/</color><color_black>  </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>         </color><color_white>\\_____/</color><color_black>      </color><color_yellow>\\</color><color_white>\\</color><color_black>     </color><color_white>/</color><color_yellow>/</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>\\_____/</color><color_black>       </color><color_white>\\_____/</color><color_black>       </color><color_white>\\</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>  </color><color_white>/</color><color_black>     </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>             </color><color_white>/</color><color_black>   </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black> </color><color_white>/</color><color_black>       </color><color_white>\\_____/</color><color_black>        </color><color_white>_____/</color><color_black>    </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>               </color><color_white>\\</color><color_black>       </color><color_white>/</color><color_black>          </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                </color><color_white>\\_____/</color><color_black>           </color><color_blue>║</color><color_white>│││</color><color_blue>║</color><color_black>", "</color><color_blue>║ </color><color_black>                                  </color><color_blue>║</color><color_white>││/</color><color_black>", "</color><color_blue>║ </color><color_black>                                  </color><color_blue>║</color><color_white>│/</color><color_black>", "</color><color_blue>╚═══════════════════════════════════╝</color>"]}, {"type": "ascii_art", "id": "electrohack", "picture": ["", "                     </color><color_light_gray>┌────┐</color><color_black>", "                     </color><color_light_gray>│□</color><color_black>  </color><color_light_gray>□│</color><color_black>", "                     </color><color_light_gray>│</color><color_black>    </color><color_light_gray>│</color><color_black>", "      </color><color_yellow>─</color><color_white>╔════════════════════════╗</color><color_black>", "      </color><color_yellow>─</color><color_white>║</color><color_green>00</color><color_light_green>11</color><color_green>000</color><color_light_green>1</color><color_green>00</color><color_light_green>11</color><color_green>00</color><color_light_green>11</color><color_green>00</color><color_light_green>11</color><color_green>00</color><color_light_green>11</color><color_white>║</color><color_black>", "      </color><color_yellow>─</color><color_white>║</color><color_green>00</color><color_light_green>11</color><color_green>0</color><color_light_green>111</color><color_green>0</color><color_light_green>1</color><color_green>00</color><color_light_green>1</color><color_green>00000</color><color_light_green>11</color><color_green>0</color><color_light_green>1</color><color_green>00</color><color_white>║</color><color_black>", "      </color><color_yellow>─</color><color_white>║</color><color_green>0</color><color_light_green>1111</color><color_green>00000</color><color_light_green>11</color><color_green>00000</color><color_light_green>111</color><color_green>00</color><color_light_green>1</color><color_green>0</color><color_white>║</color><color_black>", "       </color><color_white>╚════════════════════════╝</color><color_black>", "        </color><color_yellow>╚╝╚╝╚╝</color><color_black>             </color><color_red>║</color><color_black> </color><color_yellow>║</color><color_black> </color><color_blue>║</color><color_black>", "                           </color><color_red>║</color><color_black> </color><color_yellow>║</color><color_black> </color><color_blue>║</color><color_black>", "                           </color><color_red>║</color><color_black> </color><color_yellow>║</color><color_black> </color><color_blue>╚══</color><color_light_gray>──</color><color_black>", "                           </color><color_red>║</color><color_black> </color><color_yellow>╚════</color><color_light_gray>──</color><color_black>", "                           </color><color_red>╚══════</color><color_light_gray>──</color><color_black>"]}, {"type": "ascii_art", "id": "manual_first_aid", "picture": ["<color_red>   /════════════════════════════════════╗</color>", "<color_red>  /</color><color_white>───────────────────────────────────┐│</color><color_red>║</color>", "<color_red> /</color><color_white>───────────────────────────────────┐││</color><color_red>║</color>", "<color_red>╔═══════════════════════════════════╗</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color> <color_light_cyan>╔╦╗╦ ╦╔═╗  ╔╗ ╦╔═╗  ╔╗ ╔═╗╔═╗╦╔═</color>  <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>  <color_light_cyan>║ ╠═╣║╣   ╠╩╗║║ ╦  ╠╩╗║ ║║ ║╠╩╗ </color> <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>  <color_light_cyan>╩ ╩ ╩╚═╝  ╚═╝╩╚═╝  ╚═╝╚═╝╚═╝╩ ╩ </color> <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>  <color_light_cyan>╔═╗╔═╗  ╔═╗╦╦═╗╔═╗╔╦╗  ╔═╗╦╔╦╗  </color> <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>  <color_light_cyan>║ ║╠╣   ╠╣ ║╠╦╝╚═╗ ║   ╠═╣║ ║║  </color> <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>  <color_light_cyan>╚═╝╚    ╚  ╩╩╚═╚═╝ ╩   ╩ ╩╩═╩╝  </color> <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>╔═════╗</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>       <color_white>╔══════╝     ╚══════╗</color>       <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>       <color_white>║                   ║</color>       <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>       <color_white>║                   ║</color>       <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>       <color_white>╚══════╗     ╔══════╝</color>       <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>║     ║</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>              <color_white>╚═════╝</color>              <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>             <color_cyan>By C. Red</color>             <color_red>║</color><color_white>│││</color><color_red>║</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>││/</color>", "<color_red>║</color>                                   <color_red>║</color><color_white>│/</color>", "<color_red>╚═══════════════════════════════════╝</color>"]}, {"type": "ascii_art", "id": "crude_picklock", "picture": ["                                         ", " <color_white>┤  ┼  )  {  /  ┬                        ", " │  │  │  │  │  │                        ", " </color><color_light_gray>▓  ▓  ▓  ▓  ▓  ▓                        ", " ▓  ▓  ▓  ▓  ▓  ▓                        ", " ▓  ▓  ▓  ▓  ▓  ▓                        ", "                                         "]}, {"type": "ascii_art", "id": "glasses_reading", "picture": ["                                         ", "     <color_white>▄▄         ▄▄                       ", "    </color><color_white>▓</color>  <color_white>▌       ▐  </color><color_white>▓</color>                      ", "   <color_white>▓</color>  <color_white>▐         ▌  </color><color_white>▓</color>                     ", "  <color_white>▓</color>                 <color_white>▓</color>                    ", " <color_white>▓</color>                   <color_white>▓</color>                   ", " <color_white>█</color> <color_white>▄▄▄▄▄▄▄</color>   <color_white>▄▄▄▄▄▄▄</color> <color_white>▓</color>                   ", "  <color_white>█</color>       <color_white>█▀█</color>       <color_white>▓</color>                    ", "  <color_white>█</color>      <color_white>█</color>   <color_white>█</color>      <color_white>▓</color>                    ", "   <color_white>▀▄▄▄▄▀</color>     <color_white>▀▄▄▄▄▀</color>                     ", "                                         "]}, {"type": "ascii_art", "id": "hammer", "picture": ["                                         ", "          <color_white>▄▄▄▄▄▄▄▄                       ", "       ▄████▀                            ", "     ▄█▒██▒                              ", "    ▄█████▒                              ", " ████▀  ▀███▄                            ", "  ▀██     ▀██</color><color_brown>█▄                          ", "            ████▄                        ", "             ▀████▄                      ", "              ▀█████▄                    ", "                ▀█████▄                  ", "                  ▀█████▄                ", "                    ▀████▓               ", "                      ▀██◛▓              ", "                        ▀▀▀              ", "                                         "]}, {"type": "ascii_art", "id": "knife_bread", "picture": ["                                         ", "                           <color_white>▐██▓▓</color>         ", "                         <color_white>▐███▓</color>           ", "                        <color_white>███▓▓</color>            ", "                      <color_white>▐███▓</color>              ", "                    <color_white>▐███▓▓</color>               ", "                   <color_white>████▓</color>                 ", "                 <color_white>▐███▓▓</color>                  ", "               <color_white>▐████▓</color>                    ", "              <color_white>████▓▓</color>                     ", "            <color_white>▐████▓</color>                       ", "           <color_white>████▓▓</color>                        ", "         <color_dark_gray>███</color><color_white>██▓</color>                          ", "       <color_dark_gray>█████</color><color_white>█▓</color>                           ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_butcher", "picture": ["                                         ", "                              <color_white>██</color>         ", "                            <color_white>████</color>         ", "                          <color_white>███▓▓█</color>         ", "                        <color_white>███▓▓▓▓█</color>         ", "                       <color_white>██▓▓▓▓▓▓█</color>         ", "                     <color_white>███▓▓▓▓▓░█</color>          ", "                   <color_white>███▓▓▓▓▓▓░█</color>           ", "                  <color_white>██▓▓▓▓▓▓▓░█</color>            ", "                <color_white>███▓▓▓▓▓▓▓░█</color>             ", "              <color_white>███▓▓▓▓▓▓▓▓░█</color>              ", "             <color_white>██▓▓▓▓▓▓▓▓▓░█</color>               ", "           <color_white>███▓▓▓▓▓▓▓▓▓░█</color>                ", "         <color_dark_gray>████</color><color_white>▓▓▓▓▓▓▓▓▓░█</color>                 ", "       <color_dark_gray>████▓▓</color><color_white>████▓▓▓▓░█</color>                  ", "      <color_dark_gray>████▓</color>     <color_white>██████</color>                   ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_carving", "picture": ["                                         ", "                          <color_white>▐█</color>             ", "                         <color_white>███</color>             ", "                       <color_white>▐█▓▓█</color>             ", "                      <color_white>██▓▓▓█</color>             ", "                    <color_white>▐█▓▓▓▓█</color>              ", "                   <color_white>██▓▓▓▓█</color>               ", "                 <color_white>▐█▓▓▓▓▓█</color>               ", "               <color_white>▐██▓▓▓▓▓█</color>                 ", "              <color_white>██▓▓▓▓▓█</color>                   ", "            <color_white>▐█▓▓▓▓▓█▌</color>                    ", "           <color_white>██▓▓▓▓█▌</color>                      ", "         <color_dark_gray>███</color><color_white>▓▓▓██</color>                        ", "       <color_dark_gray>█████</color><color_white>███▌</color>                         ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_meat_cleaver", "picture": ["                                         ", "                            <color_white>███</color>          ", "                          <color_white>██◛▓▓███</color>       ", "                        <color_white>██▓▓▓▓▓▓▓▓██▌</color>    ", "                       <color_white>█▓▓▓▓▓▓▓▓▓▓▓░██▌</color>  ", "                     <color_white>██▓▓▓▓▓▓▓▓▓▓░░██</color>    ", "                   <color_white>██▓▓▓▓▓▓▓▓▓▓░░██</color>      ", "                  <color_white>█▓▓▓▓▓▓▓▓▓▓▓░██</color>        ", "                <color_white>██▓▓▓▓▓▓▓▓▓▓░░█▌</color>         ", "              <color_white>██▓▓▓▓▓▓▓▓▓▓░░██</color>           ", "             <color_white>█▓▓▓▓▓▓▓▓▓▓▓░██▌</color>            ", "           <color_white>██▓▓▓▓▓▓▓▓▓▓░░█▌</color>              ", "         <color_dark_gray>███</color><color_white>▓▓▓▓▓▓▓▓▓░░██</color>                ", "       <color_dark_gray>█████</color><color_white>████▓▓▓▓░██▌</color>                 ", "      <color_dark_gray>████▓</color>     <color_white>██▓▓█▌</color>                   ", "    <color_dark_gray>████▓</color>         <color_white>██</color>                     ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_chef", "picture": ["                                         ", "                            <color_white>██</color>           ", "                          <color_white>███</color>            ", "                        <color_white>██░█</color>             ", "                      <color_white>██▓░█</color>              ", "                     <color_white>█▓▓░█</color>               ", "                   <color_white>██▓▓░█</color>                ", "                 <color_white>██▓▓▓░█</color>                 ", "               <color_white>██▓▓▓▓░█</color>                  ", "              <color_white>█▓▓▓▓▓░█</color>                   ", "            <color_white>██▓▓▓▓▓░█</color>                    ", "           <color_white>█▓▓▓▓▓▓░█</color>                     ", "         <color_dark_gray>███</color><color_white>▓▓▓▓▓░█</color>                      ", "       <color_dark_gray>█████</color><color_white>█████</color>                        ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_paring", "picture": ["                                         ", "                  <color_white>███</color>                    ", "                <color_white>▐██▓▓</color>                    ", "               <color_white>███▓▓</color>                     ", "             <color_white>▐██▓▓▓</color>                      ", "            <color_white>██▓▓▓▓</color>                       ", "          <color_white>▐██▓▓▓▓</color>                        ", "         <color_white>██▓▓▓▓▓</color>                         ", "       <color_dark_gray>████</color><color_white>████</color>                          ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_steak", "picture": ["                                         ", "                          <color_white>█</color>              ", "                         <color_white>█▓</color>              ", "                       <color_white>██▓</color>               ", "                     <color_white>██▓▓</color>                ", "                   <color_white>██▓▓▓</color>                 ", "                  <color_white>██▓▓</color>                   ", "                <color_white>███▓▓</color>                    ", "              <color_white>███▓▓</color>                      ", "            <color_white>████▓▓</color>                       ", "          <color_white>████▓▓</color>                         ", "         <color_dark_gray>██</color><color_white>██▓</color>                           ", "       <color_dark_gray>█████</color>                             ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "knife_vegetable_cleaver", "picture": ["                                         ", "                     <color_white>█████</color>               ", "                   <color_white>███◛█████</color>             ", "                 <color_white>▐███████████</color>            ", "                <color_white>██████████████</color>           ", "              <color_white>▐██████████████▓</color>           ", "             <color_white>██████████████▓▓</color>            ", "           <color_white>▐█████████████▓▓</color>              ", "          <color_white>█████████████▓▓▓</color>               ", "        <color_dark_gray>▐██</color><color_white>██</color>  <color_white>██████▓▓▓</color>                 ", "       <color_dark_gray>████</color>     <color_white>██▓▓▓▓</color>                   ", "      <color_dark_gray>████▓</color>                              ", "    <color_dark_gray>████▓</color>                                ", "  <color_dark_gray>████▓</color>                                  ", " <color_dark_gray>█</color> <color_dark_gray>█▓</color>                                    ", " <color_dark_gray>███</color>                                     ", "                                         "]}, {"type": "ascii_art", "id": "picklocks", "picture": ["                                         ", " <color_dark_gray>╔═══════════════════════════════════╗   ", " ║                                   ║   ", " ║                                   ║   ", " ║ </color><color_white>/ ╓ ╔ ╥                   ) { } 8 </color><color_dark_gray>║   ", " ║ </color><color_white>║ ║ ║ ║   ╣ ╬ %   * φ Φ   ║ ║ { ║ </color><color_dark_gray>║   ", " ║ </color><color_white>║ ║ ║ ║   ╠ ║ ║   ║ ║ ║   ║ ║ ║ ║ </color><color_dark_gray>║   ", " ║ </color><color_green>█ █ █ █   █ █ █   █ █ █   █ █ █ █ </color><color_dark_gray>║   ", " ║▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒║   ", " ║▓▒▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▒▓║   ", " ║▓▒▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▓▓▒▓▒▓▒▓▒▓║   ", " ║▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓║   ", " ╚═══════════════════════════════════╝   ", "                                         "]}, {"type": "ascii_art", "id": "pliers", "picture": ["                                         ", "      <color_white>█{▓                                ", "     █} {▓                               ", "    ██} {█▓                              ", "   ██}   {█▓                             ", "   ███} {██▓                             ", "   ████◙███▓                             ", "    ██████▓                              ", "     ████▓                               ", "      ██▓                                ", "    ██▓ ██▓                              ", "   █▓     █▓                             ", "   </color><color_dark_gray>█▓     █▓                             ", "  █</color><color_yellow>█▓     ██</color><color_dark_gray>▓                            ", "  █</color><color_yellow>█▓     ██</color><color_dark_gray>▓                            ", " ██▓       ██▓                           ", " █</color><color_yellow>█▓       ██</color><color_dark_gray>▓                           ", " █</color><color_yellow>█▓       ██</color><color_dark_gray>▓                           ", "  ██▓     ██▓                            ", "  █</color><color_yellow>█▓     ██</color><color_dark_gray>▓                            ", "  █</color><color_yellow>█▓     ██</color><color_dark_gray>▓                            ", "   █</color><color_yellow>█▓   ██</color><color_dark_gray>▓                             ", "   ██▓   ██▓                             ", "                                         "]}, {"type": "ascii_art", "id": "scalpel", "picture": ["                                         ", " <color_black>█</color><color_white>▌</color><color_black>█</color> <color_black>██</color>                                  ", " <color_white>▐█▒</color> <color_black>██</color> <color_black>█</color>                                ", " <color_white>███▒</color><color_black>██</color> <color_black>█</color>                                ", " <color_white>█</color><color_black_white>║</color><color_white>██▒</color><color_black>█</color>           <color_black>█</color>                      ", " <color_white>█</color><color_black_white>║</color><color_white>███▒</color>           <color_black>█</color>                      ", " <color_white>█</color><color_black_white>║</color><color_white>███▒</color>           <color_black>█</color>                      ", " <color_white>████▒</color><color_black>█</color>           <color_black>█</color>                      ", " <color_white>▐██▌</color><color_black>██</color>           <color_black>█</color>                      ", " <color_white>/</color><color_white>\\\\</color><color_white>▌</color><color_black>██</color>           <color_black>█</color>                      ", " <color_white>│</color><color_white>░░</color><color_white>\\</color><color_black>██</color>           <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░</color><color_white>\\</color>  <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░()░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░()░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color> <color_black>█</color>         <color_black>█</color>                      ", " <color_white>│</color><color_white>░░░░</color><color_white>│</color>           <color_black>█</color>                      ", " <color_black>█</color><color_white>(UU)</color>  <color_black>█</color>         <color_black>█</color>                      ", "                  <color_black>█</color>                      ", "                  <color_black>█</color>                      ", "                  <color_black>█</color>                      ", "                                         "]}, {"type": "ascii_art", "id": "scissors", "picture": ["                                         ", "                <color_white>█                        ", "                ░█                       ", "                ░█                       ", "                ░█                       ", "                ░█▌                      ", "                ░█▌      </color><color_dark_gray>████            ", "                </color><color_white>░██     </color><color_dark_gray>█    █           ", "               </color><color_white>▄███    </color><color_dark_gray>█     █           ", "  </color><color_white>▄▄▄▄▄▄█████████◙█████</color><color_dark_gray>██████            ", "                 </color><color_white>██                      ", "                 ██                      ", "               </color><color_dark_gray>███▌                      ", "              █  █                       ", "             █   █                       ", "             █   █                       ", "              ███                        ", "                                         "]}, {"type": "ascii_art", "id": "screwdriver", "picture": ["                                         ", "  <color_white>/|\\</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", "  <color_white>│█│</color>                                    ", " <color_light_gray>)</color><color_yellow>░</color><color_dark_gray>▓</color><color_yellow>░</color><color_light_gray>(</color>                                   ", " <color_dark_gray>▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌</color>                                   ", " <color_dark_gray>▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌</color>                                   ", " <color_dark_gray>▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌</color>                                   ", " <color_dark_gray>▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌</color>                                   ", " <color_dark_gray>▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌</color>                                   ", " <color_dark_gray>╙</color><color_yellow>╨</color><color_dark_gray>╨</color><color_yellow>╨</color><color_dark_gray>╜</color>                                   ", "                                         "]}, {"type": "ascii_art", "id": "screwdriver_set", "picture": ["<color_dark_gray>╔═══════════════════════════════════════╗", "║'''''''''''''''''''''''''''''''''''''''║", "║'''</color><color_white>/|\\</color><color_dark_gray>'''''''''''''''''''''''''''''''''║", "║'''</color><color_white>│█│</color><color_dark_gray>''''''''''''''''</color><color_white>┌─┐</color><color_dark_gray>''''''''''''''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>/|\\</color><color_dark_gray>''''''''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>┌─┐</color><color_dark_gray>''''''''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>''''''''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>''''''''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>''''''''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>''''''''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>/\\</color><color_dark_gray>'''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>┌┐</color><color_dark_gray>'''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''║", "║'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''║", "║''</color><color_light_gray>)</color><color_yellow>░</color><color_dark_gray>▓</color><color_yellow>░</color><color_light_gray>(</color><color_dark_gray>''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>''''</color><color_light_gray>)</color><color_yellow>░</color><color_dark_gray>▓</color><color_yellow>░</color><color_light_gray>(</color><color_dark_gray>''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''║", "║''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>''''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌''</color><color_white>│█│</color><color_dark_gray>'''</color><color_white>││</color><color_dark_gray>'''║", "║''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'</color><color_light_gray>)</color><color_yellow>░</color><color_dark_gray>▓</color><color_yellow>░</color><color_light_gray>(</color><color_dark_gray>'</color><color_light_gray>)</color><color_yellow>░░</color><color_light_gray>(</color><color_dark_gray>'''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'</color><color_light_gray>)</color><color_yellow>░</color><color_dark_gray>▓</color><color_yellow>░</color><color_light_gray>(</color><color_dark_gray>'</color><color_light_gray>)</color><color_yellow>░░</color><color_light_gray>(</color><color_dark_gray>''║", "║''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒▒</color><color_dark_gray>▌'''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒▒</color><color_dark_gray>▌''║", "║▓░▓▓▓▓▓░▓▓▓▓▓░▓▓▓▓░░░▓▓▓▓▓░▓▓▓▓▓░▓▓▓▓░▓║", "║''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒▒</color><color_dark_gray>▌'''▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒</color><color_dark_gray>█</color><color_yellow>▒</color><color_dark_gray>▌'▐</color><color_yellow>▒▒</color><color_dark_gray>▌''║", "║''╙</color><color_yellow>╨</color><color_dark_gray>╨</color><color_yellow>╨</color><color_dark_gray>╜'╙</color><color_yellow>╨</color><color_dark_gray>╨</color><color_yellow>╨</color><color_dark_gray>╜'╙</color><color_yellow>╨╨</color><color_dark_gray>╜'''╙</color><color_yellow>╨</color><color_dark_gray>╨</color><color_yellow>╨</color><color_dark_gray>╜'╙</color><color_yellow>╨</color><color_dark_gray>╨</color><color_yellow>╨</color><color_dark_gray>╜'╙</color><color_yellow>╨╨</color><color_dark_gray>╜''║", "║'''''''''''''''''''''''''''''''''''''''║", "╚═══════════════════════════════════════╝", "</color>                                         ", "                                         "]}, {"type": "ascii_art", "id": "bokken", "picture": ["                                         ", "                <color_brown>|\\                       ", "                | \\                      ", "                |  \\                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "               (____)                    ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     "]}, {"type": "ascii_art", "id": "katana", "picture": ["                                         ", "                <color_light_gray>|\\                       ", "                | \\                      ", "                |  \\                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "                |  |                     ", "               </color><color_dark_gray>(____)                    ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     ", "                [__]                     "]}, {"type": "ascii_art", "id": "morningstar", "picture": ["                                         ", "               <color_light_gray>,         ,               ", "         ,     ,,       ,,               ", "         ,,     ,,    ,,,       ,        ", "     ,    ,,,   ,,,  ,,,,      ,,        ", "     ,,    ,,, ,,,,,,,,,    ,,,,         ", "     ,,,,   ,,,,,,,,,,,,, ,,,,,          ", "      ,,,,,,,,,,,,,,,,,,,,,,,            ", "        ,,,,,,,,,,,,,,,,,,,,             ", "          ,,,,,,,,,,,,,,,,,              ", "        ,,,,,,,,,,,,,,,,,,,,             ", "     ,,,,,,,,,,,,,,,,,,,,,,,,,           ", "   ,,,,   ,,,,,,,,,,,,,,,   ,,,,         ", "        ,,, ,,,,,,,,,,,,,      ,,        ", "       ,,       </color><color_brown>|║║║|   </color><color_light_gray>,,,      ,       ", "       ,        </color><color_brown>|║║║|     </color><color_light_gray>,,             ", "                </color><color_brown>|║║║|      </color><color_light_gray>,             ", "                </color><color_brown>|║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    ", "                |║║║|                    "]}, {"type": "ascii_art", "id": "spear_stone", "picture": ["                    <color_light_gray>/\\                   ", "                   /  \\                  ", "                  /</color><color_white>\\  /</color><color_light_gray>\\                 ", "                  \\ </color><color_white>\\/ </color><color_light_gray>/                 ", "                   \\</color><color_white>/\\</color><color_light_gray>/                  ", "                    </color><color_brown>||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   ", "                    ||                   "]}]