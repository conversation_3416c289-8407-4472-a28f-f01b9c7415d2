[{"type": "furniture", "id": "f_pallet_brick", "name": "pallet of bricks", "looks_like": "t_brick_wall", "description": "A massive, packed-together block of bricks wrapped with plastic packaging.  It makes for easy storage of building materials for masonry constructions.", "symbol": "#", "color": "brown", "move_cost_mod": 5, "required_str": -1, "coverage": 60, "flags": ["TRANSPARENT", "NOITEM", "MOUNTABLE", "REDUCE_SCENT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "brick", "count": 400}, {"item": "pallet", "count": 1}, {"item": "plastic_sheet", "count": 1}]}, "bash": {"str_min": 30, "str_max": 100, "sound": "crash!", "sound_fail": "bash!", "items": [{"item": "pallet", "count": 1}, {"item": "brick", "count": [300, 350]}, {"item": "brick", "count": [30, 50], "damage": [1, 6]}, {"item": "plastic_sheet", "count": 1, "damage": [2, 5]}]}}, {"type": "furniture", "id": "f_pallet_brick_adobe", "name": "pallet of adobe bricks", "looks_like": "t_adobe_brick_wall", "description": "A massive, packed-together block of adobe bricks wrapped with plastic packaging.  It makes for easy storage of building materials for masonry constructions.", "symbol": "#", "color": "brown", "move_cost_mod": 5, "required_str": -1, "coverage": 60, "flags": ["TRANSPARENT", "NOITEM", "MOUNTABLE", "REDUCE_SCENT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "adobe_brick", "count": 400}, {"item": "pallet", "count": 1}, {"item": "plastic_sheet", "count": 1}]}, "bash": {"str_min": 30, "str_max": 100, "sound": "crash!", "sound_fail": "bash!", "items": [{"item": "pallet", "count": 1}, {"item": "adobe_brick", "count": 350}, {"item": "adobe_brick", "count": 50, "damage": [1, 4]}, {"item": "plastic_sheet", "count": 1, "damage": [2, 4]}]}}, {"type": "furniture", "id": "f_pallet_cement", "name": "pallet of cement bags", "looks_like": "material_cement", "description": "A massive, packed-together block of cement bags wrapped with plastic packaging.  It makes for easy storage of building materials for masonry constructions.", "symbol": "#", "color": "light_gray", "move_cost_mod": 5, "required_str": -1, "coverage": 60, "flags": ["TRANSPARENT", "NOITEM", "MOUNTABLE", "REDUCE_SCENT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"group": "cement_bag", "count": 64}, {"item": "pallet", "count": 1}, {"item": "plastic_sheet", "count": 1}]}, "bash": {"str_min": 30, "str_max": 100, "sound": "thump!", "sound_fail": "thud!", "items": [{"item": "material_cement", "container-item": "bag_canvas", "charges": 500, "count": [80, 90]}, {"item": "material_cement", "charges": [500, 1500]}, {"item": "bag_durasack", "count": [2, 10], "damage": [1, 5]}, {"item": "pallet", "count": 1}]}}, {"type": "furniture", "id": "f_stack_plank", "name": "stack of wooden planks", "looks_like": "bundle_plank", "description": "Dozens of construction planks tied together for easier transport.  Deconstruct to untie them.", "symbol": "#", "color": "light_gray", "move_cost_mod": 5, "required_str": -1, "coverage": 40, "flags": ["TRANSPARENT", "NOITEM", "MOUNTABLE", "REDUCE_SCENT", "EASY_DECONSTRUCT", "FLAMMABLE_ASH", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": 120}, {"item": "rope_6", "count": 2}, {"item": "pallet", "count": 1}]}, "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "2x4", "count": [90, 100]}, {"item": "2x4", "count": [2, 20]}, {"item": "pallet", "count": 1}, {"item": "rope_6", "count": [1, 2], "damage": [1, 5]}]}}]