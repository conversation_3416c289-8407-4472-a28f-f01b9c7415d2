[{"type": "furniture", "id": "f_indoor_plant", "name": "indoor plant", "symbol": "^", "color": "green", "move_cost_mod": 2, "description": "A small potted plant, used for decoration indoors.  It appears to have dried up and died a while ago.", "required_str": 5, "max_volume": "10 L", "flags": ["CONTAINER", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "TINY", "TRANSPARENT", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "withered"}, {"item": "wrapper", "prob": 5}, {"item": "can_drink", "prob": 5}, {"item": "bag_plastic", "prob": 5}, {"item": "cig_butt", "prob": 10, "count": [1, 10]}, {"group": "flower_pots"}]}, "bash": {"str_min": 2, "str_max": 18, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "withered", "prob": 50}, {"item": "wrapper", "prob": 5}, {"item": "can_drink", "prob": 5}, {"item": "bag_plastic", "prob": 5}, {"item": "cig_butt", "prob": 10, "count": [1, 10]}, {"group": "flower_pots_broken"}]}}, {"type": "furniture", "id": "f_indoor_plant_y", "name": "yellow indoor plant", "description": "A decorative potted plant with a yellow flower, it looks to have wilted and died some time ago.", "symbol": "^", "color": "yellow", "move_cost_mod": 2, "required_str": 5, "max_volume": "10 L", "flags": ["CONTAINER", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "TINY", "TRANSPARENT", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "withered"}, {"item": "wrapper", "prob": 5}, {"item": "can_drink", "prob": 5}, {"item": "bag_plastic", "prob": 5}, {"item": "cig_butt", "prob": 10, "count": [1, 10]}, {"group": "flower_pots"}]}, "bash": {"str_min": 2, "str_max": 18, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "withered", "prob": 50}, {"item": "wrapper", "prob": 5}, {"item": "can_drink", "prob": 5}, {"item": "bag_plastic", "prob": 5}, {"item": "cig_butt", "prob": 10, "count": [1, 10]}, {"group": "flower_pots_broken"}]}}, {"type": "furniture", "id": "f_plant_unharvested_overgrown", "looks_like": "t_grass_tall", "name": "overgrown plant", "description": "This patch of green used to be a cultivated field or some sort of monocrop agriculture.  Neglected for too long, it has been overtaken by nature.", "symbol": "#", "color": "red", "coverage": 50, "move_cost_mod": 2, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_OVERGROWN", "SMALL_HIDE"], "examine_action": "clear_overgrown", "bash": {"str_min": 4, "str_max": 10, "sound": "crunch.", "sound_fail": "whish."}, "plant_data": {"transform": "f_plant_unharvested_overgrown", "base": "f_null"}}, {"type": "furniture", "id": "f_plant_harvest", "name": "harvestable plant", "description": "This plant is fully grown and ready to be harvested.  Identifying how to harvest it requires closer examination.", "symbol": "#", "color": "light_green", "coverage": 30, "move_cost_mod": 1, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_HARVEST", "SMALL_HIDE"], "examine_action": "harvest_plant_ex", "bash": {"str_min": 4, "str_max": 10, "sound": "crunch.", "sound_fail": "whish."}, "plant_data": {"transform": "f_plant_unharvested_overgrown", "base": "f_null"}}, {"type": "furniture", "id": "f_plant_mature", "name": "mature plant", "description": "This plant has matured, and will be ready to harvest before long.", "symbol": "#", "color": "green", "move_cost_mod": 0, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_MATURE", "SMALL_HIDE"], "examine_action": "aggie_plant", "bash": {"str_min": 3, "str_max": 8, "sound": "crunch.", "sound_fail": "whish."}, "plant_data": {"transform": "f_plant_harvest", "base": "f_null"}}, {"type": "furniture", "id": "f_plant_seed", "name": "seed", "description": "A freshly planted seed.  If properly tended to, it could grow into a healthy plant.", "symbol": "^", "color": "brown", "move_cost_mod": 0, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN"], "examine_action": "aggie_plant", "bash": {"str_min": 1, "str_max": 5, "sound": "crunch.", "sound_fail": "whish."}, "plant_data": {"transform": "f_plant_seedling", "base": "f_null"}}, {"type": "furniture", "id": "f_plant_seedling", "name": "seedling", "description": "A seed that has just begun to sprout its first roots.", "symbol": "^", "color": "green", "move_cost_mod": 0, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_SEEDLING"], "examine_action": "aggie_plant", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}, "plant_data": {"transform": "f_plant_mature", "base": "f_null"}}, {"type": "furniture", "id": "f_planter", "name": "planter", "description": "A garden planter full of soil and slatted to allow adequate drainage.  Can be used for planting crops.", "symbol": "^", "color": "green", "move_cost_mod": 1, "required_str": 10, "looks_like": "t_dirtmound", "flags": ["TRANSPARENT", "PLANTABLE", "FLAT", "MOUNTABLE"], "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 6, "str_max": 14, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "2x4", "count": [4, 10]}, {"item": "nail", "charges": [15, 30]}, {"item": "pebble", "charges": [150, 200]}, {"item": "material_soil", "count": [60, 75]}]}, "plant_data": {"transform": "f_planter_seed"}, "examine_action": "dirtmound"}, {"type": "furniture", "id": "f_planter_unharvested_overgrown", "name": "planter with dead plants", "description": "Somewhere beneath this heap of dying plants you can see the shape of wooden furniture.  Left without care, the plants it used to nurture have shriveled and died.", "symbol": "#", "color": "red", "looks_like": "f_planter", "move_cost_mod": 3, "required_str": -1, "examine_action": "clear_overgrown", "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_OVERGROWN", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 2, "str_max": 4, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 1, "furn_set": "f_planter", "items": [{"item": "withered", "count": [1, 2]}]}, "plant_data": {"transform": "f_planter_unharvested_overgrown", "base": "f_planter"}}, {"type": "furniture", "id": "f_planter_harvest", "name": "planter with harvestable plant", "description": "A garden planter full of soil and slatted to allow adequate drainage.  This one has a fully grown plant, and will need closer examination to harvest.", "symbol": "#", "color": "light_green", "looks_like": "f_plant_harvest", "move_cost_mod": 0, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_HARVEST", "SMALL_HIDE"], "examine_action": "harvest_plant_ex", "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 6, "str_max": 10, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 8, "furn_set": "f_planter", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 16]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_planter_unharvested_overgrown", "base": "f_planter"}}, {"type": "furniture", "id": "f_planter_mature", "name": "planter with mature plant", "description": "A garden planter full of soil and slatted to allow adequate drainage.  This one has a matured plant that should be ready for harvest before long.", "symbol": "#", "color": "green", "looks_like": "f_plant_mature", "move_cost_mod": 0, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_MATURE", "SMALL_HIDE"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 6, "str_max": 10, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 8, "furn_set": "f_planter", "items": [{"item": "withered", "count": [2, 8]}, {"item": "leaves", "count": [4, 16]}, {"item": "twig", "count": [1, 5]}]}, "plant_data": {"transform": "f_planter_harvest", "base": "f_planter"}}, {"type": "furniture", "id": "f_planter_seed", "name": "planter with seed", "description": "A garden planter full of soil and slatted to allow adequate drainage.  This one contains a planted seed, and will need attention to grow fully.", "symbol": "^", "color": "brown", "looks_like": "f_plant_seed", "move_cost_mod": 1, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 2, "str_max": 6, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 4, "furn_set": "f_planter", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [1, 2]}]}, "plant_data": {"transform": "f_planter_seedling", "base": "f_planter"}}, {"type": "furniture", "id": "f_planter_seedling", "name": "planter with seedling", "description": "A garden planter full of soil and slatted to allow adequate drainage.  This one has a seed that has just begun to sprout its first roots.", "symbol": "^", "color": "green", "looks_like": "f_plant_seedling", "move_cost_mod": 1, "required_str": -1, "flags": ["PLANT", "SEALED", "TRANSPARENT", "CONTAINER", "NOITEM", "TINY", "DONT_REMOVE_ROTTEN", "GROWTH_SEEDLING"], "examine_action": "aggie_plant", "deconstruct": {"items": [{"item": "2x4", "count": [11, 12]}, {"item": "nail", "charges": [30, 36]}, {"item": "pebble", "charges": [180, 200]}, {"item": "material_soil", "count": [70, 75]}]}, "bash": {"str_min": 4, "str_max": 8, "sound": "rrrrip!", "sound_fail": "brush.", "sound_vol": 6, "furn_set": "f_planter", "items": [{"item": "withered", "count": [1, 2]}, {"item": "leaves", "count": [2, 6]}, {"item": "twig", "count": [0, 1]}]}, "plant_data": {"transform": "f_planter_mature", "base": "f_planter"}}, {"type": "furniture", "id": "f_stook_full", "name": "grain stook", "description": "Sheaves of cut grain have been stood upright in a self-supporting A-frame to dry, keeping the seeds off the ground and out of reach of vermin.", "symbol": "#", "color": "brown", "move_cost_mod": 1, "coverage": 40, "deconstruct": {"items": []}, "required_str": 3, "flags": ["SEALED", "TRANSPARENT", "CONTAINER", "EASY_DECONSTRUCT", "DONT_REMOVE_ROTTEN", "FLAMMABLE_ASH"], "examine_action": "stook_full", "bash": {"str_min": 3, "str_max": 60, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_stook_empty", "name": "stooking spot", "description": "A space has been cleared out here for grain drying.  Place fresh stalks of grain in this spot and examine to set up a stook.", "symbol": "#", "color": "brown", "move_cost_mod": 0, "deconstruct": {"items": []}, "max_volume": "10 L", "required_str": -1, "flags": ["TRANSPARENT", "CONTAINER", "EASY_DECONSTRUCT", "NOCOLLIDE"], "examine_action": "stook_empty"}]