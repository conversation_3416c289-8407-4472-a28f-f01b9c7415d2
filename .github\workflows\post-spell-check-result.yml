name: Post Spell Check Result


on:
  workflow_run:
    workflows: ["Text Changes Analyzer"]
    types:
      - completed


jobs:
  post-spell-check-result:
    runs-on: ubuntu-latest
    if: >
      ${{ github.event.workflow_run.event == 'pull_request' &&
      github.event.workflow_run.conclusion == 'success' }}
    steps:
      - name: Download pr id artifact
        uses: dawidd6/action-download-artifact@v3.1.4
        with:
          workflow: ${{ github.event.workflow_run.name }}
          run_id: ${{ github.event.workflow_run.id }}
          name: pull_request_id
      - name: Download spell check retcode artifact
        uses: dawidd6/action-download-artifact@v3.1.4
        with:
          workflow: ${{ github.event.workflow_run.name }}
          run_id: ${{ github.event.workflow_run.id }}
          name: spell_check_retcode
      - name: set-spell-check-retcode
        id: set-spell-check-retcode
        run: echo "spell-check-retcode=$( cat spell_check_retcode )" >> $GITHUB_OUTPUT
      - name: Download spell check output artifact
        if: steps.set-spell-check-retcode.outputs.spell-check-retcode >= 1
        uses: dawidd6/action-download-artifact@v3.1.4
        with:
          workflow: ${{ github.event.workflow_run.name }}
          run_id: ${{ github.event.workflow_run.id }}
          name: spell_check_output
      - name: 'Comment on PR'
        if: steps.set-spell-check-retcode.outputs.spell-check-retcode >= 1
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            var fs = require('fs');
            var issue_number = Number(fs.readFileSync('./pull_request_id'));
            var body = fs.readFileSync('./spell_check_output', 'utf8');
            console.log("Fetching comments of pull request %d of repository %s/%s.", issue_number, context.repo.owner, context.repo.repo);
            const comments = await github.paginate(github.rest.issues.listComments,
              {
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue_number
              }
            );
            const prev_spell_checks = comments.slice(0).reverse()
                                              .filter(comment => comment.user.type === 'Bot' && comment.user.login === 'github-actions[bot]')
                                              .filter(comment => comment.body.startsWith("Spell checker"));
            if (prev_spell_checks.some(comment => comment.body === body)) {
              console.log("The exact same spell checker comment already exists.  Exiting.");
              return;
            }
            for (const comment of prev_spell_checks) {
              console.log("Deleting previous spell checker comment (comment id: %i)", comment.id);
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: comment.id,
              });
            }
            console.log("Posting comment.");
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issue_number,
              body: body
            });
