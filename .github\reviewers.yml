files:
  'data/mods/Aftershock/**':
    - Maleclypse
    - <PERSON>-<PERSON>
  'data/mods/Backrooms/**':
    - onura46
  'data/mods/DinoMod/**':
    - LyleSY
  'data/mods/BombasticPerks/**':
    - bombasticSlacks
  'data/mods/MMA/**':
    - Hymore246
  'data/mods/classic_zombies/**':
    - I-am-Erk
  'data/mods/generic_guns/**':
    - tenmillimaster
  'data/mods/Magiclysm/**':
    - KorGgenT
  'data/mods/My_Sweet_Cataclysm/**':
    - Fris0uman
  'data/mods/ruralbiome/**':
    - I-am-Erk
  'data/mods/speedydex/**':
    - KorGgenT
  'data/mods/stats_through_kills/**':
    - KorGgenT
  'data/mods/Xedra_Evolved/**':
    - Maleclypse
  'data/mods/MindOverMatter/**':
    - Standing-Storm
  'data/mods/innawood/**':
    - Light-Wave
  'data/mods/MA/**':
    - <PERSON><PERSON><PERSON>Serg
  'data/mods/No_Hope/**':
    - Night-<PERSON>ryan<PERSON>
  'data/mods/Tamable_Wildlife/**':
    - Termineitor244
  'data/mods/Limb_WIP/**':
    - Venera3
  'tools/json_tools/gun_variant_validator.py':
    - anothersimulacrum

  '**/.clang-tidy':
    - jbytheway
  '**/magic*.cpp':
    - KorGgenT
  '**/magic*.h':
    - KorGgenT
  'tools/{spell_checker/!(dictionary.txt){,/**},!(spell_checker){,/**}}':
    - jbytheway
  'src/widget.cpp':
    - wapcaplet
    - dseguin
  'src/widget.h':
    - wapcaplet
    - dseguin
  'src/ui_manager.*':
    - Qrox
  'data/raw/keybindings{.json,/**}':
    - Qrox
  'src/math_parser*':
    - andrei8l
