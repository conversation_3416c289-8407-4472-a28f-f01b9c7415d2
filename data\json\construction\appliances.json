[{"type": "construction", "id": "ap_box_battery_charger", "group": "place_ap_box_battery_charger", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "2 m", "components": [[["box_battery_charger", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "ap_foot_locker_recharge_station", "group": "place_ap_foot_locker_recharge_station", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "4 m", "components": [[["foot_locker_recharge_station", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_active_backup_generator", "group": "place_active_backup_generator", "category": "APPLIANCE", "required_skills": [["mechanics", 1]], "time": "5 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["active_backup_generator", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_air_compressor", "group": "place_air_compressor", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["air_compressor", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_ammonia_machine_pipework", "group": "place_ammonia_machine_pipework", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["ammonia_machine_pipework", 1]]], "pre_special": "check_empty", "post_special": "done_appliance"}, {"type": "construction", "id": "app_ammonia_machine_reactor", "group": "place_ammonia_machine_reactor", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["ammonia_machine_reactor", 1]]], "pre_special": "check_empty", "post_special": "done_appliance"}, {"type": "construction", "id": "app_apartment_freezer", "group": "place_apartment_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["apartment_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_arcade_machine", "group": "place_arcade_machine", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["arcade_machine", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_asrg_containment", "group": "place_asrg_containment", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "5 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["compact_ASRG_containment", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_autoclave", "group": "place_autoclave", "category": "APPLIANCE", "required_skills": [["electronics", 2]], "time": "6 m", "components": [[["autoclave", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_bandsaw", "group": "place_bandsaw", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["bandsaw", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_battery", "group": "place_battery", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["storage_battery", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_battery_car", "group": "place_car_battery", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["battery_car", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_battery_large", "group": "place_large_battery", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["large_storage_battery", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_battery_medium", "group": "place_medium_battery", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["medium_storage_battery", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_battery_small", "group": "place_small_battery", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["small_storage_battery", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_big_portable_freezer", "group": "place_big_portable_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["big_portable_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_big_portable_fridge", "group": "place_big_portable_fridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["big_portable_fridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_catalytic_cracking_reactor", "group": "place_catalytic_cracking_reactor", "category": "APPLIANCE", "//1": "maybe some applied science? this seems to be some semi-advanced hardware", "required_skills": [["fabrication", 0]], "//2": "I set lower activity and longer time for putting a lot of light and/or small items together. When possible, I'd like it to take more time to disassemble as well.", "time": "40 m", "components": [[["catalytic_cracking_reactor", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_chest_freezer", "group": "place_chest_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["chest_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_dishwasher", "group": "place_dishwasher", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["household_dishwasher", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_display_freezer", "group": "place_display_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["display_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_display_fridge", "group": "place_display_fridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["display_fridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_drill_press", "group": "place_drill_press", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["drill_press", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_glassblowers_crucible", "group": "place_glassblowers_crucible", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["glassblowers_crucible", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_forge", "group": "place_electric_forge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["forge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_electrolyzer_makeshift", "group": "place_electrolyzer_makeshift", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["electrolyzer_makeshift", 1]]], "pre_special": "check_empty", "post_special": "done_appliance"}, {"type": "construction", "id": "app_fridge", "group": "place_apartment_fridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["apartment_fridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_glass_freezer", "group": "place_glass_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["glass_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_glass_fridge", "group": "place_glass_fridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["glass_fridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_glass_fridge_double", "group": "place_glass_fridge_double", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["glass_fridge_double", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_ground_solar", "group": "place_ground_solar_panel", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["ground_solar_panel", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_ground_solar_v2", "group": "place_ground_solar_panel_v2", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["ground_solar_panel_v2", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_hd_compressor_unit", "group": "place_hd_compressor_unit", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["hd_compressor_unit", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_heater_large", "group": "place_heater_large", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["large_space_heater", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_heater_small", "group": "place_heater_small", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["small_space_heater", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_heavy_duty_freezer", "group": "place_heavy_duty_freezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["heavy_duty_freezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_heavy_duty_fridge", "group": "place_heavy_duty_fridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["heavy_duty_fridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_hydraulic_press", "group": "place_hydraulic_press", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["hydraulic_press", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_jointer", "group": "place_jointer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["jointer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_kiln", "group": "place_kiln", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["kiln", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_microwave", "group": "place_microwave", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["microwave", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_minifreezer", "group": "place_minifreezer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["minifreezer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_minifridge", "group": "place_minifridge", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["minifridge", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_mitresaw", "group": "place_mitresaw", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["mitresaw", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_nitrogen_generator", "group": "place_nitrogen_generator", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["nitrogen_generator", 1]]], "pre_special": "check_empty", "post_special": "done_appliance"}, {"type": "construction", "id": "app_oxygen_concentrator", "group": "place_oxygen_concentrator", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["oxygen_concentrator", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_planer", "group": "place_planer", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["planer", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_oven", "group": "place_oven", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["oven", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_reinforced_solar", "group": "place_reinforced_solar_panel", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["reinforced_solar_panel", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_reinfoced_solar_v2", "group": "place_reinforced_solar_panel_v2", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["reinforced_solar_panel_v2", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_router", "group": "place_router", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["router", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_solar", "group": "place_solar_panel", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["solar_panel", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_solar_v2", "group": "place_solar_panel_v2", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "6 m", "qualities": [[{"id": "WRENCH", "level": 1}]], "components": [[["solar_panel_v2", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_standing_directed_floodlight", "group": "place_standing_directed_floodlight", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 s", "components": [[["standing_directed_floodlight", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "app_standing_floodlight", "group": "place_standing_floodlight", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 s", "components": [[["standing_floodlight", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "app_standing_lamp", "group": "place_standing_lamp", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 s", "components": [[["standing_lamp", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "app_tablesaw", "group": "place_tablesaw", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["tablesaw", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_stereo", "group": "place_stereo", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["stereo", 1]]], "pre_special": "check_empty", "post_special": "done_appliance"}, {"type": "construction", "id": "app_veh_tools_kitchen", "group": "place_veh_tools_kitchen", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["veh_tools_kitchen", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_veh_tools_workshop", "group": "place_veh_tools_workshop", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["veh_tools_workshop", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_wall_light", "group": "place_wall_light", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 s", "components": [[["wall_light", 1]]], "pre_flags": "WALL", "post_special": "done_appliance", "activity_level": "NO_EXERCISE"}, {"type": "construction", "id": "app_wall_wiring", "group": "reveal_wall_wiring", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "0 s", "qualities": [{"id": "CUT", "level": 2}], "tools": [[["pliers", -1], ["multitool", -1], ["pliers_locking", -1], ["toolset", -1]]], "pre_flags": "WIRED_WALL", "pre_special": "check_no_wiring", "post_special": "done_wiring", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "app_wall_wiring_homemade", "group": "place_wall_wiring", "category": "APPLIANCE", "required_skills": [["fabrication", 2]], "time": "20 m", "qualities": [{"id": "CUT", "level": 2}], "tools": [[["pliers", -1], ["multitool", -1], ["pliers_locking", -1], ["toolset", -1]]], "components": [[["cable", 19]], [["duct_tape", 20]]], "pre_flags": "WALL", "pre_special": "check_no_wiring", "post_special": "done_wiring", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_washing_machine", "group": "place_washing_machine", "category": "APPLIANCE", "required_skills": [["electronics", 1]], "time": "6 m", "components": [[["household_washing_machine", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_water_purifier", "group": "place_water_purifier", "category": "APPLIANCE", "required_skills": [["fabrication", 0]], "time": "5 m", "components": [[["stationary_water_purifier", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "BRISK_EXERCISE"}, {"type": "construction", "id": "app_water_wheel", "group": "place_water_wheel", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "30 m", "qualities": [[{"id": "WRENCH", "level": 2}]], "components": [[["water_wheel", 1]]], "pre_terrain": "t_water_moving_sh", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_wind_turbine", "group": "place_wind_turbine", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "30 m", "qualities": [[{"id": "WRENCH", "level": 2}]], "components": [[["wind_turbine", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_xl_water_wheel", "group": "place_xl_water_wheel", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "30 m", "qualities": [[{"id": "WRENCH", "level": 2}]], "components": [[["xl_water_wheel", 1]]], "pre_terrain": "t_water_moving_sh", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "app_xl_wind_turbine", "group": "place_xl_wind_turbine", "category": "APPLIANCE", "required_skills": [["fabrication", 1]], "time": "30 m", "qualities": [[{"id": "WRENCH", "level": 2}]], "components": [[["xl_wind_turbine", 1]]], "pre_special": "check_empty", "post_special": "done_appliance", "activity_level": "MODERATE_EXERCISE"}]