<?xml version="1.0" encoding="utf-8"?>
<!-- Replace com.test.game with the identifier of your game below, e.g.
     com.gamemaker.game
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="1"
    android:versionName="1.0"
    android:installLocation="auto">

    <!-- OpenGL ES 2.0 -->
    <uses-feature android:glEsVersion="0x00020000" />

    <!-- Allow writing to external storage -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Allow access to the vibrator -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- if you want to capture audio, uncomment this. -->
    <!-- <uses-permission android:name="android.permission.RECORD_AUDIO" /> -->

    <!-- Create a Java class extending SDLActivity and place it in a
         directory under app/src/main/java matching the package, e.g. app/src/main/java/com/gamemaker/game/MyGame.java

         then replace "SDLActivity" with the name of your class (e.g. "MyGame")
         in the XML below.

         An example Java class can be found in README-android.md
    -->
    <application android:label="@string/app_name"
        android:icon="@drawable/ic_launcher"
        android:allowBackup="true"
        android:theme="@style/AppTheme"
        android:hardwareAccelerated="true"
        android:isGame="true" >

        <!-- Example of setting SDL hints from AndroidManifest.xml:
        <meta-data android:name="SDL_ENV.SDL_ACCELEROMETER_AS_JOYSTICK" android:value="0"/>
         -->

        <activity android:name="SplashScreen"
                  android:label="@string/app_name"
                  android:launchMode="singleInstance"
                  android:keepScreenOn="true"
                  android:screenOrientation="userLandscape"
                  android:configChanges="keyboardHidden|keyboard|orientation|screenSize|navigation"
                  android:exported="true"
                  >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="CataclysmDDA"
                  android:label="@string/app_name"
                  android:launchMode="singleInstance"
                  android:keepScreenOn="true"
                  android:screenOrientation="userLandscape"
                  android:configChanges="keyboardHidden|keyboard|orientation|screenSize|navigation"
                  >
        </activity>
    </application>

</manifest>
