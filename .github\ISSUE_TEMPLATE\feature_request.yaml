name: Feature Request ✨
description: Suggest an idea for this project
labels: ["<Suggestion / Discussion>"]

body:
  - type: markdown
    attributes:
      value: |
        Please take the time to fill out all the fields below.

  - type: textarea
    id: description-of-bug
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is that made you submit this report.
      placeholder: I am always frustrated, when...
    validations:
      required: true

  - type: textarea
    id: description-of-solution
    attributes:
      label: Solution you would like.
      description: A clear and concise description of what you want to happen.
      placeholder: Instead of behaving like this, there should be...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Describe alternatives you have considered.
      description: A clear and concise description of any alternative solutions or features you have considered.
      placeholder: Another workaround that would work, is...
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Add any other context (such as mock-ups, proof of concepts or screenshots) about the feature request here.
    validations:
      required: false
    
