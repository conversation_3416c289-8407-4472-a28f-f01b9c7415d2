name: Check printf format string in translations

on:
  pull_request:
    paths:
      - 'lang/po/*.po'
      - 'tools/check_po_printf_format.py'

permissions:
  contents: read

jobs:
  check-po-printf:
    runs-on: ubuntu-20.04
    steps:
      - name: "Install dependencies"
        run: |
          sudo apt-get update
          sudo apt-get install python3-pip
          sudo pip3 install polib
      - name: "Checkout"
        uses: actions/checkout@v4
      - name: "Check printf format string in translations"
        run: ./tools/check_po_printf_format.py
