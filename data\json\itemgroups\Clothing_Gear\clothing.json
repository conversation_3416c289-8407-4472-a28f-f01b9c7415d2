[{"id": "clothing_biker", "type": "item_group", "subtype": "collection", "//": "Protective clothing for motorbike riding.", "items": [{"item": "helmet_motor"}, {"item": "motorbike_boots", "prob": 80}, {"distribution": [{"collection": [{"item": "motorbike_armor"}, {"item": "motorbike_pants", "prob": 30}], "prob": 40}, {"collection": [{"item": "leather_police_jacket"}, {"item": "motorbike_pants", "prob": 30}], "prob": 40}, {"item": "touring_suit", "prob": 40}], "prob": 80}]}, {"id": "clothing_glasses", "type": "item_group", "subtype": "distribution", "entries": [{"distribution": [{"distribution": [{"item": "sunglasses_eye", "prob": 80}, {"item": "fancy_sunglasses_eye", "prob": 20}], "prob": 15}, {"distribution": [{"item": "sunglasses_reading", "prob": 80}, {"item": "fancy_sunglasses_reading", "prob": 20}], "prob": 10}, {"distribution": [{"item": "sunglasses_bifocal", "prob": 80}, {"item": "fancy_sunglasses_bifocal", "prob": 20}], "prob": 5}, {"distribution": [{"item": "transition_glasses_eye", "prob": 80}, {"item": "fancy_transition_glasses_eye", "prob": 20}], "prob": 9}, {"distribution": [{"item": "transition_glasses_reading", "prob": 80}, {"item": "fancy_transition_glasses_reading", "prob": 20}], "prob": 6}, {"distribution": [{"item": "transition_glasses_bifocal", "prob": 80}, {"item": "fancy_transition_glasses_bifocal", "prob": 20}], "prob": 3}, {"distribution": [{"item": "sunglasses", "prob": 80}, {"item": "fancy_sunglasses", "prob": 20}], "prob": 20}, {"collection": [{"distribution": [{"distribution": [{"item": "glasses_eye", "prob": 80}, {"item": "fancy_glasses_eye", "prob": 20}], "prob": 45}, {"distribution": [{"item": "glasses_reading", "prob": 80}, {"item": "fancy_glasses_reading", "prob": 20}], "prob": 30}, {"distribution": [{"item": "glasses_bifocal", "prob": 80}, {"item": "fancy_glasses_bifocal", "prob": 20}], "prob": 20}]}, {"item": "fitover_sunglasses"}], "prob": 50}, {"item": "glasses_monocle", "prob": 2}]}]}, {"id": "clothing_hunting", "type": "item_group", "//": "Clothes that hunters take on hunting trips.", "subtype": "distribution", "entries": [{"item": "balclava", "prob": 50}, {"item": "hat_cotton", "prob": 50}, {"item": "hat_faux_fur", "prob": 25}, {"item": "hat_fur", "prob": 50}, {"item": "hat_hunting", "prob": 75}, {"item": "hat_knit", "prob": 25}, {"item": "tank_top", "variant": "tank_top_camo", "prob": 50}, {"item": "long_underpants", "prob": 50}, {"item": "long_undertop", "prob": 50}, {"item": "gloves_liner", "prob": 50}, {"item": "gloves_wool", "prob": 50}, {"item": "gloves_wool_fingerless", "prob": 25}, {"item": "knit_scarf", "prob": 50}, {"item": "pants_cargo", "prob": 75}, {"item": "tacjacket", "prob": 75}, {"item": "jacket_army", "prob": 40}, {"item": "socks_wool", "prob": 50}, {"item": "coat_winter", "prob": 50}, {"item": "greatcoat", "prob": 25}, {"item": "jacket_flannel", "prob": 75}, {"item": "peacoat", "prob": 25}, {"item": "trenchcoat", "prob": 25}, {"item": "boots", "prob": 75}, {"item": "boots_winter", "prob": 50}, {"item": "boots_hiking", "prob": 50}, {"item": "boots_steel", "prob": 25}, {"item": "sneakers_steel", "prob": 18}, {"item": "fishing_waders", "prob": 25}, {"item": "leg_bag", "prob": 15}, {"item": "leg_small_bag", "prob": 10}, {"item": "armrig", "prob": 10}, {"item": "ankle_wallet_pouch", "prob": 5}, {"distribution": [{"item": "pants", "variant": "pants_woodland", "prob": 15}, {"item": "pants", "variant": "pants_jungle", "prob": 15}, {"item": "pants", "variant": "pants_marshland", "prob": 15}, {"item": "pants", "variant": "pants_alpine", "prob": 15}, {"item": "pants", "variant": "pants_urban", "prob": 15}, {"item": "pants", "variant": "pants_black_camo", "prob": 15}], "prob": 65}, {"item": "pants_tactical", "prob": 20}, {"item": "pants_hiking", "prob": 10}]}, {"id": "clothing_riding", "type": "item_group", "//": "Clothes that horse riders wear.", "subtype": "distribution", "entries": [{"item": "helmet_riding", "prob": 50}, {"item": "helmet_riding_peakless", "prob": 50}, {"item": "hat_hunting", "prob": 15}, {"item": "knit_scarf", "prob": 25}, {"item": "long_knit_scarf", "prob": 25}, {"item": "longshirt", "prob": 50}, {"item": "jacket_flannel", "prob": 50}, {"item": "coat_winter", "prob": 25}, {"item": "peacoat", "prob": 25}, {"item": "gloves_wool", "prob": 25}, {"item": "gloves_wool_fingerless", "prob": 12}, {"item": "gloves_leather", "prob": 25}, {"item": "leather_belt", "prob": 25}, {"item": "breeches", "prob": 50}, {"item": "pants_hiking", "prob": 50}, {"item": "socks_wool", "prob": 25}, {"item": "boots_hiking", "prob": 50}, {"item": "boots_western", "prob": 50}, {"item": "knee_high_boots", "prob": 50}, {"item": "boots", "prob": 25}, {"item": "boots_winter", "prob": 25}, {"item": "boots_steel", "prob": 15}, {"item": "boots_denim", "prob": 5}, {"item": "sneakers_steel", "prob": 15}]}, {"id": "clothing_outdoor_set", "type": "item_group", "subtype": "collection", "//": "Standard (non-winter) set of clothes for wearing outdoors.", "items": [{"group": "clothing_outdoor_torso"}, {"group": "clothing_outdoor_pants"}, {"group": "clothing_outdoor_shoes"}, {"group": "underwear"}, {"group": "clothing_glasses", "prob": 5}, {"group": "clothing_watch", "prob": 5}]}, {"id": "clothing_navy", "type": "item_group", "subtype": "collection", "//": "Base clothing entries for Navy personnel like sailors, Marines, SEALs, etc. Leaving out entries for boots and pants in order to define them in the individual drops for the monsters themselves, so as to give the appropriate type of boots and army pants pattern to the appropriate types of personnel.", "items": [{"group": "clothing_soldier_shirt"}, {"distribution": [{"item": "jacket_army_modern", "prob": 50}, {"item": "combat_shirt", "prob": 50}]}, {"distribution": [{"item": "socks", "prob": 30}, {"item": "nomex_socks", "prob": 70}]}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}]}, {"id": "clothing_navy_officer", "type": "item_group", "subtype": "collection", "//": "Base clothing entries for Navy officers.", "items": [{"item": "boots", "damage": [0, 4]}, {"item": "hat_navy", "damage": [0, 4]}, {"item": "officer_uniform", "damage": [0, 4]}, {"distribution": [{"item": "socks", "prob": 30}, {"item": "nomex_socks", "prob": 70}]}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}]}, {"id": "clothing_military", "type": "item_group", "subtype": "collection", "//": "Standard (non-winter) set of clothes worn by soldiers and paramilitary forces.", "items": [{"group": "clothing_soldier_shirt"}, {"distribution": [{"item": "jacket_army_modern", "prob": 50}, {"item": "combat_shirt", "prob": 50}]}, {"item": "pants_army"}, {"group": "loaded_webbing_belt"}, {"distribution": [{"item": "socks", "prob": 95}, {"item": "socks_wool", "prob": 5}]}, {"item": "boots_combat"}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}]}, {"id": "clothing_military_distribution", "type": "item_group", "subtype": "distribution", "//": "todo: when #57228 would be closed, change this one to copy-from `clothing_military`", "items": [{"group": "clothing_soldier_shirt"}, {"distribution": [{"item": "jacket_army_modern", "prob": 50}, {"item": "combat_shirt", "prob": 50}]}, {"item": "pants_army"}, {"group": "loaded_webbing_belt"}, {"distribution": [{"item": "socks", "prob": 95}, {"item": "socks_wool", "prob": 5}]}, {"item": "boots_combat"}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}]}, {"id": "clothing_military_winter", "type": "item_group", "subtype": "collection", "//": "Winter set of clothes worn by soldiers and paramilitary forces.", "items": [{"group": "clothing_soldier_shirt"}, {"item": "winter_jacket_army"}, {"item": "winter_pants_army"}, {"group": "loaded_webbing_belt"}, {"distribution": [{"item": "socks", "prob": 10}, {"item": "socks_wool", "prob": 90}]}, {"item": "boots_combat"}, {"collection": [{"distribution": [{"item": "balclava", "prob": 90}, {"item": "balaclava_cut_resistant", "prob": 10}]}], "prob": 25}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}]}, {"id": "clothing_soldier_set", "type": "item_group", "subtype": "collection", "//": "Standard (non-winter) set of clothes and combat gear worn by soldiers and paramilitary forces.", "items": [{"group": "clothing_military"}, {"item": "elbow_pads", "prob": 10}, {"item": "knee_pads", "prob": 85}, {"distribution": [{"group": "army_helmet_nvg", "prob": 15}, {"item": "helmet_army", "prob": 85}], "prob": 80}, {"item": "gloves_tactical", "prob": 60}, {"group": "military_ballistic_vest", "prob": 90}, {"group": "molle_backpacks_and_accessories", "prob": 85}, {"group": "clothing_tactical_leg", "prob": 15}, {"distribution": [{"group": "clothing_glasses", "prob": 60}, {"item": "glasses_bal", "prob": 30}], "prob": 20}, {"group": "clothing_watch", "prob": 5}]}, {"id": "clothing_soldier_set_pristine", "type": "item_group", "subtype": "collection", "//": "Standard (non-winter) set of clothes and combat gear worn by soldiers and paramilitary forces.", "items": [{"group": "clothing_military"}, {"item": "elbow_pads", "prob": 10}, {"item": "knee_pads", "prob": 85}, {"distribution": [{"group": "army_helmet_nvg", "prob": 20}, {"item": "helmet_army", "prob": 80}], "prob": 80}, {"item": "gloves_tactical", "prob": 60}, {"group": "military_ballistic_vest_pristine", "prob": 90}, {"group": "molle_backpacks_and_accessories", "prob": 85}, {"group": "clothing_tactical_leg", "prob": 15}, {"distribution": [{"group": "clothing_glasses", "prob": 60}, {"item": "glasses_bal", "prob": 30}], "prob": 20}, {"group": "clothing_watch", "prob": 5}]}, {"id": "clothing_soldier_heavy_set", "type": "item_group", "subtype": "collection", "//": "Set of clothes and combat gear worn by heavier soldiers and paramilitary forces including IOTV armor.", "items": [{"group": "clothing_military"}, {"item": "elbow_pads", "prob": 10}, {"item": "knee_pads", "prob": 85}, {"distribution": [{"group": "army_helmet_nvg", "prob": 20}, {"item": "helmet_army", "prob": 80}], "prob": 80}, {"item": "gloves_tactical", "prob": 60}, {"group": "military_ballistic_vest_heavy", "prob": 90}, {"group": "molle_backpacks_and_accessories", "prob": 85}, {"group": "clothing_tactical_leg", "prob": 15}, {"distribution": [{"group": "clothing_glasses", "prob": 60}, {"item": "glasses_bal", "prob": 30}], "prob": 20}, {"group": "clothing_watch", "prob": 5}]}, {"id": "clothing_soldier_winter_set", "type": "item_group", "subtype": "collection", "//": "Winter set of clothes and combat gear worn by soldiers and paramilitary forces.", "items": [{"group": "clothing_military_winter"}, {"item": "elbow_pads", "prob": 10}, {"item": "knee_pads", "prob": 85}, {"collection": [{"distribution": [{"group": "army_helmet_nvg", "prob": 15}, {"item": "helmet_army", "prob": 85}]}, {"item": "helmet_liner"}], "prob": 80}, {"collection": [{"item": "gloves_liner", "prob": 60}, {"item": "winter_gloves_army"}], "prob": 80}, {"group": "military_ballistic_vest", "prob": 90}, {"group": "molle_backpacks_and_accessories", "prob": 85}, {"group": "clothing_tactical_leg", "prob": 15}, {"distribution": [{"group": "clothing_glasses", "prob": 60}, {"item": "glasses_bal", "prob": 30}], "prob": 20}, {"group": "clothing_watch", "prob": 5}]}, {"id": "clothing_military_pilot", "type": "item_group", "subtype": "collection", "//": "Standard set of clothes worn by a military helicopter pilot.", "items": [{"item": "chestrig", "prob": 40}, {"item": "flight_helmet", "ammo-item": "light_battery_cell", "charges": [0, 150]}, {"item": "mil_flight_suit", "prob": 80}, {"item": "nomex_gloves"}, {"group": "loaded_webbing_belt"}, {"distribution": [{"item": "socks", "prob": 30}, {"item": "nomex_socks", "prob": 70}]}, {"item": "gloves_tactical", "prob": 60}, {"item": "boots_combat"}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}, {"group": "clothing_watch", "prob": 85}, {"item": "goggles_ir", "prob": 10, "charges": [0, 100]}]}, {"id": "clothing_military_pilot_distribution", "type": "item_group", "subtype": "distribution", "//": "todo: when #57228 would be closed, change this one to copy-from `clothing_military`", "items": [{"item": "chestrig", "prob": 40}, {"item": "flight_helmet", "ammo-item": "light_battery_cell", "charges": [0, 150]}, {"item": "mil_flight_suit", "prob": 80}, {"item": "nomex_gloves"}, {"group": "loaded_webbing_belt"}, {"distribution": [{"item": "socks", "prob": 30}, {"item": "nomex_socks", "prob": 70}]}, {"item": "gloves_tactical", "prob": 60}, {"item": "boots_combat"}, {"distribution": [{"collection": [{"item": "sports_bra"}, {"item": "boy_shorts"}]}, {"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}]}, {"group": "clothing_watch", "prob": 85}, {"item": "goggles_ir", "prob": 10, "charges": [0, 100]}]}, {"id": "clothing_soldier_shirt", "type": "item_group", "subtype": "distribution", "entries": [{"item": "tshirt"}]}, {"id": "clothing_military_headwear", "type": "item_group", "subtype": "distribution", "//": "Standard set of hats worn by a uniformed soldier.", "items": [{"item": "beret", "prob": 40}, {"item": "hat_field", "prob": 35}, {"item": "hat_boonie", "prob": 23}, {"item": "turban", "prob": 1}, {"item": "headscarf", "prob": 1}]}, {"id": "clothing_outdoor_pants", "type": "item_group", "//": "Standard (non-winter) pants suitable for wearing outdoors.", "subtype": "distribution", "entries": [{"collection": [{"distribution": [{"item": "jeans", "prob": 60}, {"item": "jeans_ripped", "prob": 5}, {"item": "jeans_skinny", "prob": 10}, {"item": "pants", "prob": 100}, {"item": "pants_leather", "prob": 30}, {"item": "pants_tactical", "prob": 20}, {"item": "pants_cargo", "prob": 30}, {"item": "shorts_cargo", "prob": 60}, {"item": "pants", "variant": "pants_flag", "prob": 1}, {"item": "pants", "variant": "pants_flag", "prob": 70, "event": "independence_day"}]}, {"item": "leather_belt", "prob": 30}]}]}, {"type": "item_group", "id": "ear_protection", "//": "used in shooting_range.", "items": [{"item": "powered_earmuffs", "prob": 10, "charges": [0, 100]}, ["ear_plugs", 50], {"item": "powered_earplugs", "prob": 10, "charges": [0, 100]}]}, {"id": "clothing_outdoor_shoes", "type": "item_group", "//": "Standard (non-winter) shoes suitable for wearing outdoors.", "subtype": "distribution", "entries": [{"item": "boots", "prob": 100}, {"item": "boots_combat", "prob": 10}, {"item": "boots_hiking", "prob": 40}, {"item": "boots_western", "prob": 15}, {"item": "knee_high_boots", "prob": 20}, {"item": "thigh_high_boots", "prob": 20}, {"item": "boots_denim", "prob": 2}, {"item": "shoes_denim", "prob": 3}]}, {"id": "clothing_outdoor_torso", "type": "item_group", "//": "Standard (non-winter) shirts and jackets suitable for wearing outdoors.", "subtype": "distribution", "entries": [{"collection": [{"distribution": [{"item": "longshirt", "prob": 80}, {"item": "polo_shirt", "prob": 50}, {"item": "denim_shirt", "prob": 20}, {"item": "tshirt", "prob": 80}, {"item": "rashguard", "prob": 10}]}, {"distribution": [{"item": "coat_rain", "prob": 30}, {"item": "duster", "prob": 30}, {"item": "hoodie", "prob": 60}, {"item": "jacket_jean", "prob": 20}, {"item": "jacket_leather", "prob": 20}, {"item": "tacjacket", "prob": 30}, {"item": "leather_police_jacket", "prob": 15}, {"item": "jacket_light", "prob": 40}, {"item": "jacket_army", "prob": 25}, {"item": "jacket_windbreaker", "prob": 20}, {"item": "jacket_varsity", "prob": 20}, {"item": "gosling_jacket", "prob": 5}, {"item": "sweater", "prob": 50}, {"item": "turtleneck", "prob": 30}, {"item": "trenchcoat", "prob": 10}, {"group": "costume_jackets", "prob": 1}, {"group": "costume_jackets", "prob": 40, "event": "halloween"}, {"group": "christmas_jackets", "prob": 15, "event": "christmas"}], "prob": 30}]}]}, {"id": "clothing_prisoner_shoes", "type": "item_group", "//": "All the footwear that prisoners might wear.", "subtype": "distribution", "entries": [{"item": "sneakers", "prob": 80}, {"item": "boots", "prob": 70}, {"item": "lowtops", "prob": 45}, {"item": "golf_shoes", "prob": 20}, {"item": "boots_combat", "prob": 10}, {"item": "boots_hiking", "prob": 20}, {"item": "shoes_denim", "prob": 5}]}, {"id": "clothing_tactical_leg", "type": "item_group", "//": "Tactical gear that can be strapped to the legs", "subtype": "distribution", "entries": [{"item": "legpouch_large", "prob": 100}, {"item": "legrig", "prob": 100}]}, {"id": "clothing_tactical_torso", "type": "item_group", "//": "Tactical gear that can be strapped to the torso", "subtype": "distribution", "entries": [{"item": "ammo_satchel", "prob": 40}, {"item": "ammo_pouch", "prob": 40}, {"item": "backpack", "prob": 20}, {"item": "camelbak", "prob": 40}, {"item": "chestpouch", "prob": 40}, {"item": "backpack_tactical_large", "prob": 20}, {"item": "tacvest", "prob": 160}, {"group": "molle_backpacks_and_accessories", "prob": 100}, {"item": "rucksack", "prob": 80}]}, {"id": "clothing_watch", "type": "item_group", "subtype": "distribution", "entries": [{"item": "wristwatch", "prob": 90}, {"item": "silver_watch", "prob": 70}, {"item": "gold_watch", "prob": 40}, {"item": "platinum_watch", "prob": 10}, {"item": "sf_watch", "prob": 5}, {"item": "pocketwatch", "prob": 30}, {"item": "diving_watch", "prob": 20}, {"item": "game_watch", "prob": 20, "charges": [0, 50]}, {"item": "fitness_band", "prob": 10}, {"item": "smart_watch", "prob": 10, "charges": [0, 80]}]}, {"id": "clothing_work_boots", "type": "item_group", "//": "Sturdy boots often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"item": "boots", "prob": 60}, {"item": "boots_hiking", "prob": 10}, {"item": "boots_steel", "prob": 30}, {"item": "toecaps", "prob": 30}, {"item": "sneakers_steel", "prob": 21}]}, {"id": "clothing_work_glasses", "type": "item_group", "//": "Eye protection often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"group": "clothing_glasses", "prob": 10}, {"item": "glasses_safety", "prob": 75}, {"item": "goggles_welding", "prob": 15}]}, {"id": "clothing_work_gloves", "type": "item_group", "//": "Protective gloves often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"item": "gloves_leather", "prob": 30}, {"item": "gloves_rubber", "prob": 10}, {"item": "gloves_work", "prob": 60}, {"item": "gloves_cut_resistant", "prob": 10}]}, {"id": "clothing_work_hat", "type": "item_group", "//": "Protective helmets or headlamps often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"item": "hat_hard", "prob": 30}, {"group": "hat_hard_modified", "prob": 30}, {"item": "hat_hard_hooded", "prob": 10}, {"group": "hat_hard_hooded_modified", "prob": 5}, {"group": "hat_hard_flashlight", "prob": 10}, {"item": "wearable_light", "prob": 15, "charges": [0, 100]}, {"item": "hat_hard", "variant": "flag_hat_hard", "prob": 70, "event": "independence_day"}, {"item": "hat_hard_hooded", "variant": "flag_hat_hard", "prob": 20, "event": "independence_day"}]}, {"id": "hat_hard_flashlight", "type": "item_group", "subtype": "collection", "container-item": "hat_hard", "items": [{"item": "flashlight", "charges": [0, -1]}]}, {"id": "hat_hard_modified", "type": "item_group", "subtype": "collection", "container-item": "hat_hard", "items": [{"group": "common_nape_protectors", "prob": 50}, {"item": "flashlight", "charges": [0, -1], "prob": 50}, {"group": "common_face_shields", "prob": 15}, {"item": "plastic_chin_guard", "prob": 10}, {"item": "attachable_ear_muffs", "prob": 5}]}, {"id": "hat_hard_hooded_modified", "type": "item_group", "subtype": "collection", "container-item": "hat_hard_hooded", "items": [{"group": "common_nape_protectors", "prob": 50}, {"item": "plastic_chin_guard", "prob": 10}]}, {"id": "common_nape_protectors", "type": "item_group", "//": "Common nape protectors for a hard hat or helmet.", "subtype": "distribution", "entries": [{"item": "nape_protector", "prob": 6}, {"item": "mesh_nape_protector", "prob": 4}]}, {"id": "common_face_shields", "type": "item_group", "//": "Common face shields for a hard hat.", "subtype": "distribution", "entries": [{"item": "face_shield_plastic", "prob": 75}, {"item": "face_shield", "prob": 25}]}, {"id": "clothing_work_mask", "type": "item_group", "//": "Protective masks often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"item": "mask_dust", "prob": 90}, {"item": "mask_filter", "prob": 10, "charges": [0, 100]}, {"item": "mask_gas_half", "prob": 10, "charges": [0, 100]}]}, {"id": "clothing_work_pants", "type": "item_group", "//": "Pants often worn by technicians and tradesmen.", "subtype": "distribution", "entries": [{"item": "technician_pants_gray", "prob": 60}, {"item": "denim_overalls", "prob": 40}]}, {"id": "clothing_work_torso", "type": "item_group", "//": "Shirts and jackets often worn by technicians and laborers.", "subtype": "distribution", "entries": [{"collection": [{"item": "technician_shirt_gray", "prob": 100}, {"distribution": [{"item": "coat_rain", "prob": 30}, {"item": "jacket_jean", "prob": 20}, {"item": "vest_jean", "prob": 10}, {"item": "jacket_light", "prob": 40}], "prob": 20}]}]}, {"id": "clothing_work_set", "type": "item_group", "subtype": "collection", "//": "Set of clothes worn by technicians and laborers including protective gear", "items": [{"group": "clothing_work_boots"}, {"group": "clothing_work_glasses", "prob": 60}, {"group": "clothing_work_gloves", "prob": 75}, {"group": "clothing_work_hat", "prob": 60}, {"group": "clothing_work_mask", "prob": 40}, {"distribution": [{"item": "ear_plugs"}, {"item": "attached_ear_plugs_off"}, {"item": "attachable_ear_muffs"}], "prob": 60}, {"item": "tool_belt", "prob": 25}, {"distribution": [{"collection": [{"group": "clothing_work_pants"}, {"group": "clothing_work_torso"}], "prob": 80}, {"item": "technician_coveralls", "prob": 15}, {"item": "technician_coveralls_h", "prob": 5}]}]}, {"id": "hardware_clothing", "type": "item_group", "//": "wearable utility or protective gear you can buy in a common hardware store or find on a jobsite", "//2": "Not intended to be a set of clothing for NPCs or monsters", "items": [["boots_steel", 40], {"item": "toecaps", "prob": 30}, ["sneakers_steel", 28], ["hat_hard", 40], ["mesh_nape_protector", 10], ["nape_protector", 10], ["face_shield_plastic", 10], ["sun_shield", 15], ["attachable_ear_muffs", 5], ["mask_dust", 30], {"item": "mask_filter", "prob": 30, "charges": [0, 100]}, {"distribution": [{"item": "mask_gas", "charges": [0, -1], "prob": 50}, {"group": "gasmask_bag_full", "prob": 50}], "prob": 15}, {"item": "mask_gas_half", "prob": 30, "charges": [0, 100]}, ["glasses_safety", 30], ["ear_plugs", 35], ["apron_cut_resistant", 5], ["gloves_cut_resistant", 10], ["chaps_cut_resistant", 5], ["armguard_cut_resistant", 5], ["apron_leather", 10], ["tool_belt", 30], ["knee_pads", 20], ["sheath", 10], ["bootsheath", 3], ["gloves_leather", 40], ["gloves_work", 45], {"item": "wearable_light", "prob": 10, "charges": [0, 100]}, {"group": "hat_hard_flashlight", "prob": 1}]}, {"id": "plumbing_clothing", "type": "item_group", "//": "wearable utility or protective gear you can buy in a common hardware store or find on a jobsite", "//2": "Not intended to be a set of clothing for NPCs or monsters", "items": [["tool_belt", 200], ["knee_pads", 70], ["gloves_leather", 70], ["gloves_rubber", 50], ["boots_rubber", 20], ["boots", 70], ["boots_steel", 50], {"item": "toecaps", "prob": 30}, ["sneakers_steel", 28], ["fishing_waders", 20], ["denim_overalls", 20], ["technician_coveralls", 15], ["technician_coveralls_h", 5], ["apron_plastic", 30], ["apron_leather", 10], {"item": "mask_filter", "prob": 20, "charges": [0, 100]}, ["hat_hard", 50], {"item": "wearable_light", "prob": 100, "charges": [0, 100]}]}, {"id": "prof-plumbing_gear", "type": "item_group", "subtype": "collection", "//": "wearable utility or protective gear for a plumbing job, includes tools", "//2": "Intended to be a set of clothing for NPCs or monsters", "items": [{"item": "tool_belt", "prob": 60}, {"group": "tools_plumbing", "count": [1, 2]}, {"group": "tools_common_small", "count": [1, 2]}, {"group": "supplies_plumbing"}, {"group": "large_bags"}, {"item": "knee_pads", "prob": 30}, {"distribution": [{"item": "gloves_leather", "prob": 70}, {"item": "gloves_rubber", "prob": 30}], "prob": 40}, {"distribution": [{"item": "boots_rubber"}, {"item": "boots"}, {"item": "boots_steel"}, {"item": "sneakers_steel"}, {"item": "fishing_waders"}]}, {"distribution": [{"item": "socks"}, {"item": "socks_ankle"}]}, {"distribution": [{"collection": [{"group": "clothing_work_pants"}, {"group": "clothing_work_torso"}], "prob": 75}, {"item": "technician_coveralls", "prob": 20}, {"item": "technician_coveralls_h", "prob": 5}]}, ["hat_hard", 50], {"distribution": [{"item": "apron_plastic", "prob": 30}, {"item": "apron_leather", "prob": 10}], "prob": 30}, {"item": "mask_filter", "prob": 20, "charges": [0, 100]}, {"item": "wearable_light", "prob": 70, "charges": [0, 100]}]}, {"id": "hazmat_suits", "type": "item_group", "//": "Various types of hazmat suits, without masks", "items": [{"collection": [{"item": "cleansuit"}, {"item": "gloves_rubber"}, {"item": "boots_rubber"}], "prob": 40}, ["hazmat_suit", 60]]}, {"id": "hazmat_masks", "type": "item_group", "//": "Full face protection worn with hazmat suits", "items": [{"collection": [{"item": "glasses_safety"}, {"item": "mask_filter", "charges": [0, -1]}], "prob": 40}, {"distribution": [{"item": "mask_gas", "charges": [0, -1], "prob": 50}, {"item": "mask_gas", "charges": [0, -1], "contents-group": "gasmask_inserts_in_use", "prob": 25}, {"group": "gasmask_bag_full", "prob": 25}, {"group": "gasmask_bag_used", "prob": 60}], "prob": 60}, {"item": "mask_gas_half", "prob": 60, "charges": [0, -1]}]}, {"id": "boxing_clothes", "type": "item_group", "subtype": "distribution", "items": [{"item": "tank_top", "prob": 40}, {"item": "shorts", "prob": 40}, {"item": "boxing_gloves", "prob": 50}, {"item": "headgear", "prob": 50}, {"item": "mouthpiece", "prob": 50}, {"item": "jersey", "prob": 40}, {"item": "coat_rain", "prob": 15}, {"item": "hood_rain", "prob": 15}, {"item": "unitard", "prob": 10}, {"item": "gloves_wraps", "prob": 45}, {"item": "jeans", "prob": 30}, {"item": "jeans_ripped", "prob": 5}, {"item": "sports_bra", "prob": 25}, {"item": "jacket_leather", "prob": 20}, {"item": "jacket_varsity", "prob": 10}, {"prob": 30, "group": "bottle_otc_painkiller_20"}, {"item": "water_clean", "prob": 30}, {"item": "sports_drink", "prob": 30}, {"group": "used_1st_aid", "prob": 20}, {"item": "fitness_band", "prob": 5}]}, {"id": "donated_clothes", "type": "item_group", "subtype": "collection", "items": [{"group": "underwear"}, {"group": "clothing_outdoor_torso"}, {"group": "clothing_outdoor_pants"}, {"item": "socks"}, {"item": "boots"}]}, {"id": "judo_belts", "type": "item_group", "items": [["judo_belt_white", 100], ["judo_belt_yellow", 50], ["judo_belt_orange", 25], ["judo_belt_green", 15], ["judo_belt_blue", 5], ["judo_belt_brown", 4], ["judo_belt_black", 1]]}, {"id": "gi", "type": "item_group", "items": [["karate_gi", 100], ["tabi_gi", 25], ["judo_gi", 50], ["keikogi", 25], ["zubon_gi", 25], ["hakama_gi", 10], ["obi_gi", 10], ["mouthpiece", 5], ["jikatabi_sneakers", 5], ["geta", 1], ["haori", 1], ["yukata", 1]]}, {"id": "cufflinks_silver", "type": "item_group", "subtype": "distribution", "entries": [{"item": "cufflinks", "prob": 50}, {"item": "cufflinks_intricate", "prob": 50}, {"item": "citrine_silver_cufflinks", "prob": 50}, {"item": "diamond_silver_cufflinks", "prob": 50}, {"item": "emerald_silver_cufflinks", "prob": 50}, {"item": "peridot_silver_cufflinks", "prob": 50}, {"item": "amethyst_silver_cufflinks", "prob": 50}, {"item": "sapphire_silver_cufflinks", "prob": 50}, {"item": "aquamarine_silver_cufflinks", "prob": 50}, {"item": "blue_topaz_silver_cufflinks", "prob": 50}, {"item": "tourmaline_silver_cufflinks", "prob": 50}, {"item": "alexandrite_silver_cufflinks", "prob": 50}, {"item": "pearl_silver_cufflinks", "prob": 50}, {"item": "onyx_silver_cufflinks", "prob": 50}, {"item": "opal_silver_cufflinks", "prob": 50}, {"item": "ruby_silver_cufflinks", "prob": 50}, {"item": "garnet_silver_cufflinks", "prob": 50}]}, {"id": "cufflinks_gold", "type": "item_group", "subtype": "distribution", "entries": [{"item": "citrine_gold_cufflinks", "prob": 50}, {"item": "diamond_gold_cufflinks", "prob": 50}, {"item": "emerald_gold_cufflinks", "prob": 50}, {"item": "peridot_gold_cufflinks", "prob": 50}, {"item": "amethyst_gold_cufflinks", "prob": 50}, {"item": "sapphire_gold_cufflinks", "prob": 50}, {"item": "aquamarine_gold_cufflinks", "prob": 50}, {"item": "blue_topaz_gold_cufflinks", "prob": 50}, {"item": "tourmaline_gold_cufflinks", "prob": 50}, {"item": "alexandrite_gold_cufflinks", "prob": 50}, {"item": "pearl_gold_cufflinks", "prob": 50}, {"item": "onyx_gold_cufflinks", "prob": 50}, {"item": "opal_gold_cufflinks", "prob": 50}, {"item": "ruby_gold_cufflinks", "prob": 50}, {"item": "garnet_gold_cufflinks", "prob": 50}]}, {"id": "cufflinks_platinum", "type": "item_group", "subtype": "distribution", "entries": [{"item": "citrine_platinum_cufflinks", "prob": 50}, {"item": "diamond_platinum_cufflinks", "prob": 50}, {"item": "emerald_platinum_cufflinks", "prob": 50}, {"item": "peridot_platinum_cufflinks", "prob": 50}, {"item": "amethyst_platinum_cufflinks", "prob": 50}, {"item": "sapphire_platinum_cufflinks", "prob": 50}, {"item": "aquamarine_platinum_cufflinks", "prob": 50}, {"item": "blue_topaz_platinum_cufflinks", "prob": 50}, {"item": "tourmaline_platinum_cufflinks", "prob": 50}, {"item": "alexandrite_platinum_cufflinks", "prob": 50}, {"item": "pearl_platinum_cufflinks", "prob": 50}, {"item": "onyx_platinum_cufflinks", "prob": 50}, {"item": "opal_platinum_cufflinks", "prob": 50}, {"item": "ruby_platinum_cufflinks", "prob": 50}, {"item": "garnet_platinum_cufflinks", "prob": 50}]}, {"id": "dental_grills", "type": "item_group", "items": [["gold_dental_grill", 50], ["platinum_dental_grill", 50], ["citrine_dental_grill", 50], ["diamond_dental_grill", 50], ["emerald_dental_grill", 50], ["peridot_dental_grill", 50], ["amethyst_dental_grill", 50], ["sapphire_dental_grill", 50], ["aquamarine_dental_grill", 50], ["blue_topaz_dental_grill", 50], ["tourmaline_dental_grill", 50], ["alexandrite_dental_grill", 50], ["ruby_dental_grill", 50], ["garnet_dental_grill", 50]]}, {"id": "tiaras_silver", "type": "item_group", "items": [["citrine_silver_tiara", 50], ["diamond_silver_tiara", 50], ["emerald_silver_tiara", 50], ["peridot_silver_tiara", 50], ["amethyst_silver_tiara", 50], ["sapphire_silver_tiara", 50], ["aquamarine_silver_tiara", 50], ["blue_topaz_silver_tiara", 50], ["tourmaline_silver_tiara", 50], ["alexandrite_silver_tiara", 50], ["pearl_silver_tiara", 50], ["onyx_silver_tiara", 50], ["opal_silver_tiara", 50], ["ruby_silver_tiara", 50], ["garnet_silver_tiara", 50]]}, {"id": "tiaras_gold", "type": "item_group", "items": [["citrine_gold_tiara", 50], ["diamond_gold_tiara", 50], ["emerald_gold_tiara", 50], ["peridot_gold_tiara", 50], ["amethyst_gold_tiara", 50], ["sapphire_gold_tiara", 50], ["aquamarine_gold_tiara", 50], ["blue_topaz_gold_tiara", 50], ["tourmaline_gold_tiara", 50], ["alexandrite_gold_tiara", 50], ["pearl_gold_tiara", 50], ["onyx_gold_tiara", 50], ["opal_gold_tiara", 50], ["ruby_gold_tiara", 50], ["garnet_gold_tiara", 50]]}, {"id": "tiaras_platinum", "type": "item_group", "items": [["citrine_platinum_tiara", 50], ["diamond_platinum_tiara", 50], ["emerald_platinum_tiara", 50], ["peridot_platinum_tiara", 50], ["amethyst_platinum_tiara", 50], ["sapphire_platinum_tiara", 50], ["aquamarine_platinum_tiara", 50], ["blue_topaz_platinum_tiara", 50], ["tourmaline_platinum_tiara", 50], ["alexandrite_platinum_tiara", 50], ["pearl_platinum_tiara", 50], ["onyx_platinum_tiara", 50], ["opal_platinum_tiara", 50], ["ruby_platinum_tiara", 50], ["garnet_platinum_tiara", 50]]}, {"id": "pendant_necklaces_silver", "type": "item_group", "items": [["silver_necklace", 50], ["citrine_silver_pendant_necklace", 50], ["diamond_silver_pendant_necklace", 50], ["emerald_silver_pendant_necklace", 50], ["peridot_silver_pendant_necklace", 50], ["amethyst_silver_pendant_necklace", 50], ["sapphire_silver_pendant_necklace", 50], ["aquamarine_silver_pendant_necklace", 50], ["blue_topaz_silver_pendant_necklace", 50], ["tourmaline_silver_pendant_necklace", 50], ["alexandrite_silver_pendant_necklace", 50], ["pearl_silver_pendant_necklace", 50], ["onyx_silver_pendant_necklace", 50], ["opal_silver_pendant_necklace", 50], ["ruby_silver_pendant_necklace", 50], ["garnet_silver_pendant_necklace", 50]]}, {"id": "pendant_necklaces_gold", "type": "item_group", "items": [["gold_necklace", 50], ["citrine_gold_pendant_necklace", 50], ["diamond_gold_pendant_necklace", 50], ["emerald_gold_pendant_necklace", 50], ["peridot_gold_pendant_necklace", 50], ["amethyst_gold_pendant_necklace", 50], ["sapphire_gold_pendant_necklace", 50], ["aquamarine_gold_pendant_necklace", 50], ["blue_topaz_gold_pendant_necklace", 50], ["tourmaline_gold_pendant_necklace", 50], ["alexandrite_gold_pendant_necklace", 50], ["pearl_gold_pendant_necklace", 50], ["onyx_gold_pendant_necklace", 50], ["opal_gold_pendant_necklace", 50], ["ruby_gold_pendant_necklace", 50], ["garnet_gold_pendant_necklace", 50]]}, {"id": "pendant_necklaces_platinum", "type": "item_group", "items": [["platinum_necklace", 50], ["citrine_platinum_pendant_necklace", 50], ["diamond_platinum_pendant_necklace", 50], ["emerald_platinum_pendant_necklace", 50], ["peridot_platinum_pendant_necklace", 50], ["amethyst_platinum_pendant_necklace", 50], ["sapphire_platinum_pendant_necklace", 50], ["aquamarine_platinum_pendant_necklace", 50], ["blue_topaz_platinum_pendant_necklace", 50], ["tourmaline_platinum_pendant_necklace", 50], ["alexandrite_platinum_pendant_necklace", 50], ["pearl_platinum_pendant_necklace", 50], ["onyx_platinum_pendant_necklace", 50], ["opal_platinum_pendant_necklace", 50], ["ruby_platinum_pendant_necklace", 50], ["garnet_platinum_pendant_necklace", 50]]}, {"id": "earrings_silver", "type": "item_group", "items": [["silver_ear", 50], ["citrine_silver_earring", 50], ["diamond_silver_earring", 50], ["emerald_silver_earring", 50], ["peridot_silver_earring", 50], ["amethyst_silver_earring", 50], ["sapphire_silver_earring", 50], ["aquamarine_silver_earring", 50], ["blue_topaz_silver_earring", 50], ["tourmaline_silver_earring", 50], ["alexandrite_silver_earring", 50], ["pearl_silver_earring", 50], ["onyx_silver_earring", 50], ["opal_silver_earring", 50], ["ruby_silver_earring", 50], ["garnet_silver_earring", 50]]}, {"id": "earrings_gold", "type": "item_group", "items": [["gold_ear", 50], ["citrine_gold_earring", 50], ["diamond_gold_earring", 50], ["emerald_gold_earring", 50], ["peridot_gold_earring", 50], ["amethyst_gold_earring", 50], ["sapphire_gold_earring", 50], ["aquamarine_gold_earring", 50], ["blue_topaz_gold_earring", 50], ["tourmaline_gold_earring", 50], ["alexandrite_gold_earring", 50], ["pearl_gold_earring", 50], ["onyx_gold_earring", 50], ["opal_gold_earring", 50], ["ruby_gold_earring", 50], ["garnet_gold_earring", 50]]}, {"id": "earrings_platinum", "type": "item_group", "items": [["platinum_ear", 50], ["citrine_platinum_earring", 50], ["diamond_platinum_earring", 50], ["emerald_platinum_earring", 50], ["peridot_platinum_earring", 50], ["amethyst_platinum_earring", 50], ["sapphire_platinum_earring", 50], ["aquamarine_platinum_earring", 50], ["blue_topaz_platinum_earring", 50], ["tourmaline_platinum_earring", 50], ["alexandrite_platinum_earring", 50], ["pearl_platinum_earring", 50], ["onyx_platinum_earring", 50], ["opal_platinum_earring", 50], ["ruby_platinum_earring", 50], ["garnet_platinum_earring", 50]]}, {"id": "rings_silver", "type": "item_group", "items": [["silver_ring", 50], ["citrine_silver_ring", 50], ["diamond_silver_ring", 50], ["emerald_silver_ring", 50], ["peridot_silver_ring", 50], ["amethyst_silver_ring", 50], ["sapphire_silver_ring", 50], ["aquamarine_silver_ring", 50], ["blue_topaz_silver_ring", 50], ["tourmaline_silver_ring", 50], ["alexandrite_silver_ring", 50], ["pearl_silver_ring", 50], ["onyx_silver_ring", 50], ["opal_silver_ring", 50], ["ruby_silver_ring", 50], ["garnet_silver_ring", 50]]}, {"id": "rings_gold", "type": "item_group", "items": [["gold_ring", 50], ["citrine_gold_ring", 50], ["diamond_ring", 50], ["emerald_gold_ring", 50], ["peridot_gold_ring", 50], ["amethyst_gold_ring", 50], ["sapphire_gold_ring", 50], ["aquamarine_gold_ring", 50], ["blue_topaz_gold_ring", 50], ["tourmaline_gold_ring", 50], ["alexandrite_gold_ring", 50], ["pearl_gold_ring", 50], ["onyx_gold_ring", 50], ["opal_gold_ring", 50], ["ruby_gold_ring", 50], ["garnet_gold_ring", 50]]}, {"id": "rings_platinum", "type": "item_group", "items": [["platinum_ring", 50], ["citrine_platinum_ring", 50], ["diamond_platinum_ring", 50], ["emerald_platinum_ring", 50], ["peridot_platinum_ring", 50], ["amethyst_platinum_ring", 50], ["sapphire_platinum_ring", 50], ["aquamarine_platinum_ring", 50], ["blue_topaz_platinum_ring", 50], ["tourmaline_platinum_ring", 50], ["alexandrite_platinum_ring", 50], ["pearl_platinum_ring", 50], ["onyx_platinum_ring", 50], ["opal_platinum_ring", 50], ["ruby_platinum_ring", 50], ["garnet_platinum_ring", 50]]}, {"id": "bracelets_silver", "type": "item_group", "subtype": "distribution", "entries": [{"item": "silver_bracelet", "prob": 50}, {"item": "citrine_silver_bracelet", "prob": 50}, {"item": "diamond_silver_bracelet", "prob": 50}, {"item": "emerald_silver_bracelet", "prob": 50}, {"item": "peridot_silver_bracelet", "prob": 50}, {"item": "amethyst_silver_bracelet", "prob": 50}, {"item": "sapphire_silver_bracelet", "prob": 50}, {"item": "aquamarine_silver_bracelet", "prob": 50}, {"item": "blue_topaz_silver_bracelet", "prob": 50}, {"item": "tourmaline_silver_bracelet", "prob": 50}, {"item": "alexandrite_silver_bracelet", "prob": 50}, {"item": "pearl_silver_bracelet", "prob": 50}, {"item": "onyx_silver_bracelet", "prob": 50}, {"item": "opal_silver_bracelet", "prob": 50}, {"item": "ruby_silver_bracelet", "prob": 50}, {"item": "garnet_silver_bracelet", "prob": 50}]}, {"id": "bracelets_gold", "type": "item_group", "subtype": "distribution", "entries": [{"item": "gold_bracelet", "prob": 50}, {"item": "citrine_gold_bracelet", "prob": 50}, {"item": "diamond_gold_bracelet", "prob": 50}, {"item": "emerald_gold_bracelet", "prob": 50}, {"item": "peridot_gold_bracelet", "prob": 50}, {"item": "amethyst_gold_bracelet", "prob": 50}, {"item": "sapphire_gold_bracelet", "prob": 50}, {"item": "aquamarine_gold_bracelet", "prob": 50}, {"item": "blue_topaz_gold_bracelet", "prob": 50}, {"item": "tourmaline_gold_bracelet", "prob": 50}, {"item": "alexandrite_gold_bracelet", "prob": 50}, {"item": "pearl_gold_bracelet", "prob": 50}, {"item": "onyx_gold_bracelet", "prob": 50}, {"item": "opal_gold_bracelet", "prob": 50}, {"item": "ruby_gold_bracelet", "prob": 50}, {"item": "garnet_gold_bracelet", "prob": 50}]}, {"id": "bracelets_platinum", "type": "item_group", "subtype": "distribution", "entries": [{"item": "platinum_bracelet", "prob": 50}, {"item": "citrine_platinum_bracelet", "prob": 50}, {"item": "diamond_platinum_bracelet", "prob": 50}, {"item": "emerald_platinum_bracelet", "prob": 50}, {"item": "peridot_platinum_bracelet", "prob": 50}, {"item": "amethyst_platinum_bracelet", "prob": 50}, {"item": "sapphire_platinum_bracelet", "prob": 50}, {"item": "aquamarine_platinum_bracelet", "prob": 50}, {"item": "blue_topaz_platinum_bracelet", "prob": 50}, {"item": "tourmaline_platinum_bracelet", "prob": 50}, {"item": "alexandrite_platinum_bracelet", "prob": 50}, {"item": "pearl_platinum_bracelet", "prob": 50}, {"item": "onyx_platinum_bracelet", "prob": 50}, {"item": "opal_platinum_bracelet", "prob": 50}, {"item": "ruby_platinum_bracelet", "prob": 50}, {"item": "garnet_platinum_bracelet", "prob": 50}]}, {"type": "item_group", "id": "shirts_summer_womens", "subtype": "distribution", "entries": [{"item": "camisole", "prob": 5}, {"item": "halter_top", "prob": 5}, {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 1}, {"item": "cheerleader_top_short", "prob": 1}]}, {"type": "item_group", "id": "pants_summer_womens", "subtype": "distribution", "entries": [{"item": "skirt", "prob": 5}, {"item": "skirt_denim", "prob": 5}, {"item": "skirt_leather", "prob": 5}, {"item": "skirt_long", "prob": 5}, {"item": "leggings", "prob": 3}, {"item": "tights", "prob": 4}, {"item": "hot_pants", "prob": 4}, {"item": "hot_pants_fur", "prob": 1}, {"item": "hot_pants_leather", "prob": 1}, {"item": "cheerleader_skirt", "prob": 1}, {"item": "cheerleader_skirt_short", "prob": 1}]}, {"id": "dress_shop", "type": "item_group", "items": [["skirt", 40], ["skirt_long", 40], ["skirt_denim", 15], ["skirt_leather", 5], ["dress", 120], ["sundress", 50], ["nanoskirt", 30], ["camisole", 60], ["corset", 20], ["corset_waist", 20], ["unitard", 5], ["leotard", 10], ["stockings", 50], ["garter_belt", 25], ["leggings", 20], ["tights", 50]]}, {"id": "dress_shoes", "type": "item_group", "items": [["dress_shoes", 50], ["heels", 50], ["espadrilles", 20]]}, {"id": "wedding_dresses", "type": "item_group", "items": [["dress", 50], ["veil_wedding", 150], ["dress_wedding", 150]]}, {"id": "wedding_suits", "type": "item_group", "items": [["tux", 100], ["suit", 80], ["waistcoat", 50], ["dress_shirt", 100], ["undershirt", 50]]}, {"id": "neckties", "type": "item_group", "items": [["tie_skinny", 40], ["tie_necktie", 40], ["tie_bow", 40], ["tie_clipon", 40]]}, {"type": "item_group", "id": "pants_male", "subtype": "distribution", "entries": [{"item": "jeans", "prob": 90}, {"item": "jeans_ripped", "prob": 10}, {"item": "jeans_skinny", "prob": 20}, {"item": "jeans_red", "prob": 50}, {"item": "shorts", "prob": 70}, {"item": "shorts_denim", "prob": 35}, {"item": "b_shorts", "prob": 30}, {"item": "shorts_cargo", "prob": 50}, {"item": "postman_shorts", "prob": 12}, {"item": "under_armor_shorts", "prob": 5}, {"item": "pants", "prob": 75}, {"item": "pants_leather", "prob": 60}, {"item": "pants_cargo", "prob": 70}, {"item": "pants_checkered", "prob": 55}, {"item": "pants_ski", "prob": 45}, {"item": "pants_tactical", "prob": 40}, {"item": "pants_fur", "prob": 5}, {"item": "pants_faux_fur", "prob": 15}, {"item": "hot_pants", "prob": 30}, {"item": "hot_pants_fur", "prob": 5}, {"item": "hot_pants_leather", "prob": 25}, {"item": "breeches", "prob": 25}, {"item": "pants", "variant": "pants_flag", "prob": 10}, {"item": "trousers_ninja", "prob": 1}, {"group": "pants_mens", "prob": 10}, {"item": "pants", "variant": "pants_flag", "prob": 70, "event": "independence_day"}, {"group": "christmas_pants_male", "prob": 150, "event": "christmas"}, {"group": "costume_pants", "prob": 150, "event": "halloween"}]}, {"type": "item_group", "id": "pants_female", "subtype": "distribution", "entries": [{"item": "nanoskirt", "prob": 10}, {"group": "pants_female_child", "prob": 290}]}, {"type": "item_group", "id": "pants_female_child", "subtype": "distribution", "entries": [{"group": "pants_male", "prob": 200}, {"item": "skirt", "prob": 75}, {"item": "skirt_long", "prob": 25}, {"item": "skirt_denim", "prob": 10}, {"item": "skirt_leather", "prob": 5}, {"item": "leggings", "prob": 15}, {"item": "cheerleader_skirt", "prob": 1}, {"item": "cheerleader_skirt_short", "prob": 1}, {"group": "costume_skirts", "prob": 200, "event": "halloween"}]}, {"type": "item_group", "id": "dresser", "ammo": 50, "magazine": 100, "items": [["jeans", 90], ["jeans_ripped", 10], ["jeans_skinny", 20], ["pants_checkered", 5], ["shorts", 70], ["shorts_denim", 50], ["pants", 75], ["leather_belt", 30], ["pants_leather", 60], ["pants_cargo", 70], ["pants_hiking", 1], {"item": "pants_tactical", "prob": 5}, ["shorts_cargo", 50], ["motorbike_pants", 5], ["breeches", 5], ["skirt", 75], ["skirt_long", 50], ["skirt_denim", 10], ["dress", 70], ["sundress", 50], ["camisole", 60], ["bra", 70], ["hairpin", 30], ["fc_hairpin", 5], ["barrette", 15], ["tieclip", 10], ["collarpin", 10], ["undershirt", 70], ["boxer_shorts", 50], {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 5}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, ["briefs", 40], ["boxer_briefs", 45], ["swim_briefs", 10], ["speedo", 5], {"group": "bikini", "prob": 20}, ["panties", 70], ["boy_shorts", 25], {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 70, "event": "independence_day"}, ["gown", 1], ["long_glove_white", 1], ["veil_wedding", 1], ["dress_wedding", 1], ["tshirt", 80], ["tshirt_cropped", 8], ["longshirt", 70], ["polo_shirt", 65], ["dress_shirt", 60], ["denim_shirt", 30], ["tank_top", 50], ["sweatshirt", 75], ["sweater", 75], ["turtleneck", 25], ["turtleneck_shirt", 25], ["hoodie", 65], ["hoodie_cropped", 6], ["jacket_light", 50], ["jacket_windbreaker", 25], ["jacket_varsity", 20], ["jacket_jean", 35], ["vest_jean", 15], ["gosling_jacket", 3], ["blazer", 35], ["towel", 40], ["jacket_leather", 30], ["tacjacket", 30], ["leather_police_jacket", 5], ["motorbike_armor", 5], ["poncho", 15], ["folding_poncho", 5], ["trenchcoat", 12], ["sleeveless_trenchcoat", 2], ["duster", 12], ["sleeveless_duster", 2], ["peacoat", 30], ["greatcoat", 15], ["vest", 15], ["mag_porn", 20], ["photo_album", 10], {"item": "lighter", "prob": 60, "charges": [0, -1]}, {"item": "ref_lighter", "prob": 2, "charges": [0, -1]}, {"item": "sewing_kit", "prob": 30, "charges": [0, -1]}, ["thread", 40], {"item": "flashlight", "prob": 40, "charges": [0, 300]}, ["suit", 60], ["waistcoat", 30], ["tophat", 10], ["bowhat", 10], ["cowboy_hat", 10], {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 2}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 70, "event": "independence_day"}, ["bullwhip", 1], ["10gal_hat", 3], ["glasses_monocle", 2], ["duct_tape", 60], {"group": "superglue", "prob": 20}, ["firecracker_pack", 5], ["firecracker", 5], ["wolfsuit", 3], ["dinosuit", 2], ["sharksuit", 2], ["felinesuit", 3], ["dragonsuit", 2], ["zentai", 5], {"item": "vibrator", "prob": 5, "charges": [0, 100]}, {"item": "condom", "prob": 30, "count": [1, 5]}, ["snuggie", 5], ["flyer", 10], ["socks", 70], ["socks_ankle", 50], ["socks_wool", 30], ["pocketwatch", 5], ["flask_hip", 2], ["bholster", 5], ["bb<PERSON>ster", 4], ["leg_bag", 8], ["leg_small_bag", 4], ["armrig", 8], ["ankle_wallet_pouch", 2], ["tux", 1], ["dress_wedding", 1], ["kasaya", 1], ["antar<PERSON>a", 1], ["uttarasanga", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["clogs", 2], ["wristwatch", 15], ["maid_dress", 1], ["maid_dress_short", 1], ["maid_hat", 1], ["knitting_needles", 1], ["survnote", 1], ["b_shorts", 15], ["halter_top", 30], ["kilt", 2], ["nanoskirt", 10], ["sleeveless_tunic", 5], ["fedora", 10], ["straw_hat", 5], ["straw_fedora", 5], ["cassock", 1], ["zucchetto", 1], ["cape_catholic", 1], ["nun_habit", 1], ["nun_wimple", 1], ["holy_symbol", 20], ["kufi", 5], ["kippah", 5], ["tallit_katan", 5], ["tallit_gadol", 1], {"group": "tefillin_sets", "prob": 1}, ["headscarf", 5], {"item": "kirpan", "container-item": "sheath", "prob": 1}, {"item": "kirpan_cheap", "container-item": "sheath", "prob": 3}, ["leg_sheath6", 1], ["gartersheath1", 5], ["gartersheath2", 1], ["robe", 10], ["<PERSON><PERSON><PERSON>", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["geta", 1], ["kimono", 2], ["yukata", 3], ["haori", 2], ["hakama", 2], ["eclipse_glasses", 1], ["thermos", 20], ["camera_bag", 5], {"group": "archery_quivers", "prob": 1}, ["santa_jacket", 1], ["santa_jacket_short", 1], ["santa_shorts", 1], ["santa_pants", 1], ["santa_gloves", 1], ["gloves_white", 5], ["santa_hat", 1], ["santa_belt", 1], ["santa_beard", 1], ["santa_dress", 1], ["santa_dress_long", 1], ["santa_dress_short", 1], ["cloak_vampire", 1], ["cloak_black", 1], ["robe_wizard", 1], ["ghost_robe", 1], ["grim_reaper_robe", 1], ["jacket_ninja", 1], ["bee_dress", 1], ["cheerleader_dress", 1], ["mummy_dress", 1], ["ninja_dress", 1], ["ninja_dress_sleeveless", 1], ["sinister_dress", 1], ["sinister_dress_short", 1], ["sinister_gown", 1], ["witch_dress", 1], ["mummy_dress", 1], ["witch_dress_long", 1], ["witch_dress_short", 1], ["gloves_black", 5], ["gloves_skeleton", 1], ["gloves_claws", 1], ["wizard_hat_costume", 1], ["pointed_hat", 1], ["hood_ninja", 1], ["balaclava_skull", 1], ["mask_skull", 1], ["zombie_mask", 2], ["cheerleader_skirt", 5], ["cheerleader_skirt_short", 1], ["trousers_ninja", 1], {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 5}, ["cheerleader_top_short", 1], ["flag_jumpsuit", 2], ["denim_overalls", 10], ["jumpsuit_skeleton", 1], ["mummy_jumpsuit", 1], ["flag_swimsuit", 2], ["pom_poms", 5], ["sinister_cane", 2], ["wizard_cane_cheap", 2], ["wizard_cane", 1], ["scythe_fake", 2], ["scythe_toy", 2], ["bodysuit_lycra", 5], {"group": "independence_all_clothing", "prob": 100, "event": "independence_day"}, {"group": "christmas_all_clothing", "prob": 100, "event": "christmas"}, {"group": "costume_all_clothing", "prob": 100, "event": "halloween"}]}, {"type": "item_group", "id": "shoes", "items": [["sneakers", 80], ["knee_high_boots", 20], ["thigh_high_boots", 20], ["boots", 70], ["flip_flops", 35], ["flip_flops_exp", 5], ["lowtops", 45], ["dress_shoes", 50], ["dance_shoes", 5], ["shoes_denim", 5], ["heels", 50], ["golf_shoes", 20], ["boots_combat", 10], ["boots_denim", 1], ["boots_hiking", 20], ["boots_western", 10], ["clogs", 1], ["leathersandals", 10], ["espadrilles", 5], ["rollerskates", 1], ["roller_blades", 5], ["roller_shoes_off", 1], ["mocassins", 20], {"item": "heels", "variant": "heels_flag", "prob": 70, "event": "independence_day"}]}, {"type": "item_group", "id": "pants", "items": [["jeans", 90], ["jeans_ripped", 10], ["jeans_skinny", 20], ["pants_checkered", 5], ["shorts", 70], ["shorts_denim", 35], ["pants_hiking", 2], {"item": "pants_tactical", "prob": 5}, ["pants", 75], ["leather_belt", 30], ["pants_leather", 60], ["pants_cargo", 70], ["shorts_cargo", 50], ["skirt", 75], ["skirt_long", 55], ["skirt_denim", 10], ["skirt_leather", 5], ["leggings", 15], ["under_armor_shorts", 20], ["dress", 70], ["sundress", 50], ["dress_wedding", 1], ["postman_shorts", 5], ["b_shorts", 15], ["kilt", 2], ["nanoskirt", 10], {"item": "pants", "variant": "pants_flag", "prob": 5}, {"group": "costume_pants_skirts", "prob": 1}, {"item": "pants", "variant": "pants_flag", "prob": 100, "event": "independence_day"}, {"group": "christmas_pants_male", "prob": 100, "event": "christmas"}, {"group": "costume_pants_skirts", "prob": 100, "event": "halloween"}]}, {"type": "item_group", "id": "shirts", "items": [["tshirt", 90], ["tshirt_cropped", 8], ["longshirt", 80], ["polo_shirt", 65], ["dress_shirt", 60], ["denim_shirt", 30], ["tank_top", 50], ["sweatshirt", 75], ["sweater", 75], ["turtleneck", 25], ["turtleneck_shirt", 25], ["hoodie", 65], ["hoodie_cropped", 6], ["under_armor", 20], ["jersey", 40], ["camisole", 60], ["tie_clipon", 10], ["tie_necktie", 10], ["tie_skinny", 10], ["tieclip", 5], ["collarpin", 5], ["postman_shirt", 5], ["halter_top", 50], ["rashguard", 10], ["sleeveless_tunic", 5], ["poncho", 20], {"item": "tshirt", "variant": "flag_shirt", "prob": 50, "event": "independence_day"}, {"group": "costume_shirts", "prob": 70, "event": "halloween"}]}, {"type": "item_group", "id": "jackets", "items": [["jacket_light", 50], ["jacket_windbreaker", 25], ["jacket_varsity", 20], ["jacket_jean", 35], ["blazer", 35], ["jacket_leather", 30], ["tacjacket", 30], ["leather_police_jacket", 8], ["gosling_jacket", 5], ["coat_rain", 50], ["trenchcoat", 12], ["trenchcoat_steampunk", 1], ["duster", 12], {"group": "neckties", "prob": 5}, ["tieclip", 2], ["collarpin", 2], ["apron_cotton", 5], ["apron_leather", 1], ["apron_plastic", 1], {"group": "costume_jackets", "prob": 1}, {"group": "costume_jackets", "prob": 50, "event": "halloween"}, {"group": "christmas_jackets", "prob": 50, "event": "christmas"}]}, {"type": "item_group", "id": "suits", "items": [["dress_shirt", 100], {"group": "neckties", "prob": 50}, ["tieclip", 30], ["collarpin", 30], ["undershirt", 100], ["suit", 100], ["waistcoat", 70], ["tophat", 30], ["tux", 50], ["gown", 50], ["long_glove_white", 30], ["breeches", 10]]}, {"type": "item_group", "id": "winter", "ammo": 50, "magazine": 100, "items": [["coat_winter", 50], ["peacoat", 30], ["ski_jacket", 40], ["greatcoat", 15], ["gloves_light", 35], ["mittens", 30], ["gloves_wool", 33], ["gloves_wool_fingerless", 15], ["thermal_socks", 10], ["thermal_gloves", 10], ["thermal_suit", 10], ["thermal_mask", 10], ["thermal_outfit", 5], ["gloves_winter", 40], ["gloves_liner", 25], ["gloves_leather", 45], ["bellyband", 10], ["scarf", 45], ["knit_scarf", 35], ["long_knit_scarf", 15], ["scarf_long", 2], ["hat_cotton", 45], ["hat_newsboy", 20], ["hat_knit", 25], ["hat_fur", 15], ["hat_faux_fur", 20], ["pants_ski", 60], ["mask_ski", 15], ["long_underpants", 40], ["long_undertop", 40], ["union_suit", 40], ["tights", 20], ["arm_warmers", 20], ["leg_warmers", 20], ["balclava", 15], ["santa_jacket", 1], ["santa_jacket_short", 1], ["santa_shorts", 1], ["santa_pants", 1], ["santa_gloves", 1], ["gloves_white", 1], ["santa_hat", 1], ["santa_belt", 1], ["santa_beard", 1], ["santa_dress", 1], ["santa_dress_long", 1], ["santa_dress_short", 1]]}, {"type": "item_group", "id": "fur_coats_unisex", "subtype": "distribution", "entries": [{"item": "trenchcoat_fur", "prob": 4}, {"item": "duster_fur", "prob": 4}, {"item": "sleeveless_trenchcoat_fur", "prob": 2}, {"item": "sleeveless_duster_fur", "prob": 2}, {"item": "coat_fur", "prob": 4}, {"item": "cloak_fur", "prob": 1}]}, {"type": "item_group", "id": "fur_pants_womens", "subtype": "distribution", "entries": [{"item": "hot_pants_fur", "prob": 100}]}, {"type": "item_group", "id": "fur_hats_unisex", "subtype": "distribution", "entries": [{"item": "hat_fur", "prob": 100}]}, {"type": "item_group", "id": "fur_gloves_unisex", "subtype": "distribution", "entries": [{"item": "gloves_fur", "prob": 30}]}, {"type": "item_group", "id": "fur_shoes_unisex", "subtype": "distribution", "entries": [{"item": "boots_fur", "prob": 100}]}, {"type": "item_group", "id": "fancyfurs", "items": [["hat_fur", 300], ["hat_faux_fur", 300], ["sleeveless_trenchcoat_fur", 10], ["sleeveless_duster_fur", 10], ["sleeveless_trenchcoat_faux_fur", 10], ["coat_faux_fur", 300], ["coat_fur", 400], ["gloves_fur", 300], ["boots_fur", 200], ["trenchcoat_faux_fur", 50], ["duster_faux_fur", 50], ["pants_faux_fur", 50]]}, {"type": "item_group", "id": "hatstore_hats", "items": [["hat_cotton", 30], ["hat_knit", 40], ["hat_faux_fur", 30], ["hat_fur", 20], ["hat_newsboy", 20], ["hat_sombrero", 10], ["fedora", 100], ["straw_hat", 100], ["straw_fedora", 100], ["hat_chef", 10], ["maid_hat", 5], ["hat_golf", 50], ["hat_ball", 100], ["postman_hat", 10], ["tophat", 50], ["bowhat", 50], ["cowboy_hat", 100], {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 5}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 150, "event": "independence_day"}, ["10gal_hat", 10], ["kufi", 10], ["kippah", 30], ["<PERSON><PERSON><PERSON>", 10], ["balclava", 20], ["porkpie", 30], {"group": "costume_hats_hoods", "prob": 450, "event": "halloween"}, {"group": "christmas_hats", "prob": 450, "event": "christmas"}]}, {"type": "item_group", "id": "hatstore_accessories", "items": [["scarf", 50], ["knit_scarf", 50], ["long_knit_scarf", 30], ["scarf_long", 30], ["bandana", 100], ["hairpin", 50], ["fc_hairpin", 20], ["barrette", 30]]}, {"type": "item_group", "id": "shoestore_shoes", "items": [["sneakers", 100], ["knee_high_boots", 40], ["thigh_high_boots", 40], ["boots", 100], ["flip_flops", 50], ["flip_flops_exp", 5], ["lowtops", 100], ["dress_shoes", 50], ["dance_shoes", 20], ["shoes_denim", 5], ["heels", 100], ["golf_shoes", 40], ["boots_denim", 5], ["boots_hiking", 40], ["boots_western", 60], ["clogs", 60], ["leathersandals", 100], ["espadrilles", 50], ["rollerskates", 10], ["roller_blades", 20], ["roller_shoes_off", 10], ["boots_rubber", 20], ["clownshoes", 10], ["mocassins", 40], {"item": "heels", "variant": "heels_flag", "prob": 120, "event": "independence_day"}]}, {"type": "item_group", "id": "shoestore_accessories", "items": [["socks", 100], ["socks_ankle", 75], ["thermal_socks", 25], ["socks_wool", 50], ["string_36", 200]]}, {"type": "item_group", "id": "bags", "subtype": "distribution", "entries": [{"item": "backpack", "prob": 38}, {"item": "backpack_hiking", "prob": 3}, {"item": "backpack_hunting", "prob": 1}, {"item": "petpack", "prob": 3}, {"item": "backpack_tactical_large", "prob": 1}, {"item": "bigback", "prob": 1}, {"item": "travelpack", "prob": 5}, {"item": "purse", "prob": 40}, {"item": "mbag", "prob": 20}, {"item": "slingpack", "prob": 8}, {"item": "rucksack", "prob": 20}, {"item": "backpack_denim", "prob": 3}, {"item": "backpack_leather", "prob": 8}, {"item": "briefcase", "prob": 10}, {"item": "leg_bag", "prob": 1}, {"item": "quiver_takedown_bow", "prob": 1}]}, {"type": "item_group", "id": "large_bags", "subtype": "distribution", "entries": [{"item": "backpack", "prob": 38}, {"item": "backpack_hiking", "prob": 3}, {"item": "backpack_hunting", "prob": 1}, {"item": "daypack", "prob": 3}, {"item": "backpack_tactical_large", "prob": 1}, {"group": "molle_backpacks_and_accessories", "prob": 1}, {"item": "duffelbag", "prob": 5}, {"item": "bigback", "prob": 1}, {"item": "travelpack", "prob": 5}, {"item": "rucksack", "prob": 20}]}, {"type": "item_group", "id": "small_bags", "subtype": "distribution", "entries": [{"item": "petpack", "prob": 3}, {"item": "purse", "prob": 40}, {"item": "mbag", "prob": 20}, {"item": "slingpack", "prob": 8}, {"item": "backpack_denim", "prob": 3}, {"item": "backpack_leather", "prob": 8}, {"item": "briefcase", "prob": 10}]}, {"type": "item_group", "id": "gun_cases", "subtype": "distribution", "entries": [{"item": "rifle_case_soft", "prob": 50}, {"item": "rifle_case_soft_leather", "prob": 30}, {"item": "rifle_case_soft_2", "prob": 40}, {"item": "rifle_case_soft_leather_2", "prob": 30}, {"item": "rifle_case_xl_soft_leather", "prob": 30}]}, {"type": "item_group", "id": "bags_trip", "subtype": "distribution", "entries": [{"item": "suitcase_l", "prob": 37}, {"item": "suitcase_m", "prob": 55}, {"item": "duffelbag", "prob": 8}]}, {"type": "item_group", "id": "dresses", "items": [["dress", 55], ["sundress", 43], ["gown", 10], ["dress_wedding", 2], {"group": "costume_dresses", "prob": 1}, {"group": "costume_dresses", "prob": 15, "event": "halloween"}, {"group": "christmas_dresses", "prob": 15, "event": "christmas"}]}, {"type": "item_group", "id": "female_underwear_top", "items": [["corset", 5], ["binder_top", 1], ["corset_waist", 5], {"group": "female_underwear_top_child", "prob": 190}]}, {"type": "item_group", "id": "female_underwear_top_child", "items": [["bra", 70], ["sports_bra", 50], ["camisole", 30], ["halter_top", 30], ["bikini_top", 5], ["bikini_top_short", 1], ["bikini_top_leather", 3], ["bikini_top_fur", 2], {"item": "bikini_top", "variant": "flag_bikini_top", "prob": 70, "event": "independence_day"}]}, {"type": "item_group", "id": "female_underwear_bottom", "items": [["panties", 70], ["boy_shorts", 50], {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 70, "event": "independence_day"}, ["bikini_bottom", 9], ["bikini_bottom_short", 1], {"item": "bikini_bottom", "variant": "flag_bikini_bottom", "prob": 70, "event": "independence_day"}]}, {"type": "item_group", "id": "male_underwear_top", "items": [["undershirt", 50], ["binder_top", 2], ["long_undertop", 20], ["thermal_shirt", 10], ["under_armor", 20], ["tank_top", 50]]}, {"type": "item_group", "id": "male_underwear_bottom", "items": [["boxer_shorts", 70], {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 5}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, ["briefs", 30], ["boxer_briefs", 50], ["long_underpants", 20], ["under_armor_shorts", 20], ["trunks", 10], ["swim_briefs", 5], ["speedo", 1]]}, {"type": "item_group", "id": "allclothes", "subtype": "distribution", "entries": [{"item": "jeans", "prob": 90}, {"item": "jeans_ripped", "prob": 10}, {"item": "jeans_skinny", "prob": 20}, {"item": "pants_checkered", "prob": 5}, {"item": "shorts", "prob": 70}, {"item": "shorts_denim", "prob": 35}, {"item": "ski_jacket", "prob": 40}, {"item": "pants", "prob": 75}, {"item": "breeches", "prob": 10}, {"item": "leather_belt", "prob": 30}, {"item": "suit", "prob": 60}, {"item": "waistcoat", "prob": 30}, {"item": "tophat", "prob": 5}, {"item": "bowhat", "prob": 10}, {"item": "cowboy_hat", "prob": 10}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 1}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 70, "event": "independence_day"}, {"item": "boots_denim", "prob": 2}, {"item": "shoes_denim", "prob": 5}, {"item": "boots_western", "prob": 8}, {"item": "glasses_monocle", "prob": 2}, {"item": "pants_leather", "prob": 60}, {"item": "pants_cargo", "prob": 70}, {"item": "pants_hiking", "prob": 2}, {"item": "shorts_cargo", "prob": 50}, {"item": "skirt", "prob": 75}, {"item": "skirt_long", "prob": 55}, {"item": "skirt_denim", "prob": 10}, {"item": "skirt_leather", "prob": 5}, {"item": "tshirt", "prob": 70}, {"item": "longshirt", "prob": 80}, {"item": "polo_shirt", "prob": 65}, {"item": "dress_shirt", "prob": 60}, {"item": "denim_shirt", "prob": 30}, {"item": "tank_top", "prob": 50}, {"item": "camisole", "prob": 30}, {"item": "bra", "prob": 30}, {"item": "undershirt", "prob": 30}, {"item": "boxer_shorts", "prob": 30}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 2}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, {"item": "briefs", "prob": 15}, {"item": "boxer_briefs", "prob": 20}, {"item": "swim_briefs", "prob": 10}, {"item": "speedo", "prob": 5}, {"group": "bikini", "prob": 10}, {"item": "panties", "prob": 30}, {"item": "boy_shorts", "prob": 25}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 70, "event": "independence_day"}, {"item": "sweatshirt", "prob": 75}, {"item": "sweater", "prob": 75}, {"item": "turtleneck", "prob": 25}, {"item": "turtleneck_shirt", "prob": 25}, {"item": "hoodie", "prob": 65}, {"item": "hoodie_cropped", "prob": 6}, {"item": "jacket_light", "prob": 50}, {"item": "jacket_windbreaker", "prob": 25}, {"item": "jacket_varsity", "prob": 20}, {"item": "gosling_jacket", "prob": 5}, {"item": "jacket_jean", "prob": 35}, {"item": "vest_jean", "prob": 20}, {"item": "blazer", "prob": 35}, {"item": "jacket_leather", "prob": 30}, {"item": "tacjacket", "prob": 30}, {"item": "leather_police_jacket", "prob": 10}, {"item": "coat_winter", "prob": 50}, {"item": "peacoat", "prob": 30}, {"item": "greatcoat", "prob": 15}, {"item": "gloves_light", "prob": 35}, {"item": "mittens", "prob": 30}, {"item": "gloves_wool", "prob": 33}, {"item": "gloves_wool_fingerless", "prob": 10}, {"item": "thermal_socks", "prob": 2}, {"item": "thermal_gloves", "prob": 2}, {"item": "thermal_suit", "prob": 2}, {"item": "thermal_mask", "prob": 2}, {"item": "thermal_outfit", "prob": 1}, {"item": "thermal_shirt", "prob": 1}, {"item": "gloves_winter", "prob": 40}, {"item": "gloves_liner", "prob": 25}, {"item": "gloves_leather", "prob": 45}, {"item": "gloves_work", "prob": 5}, {"item": "scarf", "prob": 45}, {"item": "hat_golf", "prob": 30}, {"item": "knit_scarf", "prob": 35}, {"item": "long_knit_scarf", "prob": 5}, {"item": "scarf_long", "prob": 1}, {"item": "hat_cotton", "prob": 45}, {"item": "hat_knit", "prob": 25}, {"item": "hat_newsboy", "prob": 20}, {"item": "hat_sombrero", "prob": 3}, {"item": "hat_fur", "prob": 15}, {"item": "hat_faux_fur", "prob": 20}, {"item": "under_armor", "prob": 20}, {"item": "under_armor_shorts", "prob": 20}, {"item": "tights", "prob": 20}, {"item": "leggings", "prob": 20}, {"item": "garter_belt", "prob": 10}, {"item": "stockings", "prob": 20}, {"item": "balclava", "prob": 15}, {"item": "pants_ski", "prob": 60}, {"item": "long_underpants", "prob": 40}, {"item": "long_undertop", "prob": 40}, {"item": "union_suit", "prob": 5}, {"item": "leotard", "prob": 3}, {"item": "unitard", "prob": 1}, {"item": "arm_warmers", "prob": 20}, {"item": "leg_warmers", "prob": 20}, {"item": "trenchcoat_leather", "prob": 12}, {"item": "trenchcoat_faux_fur", "prob": 6}, {"item": "sleeveless_trenchcoat", "prob": 2}, {"item": "sleeveless_trenchcoat_leather", "prob": 2}, {"item": "sleeveless_trenchcoat_faux_fur", "prob": 2}, {"item": "trenchcoat_leather", "prob": 12}, {"item": "sleeveless_trenchcoat_fur", "prob": 1}, {"item": "duster_leather", "prob": 12}, {"item": "duster_faux_fur", "prob": 6}, {"item": "sleeveless_duster", "prob": 2}, {"item": "sleeveless_duster_leather", "prob": 2}, {"item": "sleeveless_duster_faux_fur", "prob": 2}, {"item": "sleeveless_duster_fur", "prob": 1}, {"item": "cloak", "prob": 5}, {"item": "cloak_wool", "prob": 5}, {"item": "house_coat", "prob": 25}, {"item": "flotation_vest", "prob": 1}, {"item": "fishing_waders", "prob": 5}, {"item": "rashguard", "prob": 15}, {"item": "wetsuit_jacket", "prob": 10}, {"item": "wetsuit_top", "prob": 10}, {"item": "wetsuit_top_sleeved", "prob": 10}, {"item": "wetsuit_pants", "prob": 10}, {"item": "wetsuit_shorts", "prob": 10}, {"item": "flag_swimsuit", "prob": 15}, {"item": "wetsuit", "prob": 5}, {"item": "wetsuit_thick", "prob": 1}, {"item": "wetsuit_spring", "prob": 5}, {"item": "wetsuit_spring_sleeveless", "prob": 5}, {"item": "wetsuit_gloves", "prob": 10}, {"item": "wetsuit_gloves_thick", "prob": 1}, {"item": "wetsuit_booties", "prob": 10}, {"item": "wetsuit_booties_thick", "prob": 1}, {"item": "wetsuit_hood", "prob": 5}, {"item": "wetsuit_hood_thick", "prob": 5}, {"item": "swim_cap", "prob": 15}, {"item": "dive_bag", "prob": 5}, {"item": "dry_bag", "prob": 5}, {"item": "dry_duffelbag", "prob": 5}, {"item": "dry_bag_large", "prob": 3}, {"item": "jedi_cloak", "prob": 1}, {"item": "apron_cotton", "prob": 30}, {"item": "waist_apron_long", "prob": 10}, {"item": "waist_apron_short", "prob": 5}, {"item": "apron_leather", "prob": 5}, {"item": "apron_plastic", "prob": 5}, {"item": "clown_suit", "prob": 1}, {"item": "clownshoes", "prob": 1}, {"item": "clown_wig", "prob": 1}, {"item": "clown_nose", "prob": 1}, {"item": "bondage_suit", "prob": 1}, {"item": "bondage_mask", "prob": 1}, {"item": "zentai", "prob": 1}, {"item": "kasaya", "prob": 1}, {"item": "antar<PERSON>a", "prob": 1}, {"item": "uttarasanga", "prob": 1}, {"item": "<PERSON><PERSON><PERSON><PERSON>", "prob": 1}, {"item": "corset", "prob": 10}, {"item": "corset_waist", "prob": 10}, {"item": "chestwrap", "prob": 5}, {"item": "bellywrap", "prob": 2}, {"item": "bellyband", "prob": 2}, {"item": "boots_combat", "prob": 10}, {"item": "pants_army", "prob": 10}, {"item": "jacket_army", "prob": 10}, {"item": "jacket_army_modern", "prob": 2}, {"item": "combat_shirt", "prob": 2}, {"item": "winter_pants_army", "prob": 10}, {"item": "winter_jacket_army", "prob": 10}, {"item": "winter_gloves_army", "prob": 10}, {"item": "tank_top", "variant": "tank_top_camo", "prob": 20}, {"item": "tux", "prob": 1}, {"item": "gown", "prob": 1}, {"item": "long_glove_white", "prob": 1}, {"item": "veil_wedding", "prob": 1}, {"item": "dress_wedding", "prob": 1}, {"item": "porkpie", "prob": 5}, {"item": "tie_bow", "prob": 5}, {"item": "tie_clipon", "prob": 5}, {"item": "tie_necktie", "prob": 5}, {"item": "tie_skinny", "prob": 5}, {"item": "tieclip", "prob": 2}, {"item": "collarpin", "prob": 2}, {"item": "jersey", "prob": 40}, {"item": "postman_shorts", "prob": 5}, {"item": "postman_shirt", "prob": 5}, {"item": "postman_hat", "prob": 5}, {"item": "maid_dress", "prob": 1}, {"item": "maid_dress_short", "prob": 1}, {"item": "maid_hat", "prob": 1}, {"item": "halter_top", "prob": 50}, {"item": "kilt", "prob": 2}, {"item": "nanoskirt", "prob": 10}, {"item": "sleeveless_tunic", "prob": 5}, {"item": "ear_spool", "prob": 30}, {"item": "fedora", "prob": 5}, {"item": "straw_hat", "prob": 5}, {"item": "straw_fedora", "prob": 5}, {"item": "kufi", "prob": 2}, {"item": "kippah", "prob": 2}, {"item": "thawb", "prob": 1}, {"item": "kittel", "prob": 1}, {"item": "cassock", "prob": 1}, {"item": "zucchetto", "prob": 1}, {"item": "cape_catholic", "prob": 1}, {"item": "nun_habit", "prob": 1}, {"item": "nun_wimple", "prob": 1}, {"item": "robe", "prob": 5}, {"item": "<PERSON><PERSON><PERSON>", "prob": 1}, {"item": "<PERSON><PERSON><PERSON><PERSON>", "prob": 1}, {"item": "geta", "prob": 1}, {"item": "kimono", "prob": 2}, {"item": "yukata", "prob": 4}, {"item": "haori", "prob": 1}, {"item": "hakama", "prob": 2}, {"item": "santa_jacket", "prob": 1}, {"item": "santa_jacket_short", "prob": 1}, {"item": "santa_shorts", "prob": 1}, {"item": "santa_pants", "prob": 1}, {"item": "santa_gloves", "prob": 1}, {"item": "gloves_white", "prob": 5}, {"item": "santa_hat", "prob": 1}, {"item": "santa_belt", "prob": 1}, {"item": "santa_beard", "prob": 1}, {"item": "santa_dress", "prob": 1}, {"item": "santa_dress_long", "prob": 1}, {"item": "santa_dress_short", "prob": 1}, {"item": "cloak_vampire", "prob": 1}, {"item": "cloak_black", "prob": 1}, {"item": "robe_wizard", "prob": 1}, {"item": "ghost_robe", "prob": 1}, {"item": "grim_reaper_robe", "prob": 1}, {"item": "jacket_ninja", "prob": 1}, {"item": "bee_dress", "prob": 1}, {"item": "cheerleader_dress", "prob": 1}, {"item": "mummy_dress", "prob": 1}, {"item": "ninja_dress", "prob": 1}, {"item": "ninja_dress_sleeveless", "prob": 1}, {"item": "sinister_dress", "prob": 1}, {"item": "sinister_dress_short", "prob": 1}, {"item": "sinister_gown", "prob": 1}, {"item": "witch_dress", "prob": 1}, {"item": "mummy_dress", "prob": 1}, {"item": "witch_dress_long", "prob": 1}, {"item": "witch_dress_short", "prob": 1}, {"item": "gloves_black", "prob": 5}, {"item": "gloves_skeleton", "prob": 1}, {"item": "gloves_claws", "prob": 1}, {"item": "wizard_hat_costume", "prob": 1}, {"item": "pointed_hat", "prob": 1}, {"item": "hood_ninja", "prob": 1}, {"item": "balaclava_skull", "prob": 1}, {"item": "mask_skull", "prob": 1}, {"item": "zombie_mask", "prob": 1}, {"item": "cheerleader_skirt", "prob": 5}, {"item": "cheerleader_skirt_short", "prob": 1}, {"item": "pants", "variant": "pants_flag", "prob": 5}, {"item": "trousers_ninja", "prob": 1}, {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 5}, {"item": "cheerleader_top_short", "prob": 1}, {"item": "flag_jumpsuit", "prob": 2}, {"item": "denim_overalls", "prob": 10}, {"item": "jumpsuit_skeleton", "prob": 1}, {"item": "mummy_jumpsuit", "prob": 1}, {"item": "bodysuit_lycra", "prob": 5}, {"item": "flag_swimsuit", "prob": 10}]}, {"type": "item_group", "id": "allclothes_damaged", "subtype": "distribution", "items": [{"group": "allclothes", "damage": [3, 4]}]}, {"type": "item_group", "id": "swimmer_set_any", "//": "clothing sets for swimming", "subtype": "distribution", "entries": [{"group": "swimmer_accessories", "prob": 20}, {"group": "swimmer_glasses", "prob": 15}, {"group": "swimmer_head", "prob": 25}, {"group": "swimmer_torso", "prob": 50}, {"group": "swimmer_pants", "prob": 75}, {"group": "swimmer_gloves", "prob": 5}, {"group": "swimmer_shoes", "prob": 25}, {"group": "swimmer_wetsuit", "prob": 50}]}, {"type": "item_group", "id": "swimmer_accessories", "items": [["dive_bag", 35], ["dry_bag", 35], ["dry_duffelbag", 35], ["dry_bag_large", 20], ["wicker_backpack", 10], ["flotation_vest", 30], ["swimming_kickboard", 5], ["diving_watch", 5], ["wristwatch", 5], ["small_scuba_tank", 5], ["scuba_tank", 5], ["whistle", 3], {"item": "camera", "container-item": "camera_bag", "prob": 2, "charges": [0, 150]}, ["camera_bag", 5]]}, {"type": "item_group", "id": "swimmer_glasses", "items": [["goggles_swim", 80], ["fancy_sunglasses", 5], ["fitover_sunglasses", 5], ["sunglasses", 10]]}, {"type": "item_group", "id": "swimmer_head", "items": [["swim_cap", 50], ["wetsuit_hood", 20], ["wetsuit_hood_thick", 5], ["rebreather", 5], ["straw_hat", 45], ["hat_sombrero", 10]]}, {"type": "item_group", "id": "swimmer_torso", "items": [["bikini_top", 85], ["bikini_top_fur", 5], ["bikini_top_leather", 5], ["bikini_top_short", 20], ["sports_bra", 10], ["wetsuit_jacket", 10], ["wetsuit_top", 25], ["wetsuit_top_sleeved", 25], ["rashguard", 25], ["thermal_shirt", 20], ["under_armor", 15], {"item": "tank_top", "variant": "tank_top_camo", "prob": 6}, ["tank_top", 6], ["halter_top", 6], ["tshirt", 15], {"item": "rashguard", "variant": "flag_rashguard_shirt", "prob": 60, "event": "independence_day"}, {"item": "tshirt", "variant": "flag_shirt", "prob": 20, "event": "independence_day"}, {"item": "bikini_top", "variant": "flag_bikini_top", "prob": 50, "event": "independence_day"}]}, {"type": "item_group", "id": "swimmer_torso_no_bikini", "//": "For use in death drops where bikinis are paired separately", "items": [["sports_bra", 10], ["wetsuit_jacket", 10], ["wetsuit_top", 25], ["wetsuit_top_sleeved", 25], ["rashguard", 25], ["thermal_shirt", 20], ["under_armor", 15], {"item": "tank_top", "variant": "tank_top_camo", "prob": 6}, ["tank_top", 6], ["halter_top", 6], ["tshirt", 15], {"item": "rashguard", "variant": "flag_rashguard_shirt", "prob": 60, "event": "independence_day"}, {"item": "tshirt", "variant": "flag_shirt", "prob": 20, "event": "independence_day"}]}, {"type": "item_group", "id": "swimmer_pants", "items": [["trunks", 65], ["bikini_bottom", 95], ["bikini_bottom_short", 20], ["swim_briefs", 35], ["speedo", 10], ["wetsuit_pants", 25], ["wetsuit_shorts", 35], ["hot_pants", 30], ["under_armor_shorts", 15], ["skirt", 10], ["skirt_grass", 15], ["nanoskirt", 5], {"item": "bikini_bottom", "variant": "flag_bikini_bottom", "prob": 50, "event": "independence_day"}]}, {"type": "item_group", "id": "swimmer_pants_no_bikini", "//": "For use in death drops where bikinis are paired separately", "items": [["trunks", 65], ["swim_briefs", 35], ["speedo", 10], ["wetsuit_pants", 25], ["wetsuit_shorts", 35], ["hot_pants", 30], ["under_armor_shorts", 15], ["skirt", 10], ["skirt_grass", 15], ["nanoskirt", 5]]}, {"type": "item_group", "id": "swimmer_gloves", "items": [["wetsuit_gloves", 95], ["wetsuit_gloves_thick", 5], ["gloves_fingerless", 3], ["gloves_light", 2]]}, {"type": "item_group", "id": "swimmer_shoes", "items": [["flip_flops", 50], ["flip_flops_exp", 5], ["swim_fins", 15], ["wetsuit_booties", 20], ["wetsuit_booties_thick", 5], ["bastsandals", 5], ["straw_sandals", 5], ["espadrilles", 5]]}, {"type": "item_group", "id": "swimmer_wetsuit", "items": [["wetsuit", 35], ["wetsuit_thick", 5], ["wetsuit_spring", 25], ["wetsuit_spring_sleeveless", 25], ["flag_swimsuit", 65], {"item": "flag_swimsuit", "variant": "flag_swimsuit", "prob": 60, "event": "independence_day"}, {"item": "bodysuit_lycra", "variant": "flag_suit", "prob": 10, "event": "independence_day"}]}, {"type": "item_group", "id": "swimmer_items", "//": "Not really clothing, but connected to the swimmer sets of clothing.", "items": [["towel", 60], ["beach_volleyball", 40], ["mag_swimming", 30], ["manual_swimming", 10], ["mag_porn", 5], ["mag_tv", 5], ["mag_glam", 5], ["mag_dude", 5], {"group": "SUS_book_nonf_soft_sports", "prob": 5}, ["diveknife", 5], ["sports_drink", 5], ["water_clean", 5], ["fish_sandwich", 2], ["mango", 1], ["hand_pump", 5], ["folded_inflatable_boat", 1]]}, {"type": "item_group", "id": "diving_head", "//": "Only diving appropriate headgear, not including masks or glasses.", "items": [["wetsuit_hood", 45], ["wetsuit_hood_thick", 45], ["swim_cap", 10]]}, {"type": "item_group", "id": "diving_masks", "//": "Only diving appropriate masks, it needs to let the user breath underwater.", "items": [["rebreather", 100], ["rebreather_cartridge_o2", 100]]}, {"type": "item_group", "id": "diving_glasses", "//": "Only diving appropriate glasses, it needs to let the user see underwater.", "items": [["goggles_swim", 100]]}, {"type": "item_group", "id": "diving_suits", "//": "Only diving appropriate (even if not optimal) suits of torso + pants.", "items": [{"group": "diving_wetsuit", "prob": 95}, {"distribution": [{"group": "diving_torso"}, {"group": "diving_pants"}], "prob": 5}]}, {"type": "item_group", "id": "diving_wetsuit", "//": "Only diving appropriate suits.", "items": [["wetsuit", 50], ["wetsuit_thick", 50], ["wetsuit_spring", 5], ["wetsuit_spring_sleeveless", 5], ["flag_swimsuit", 1], {"item": "flag_swimsuit", "variant": "flag_swimsuit", "prob": 15, "event": "independence_day"}]}, {"type": "item_group", "id": "diving_torso", "//": "Only diving appropriate torso clothing.", "items": [["wetsuit_top_sleeved", 60], ["wetsuit_top", 40], ["wetsuit_jacket", 30], ["bikini_top", 5], ["bikini_top_short", 1]]}, {"type": "item_group", "id": "diving_pants", "//": "Only diving appropriate legs clothing.  To be expanded later with rash guards.", "items": [["wetsuit_pants", 80], ["wetsuit_shorts", 20], ["bikini_bottom", 5], ["bikini_bottom_short", 1], ["swim_briefs", 5], ["speedo", 1]]}, {"type": "item_group", "id": "diving_gloves", "//": "Only diving appropriate gloves.", "items": [["wetsuit_gloves", 50], ["wetsuit_gloves_thick", 50]]}, {"type": "item_group", "id": "diving_shoes", "//": "Only diving appropriate footgear.", "items": [["swim_fins", 50], ["wetsuit_booties", 50], ["wetsuit_booties_thick", 50]]}, {"type": "item_group", "id": "diving_accessories", "//": "Only diving appropriate accesories.", "items": [["dive_bag", 25], ["dry_bag", 25], ["dry_duffelbag", 25], ["dry_bag_large", 15], ["diving_watch", 25], ["small_scuba_tank", 25], ["scuba_tank", 25]]}, {"type": "item_group", "id": "lab_shoes", "items": [["sneakers", 80], ["boots", 70], ["boots_steel", 50], {"item": "toecaps", "prob": 30}, ["sneakers_steel", 35], ["boots_hiking", 40], ["dress_shoes", 50], ["boots_denim", 1], ["shoes_denim", 1]]}, {"type": "item_group", "id": "lab_torso", "items": [["coat_lab", 20], ["coat_lab", 20], ["coat_lab", 20], ["coat_lab", 20], ["tshirt", 80], ["longshirt", 80], ["polo_shirt", 65], ["dress_shirt", 60], ["denim_shirt", 20], ["dress", 70], ["sweatshirt", 75], ["sweater", 75], ["turtleneck", 25], ["turtleneck_shirt", 25], ["hoodie", 65], ["hoodie_cropped", 6], ["jumpsuit", 20], ["lanyard", 10], ["badge_doctor", 10], ["hazmat_suit", 5], ["cleansuit", 10]]}, {"type": "item_group", "id": "lab_pants", "items": [["jeans", 90], ["jeans_skinny", 10], ["pants_checkered", 5], ["pants", 75], ["pants_cargo", 70], ["skirt", 75], ["skirt_long", 60], ["skirt_denim", 10]]}, {"type": "item_group", "id": "postman_carried", "subtype": "collection", "container-item": "mbag", "on_overflow": "spill", "items": [{"item": "letter", "container-item": "envelope"}, {"item": "letter", "container-item": "envelope"}, {"item": "postcard", "container-item": "envelope"}, {"item": "letter", "count": [0, 5]}, {"item": "postcard", "count": [0, 5]}, {"item": "envelope", "count": [0, 5]}, {"item": "newest_newspaper", "count": [0, 5]}]}, {"type": "item_group", "id": "postman_gear", "subtype": "collection", "items": [{"distribution": [{"item": "postman_hat", "prob": 80}, {"item": "hat_ball", "prob": 20}]}, {"item": "postman_shirt"}, {"item": "postman_shorts"}, {"group": "postman_carried"}, {"item": "wristwatch"}, {"item": "leather_belt", "prob": 70}, {"distribution": [{"distribution": [{"item": "briefs"}, {"item": "boxer_briefs"}, {"item": "boxer_shorts"}]}, {"collection": [{"distribution": [{"item": "bra"}, {"item": "sports_bra"}]}, {"item": "panties"}]}]}, {"item": "socks"}, {"item": "sneakers"}]}, {"type": "item_group", "subtype": "distribution", "id": "eod_armor", "entries": [{"collection": [{"item": "helmet_eod", "charges": [0, 500]}, {"item": "jacket_eod"}, {"item": "trousers_eod"}, {"item": "foot_protectors_eod"}, {"item": "gloves_eod", "prob": 20}], "prob": 100}, {"collection": [{"item": "jacket_eod_light"}, {"item": "face_shield_eod"}, {"item": "trousers_eod_light"}], "prob": 20}]}, {"type": "item_group", "id": "mil_armor", "items": [["pants_army", 10], ["jacket_army_modern", 5], ["jacket_army_modern", 5], ["winter_pants_army", 30], ["winter_jacket_army", 30], ["winter_gloves_army", 30], {"item": "tank_top", "variant": "tank_top_camo", "prob": 30}, ["kevlar", 10], {"group": "military_ballistic_vest", "prob": 30}, ["vest", 15], {"group": "gasmask_bag_full", "prob": 10}, {"item": "goggles_nv", "prob": 1, "charges": [0, 100]}, {"item": "goggles_ir", "prob": 1, "charges": [0, 100]}, ["backpack", 38], {"item": "UPS_OFF", "prob": 8, "charges": [0, 1000]}, ["tacvest", 10], {"group": "molle_backpacks_and_accessories", "prob": 8}, ["duffelbag", 15], ["dump_pouch", 20], ["legrig", 10], ["under_armor", 20], ["boots", 70], ["boots_combat", 70], ["gloves_tactical", 30], ["glasses_bal", 40], ["chestguard_hard", 20], ["armguard_hard", 20], ["legguard_hard", 15], {"group": "army_helmet_nvg", "prob": 1}, ["helmet_army", 40], ["helmet_liner", 10], ["beret", 50], ["beret_wool", 40], ["elbow_pads", 50], ["knee_pads", 50], ["solarpack", 5]]}, {"type": "item_group", "id": "mil_accessories", "items": [{"group": "gasmask_bag_full", "prob": 10}, ["duffelbag", 15], {"item": "goggles_nv", "prob": 1, "charges": [0, 100]}, {"item": "goggles_ir", "prob": 1, "charges": [0, 100]}, {"item": "military_nvg", "prob": 1, "charges": [0, 100]}, {"item": "advanced_gpnvg", "prob": 1, "charges": [0, 100]}, {"item": "enhanced_nvg", "prob": 1, "charges": [0, 100]}, ["optical_cloak", 1], {"item": "holo_cloak", "prob": 1, "charges": [0, 1000]}, {"item": "mess_kit", "prob": 9}, ["backpack", 38], ["briefcase", 10], {"item": "UPS_OFF", "prob": 8, "charges": [0, 1000]}, ["chestguard_hard", 20], ["armguard_hard", 20], ["legguard_hard", 15], ["power_armor_frame", 4], ["power_armor_generator", 4], ["elbow_pads", 40], ["knee_pads", 40], ["mask_bal", 5], ["e_tool", 10], ["waterproof_gunmod", 8], ["grapnel", 3], ["glasses_bal", 30], ["sheath", 10], ["bootsheath", 8], ["nylon_holster", 15], ["sholster", 10], ["shoulder_holster", 1], ["bandolier_shotgun", 8], ["torso_bandolier_grenade", 8], ["solarpack", 5], ["chem_hexamine", 3], {"item": "esbit_stove", "prob": 6, "charges": [0, 50]}, {"group": "full_survival_kit", "prob": 4}]}, {"type": "item_group", "id": "mil_armor_pants", "items": [["pants_army", 10], ["winter_pants_army", 40], ["pants", 75], ["pants_cargo", 70], ["legrig", 10], ["knee_pads", 20]]}, {"type": "item_group", "id": "construction_worker", "subtype": "distribution", "entries": [{"item": "boots", "prob": 70}, {"item": "boots_steel", "prob": 50}, {"item": "toecaps", "prob": 30}, {"item": "sneakers_steel", "prob": 35}, {"item": "boots_hiking", "prob": 10}, {"item": "denim_overalls", "prob": 15}, {"item": "technician_coveralls", "prob": 15}, {"item": "technician_coveralls_h", "prob": 5}, {"item": "gloves_rubber", "prob": 20}, {"item": "gloves_leather", "prob": 45}, {"item": "gloves_work", "prob": 45}, {"item": "mask_filter", "prob": 30, "charges": [0, -1]}, {"item": "glasses_safety", "prob": 40}, {"item": "hat_hard", "prob": 25}, {"group": "hat_hard_modified", "prob": 25}, {"item": "hat_hard_hooded", "prob": 15}, {"group": "hat_hard_hooded_modified", "prob": 10}, {"item": "wearable_light", "prob": 5, "charges": [0, 100]}, {"item": "ear_plugs", "prob": 50}, {"group": "ammo_any_batteries_full", "prob": 50}, {"item": "flashlight", "prob": 40, "charges": [0, 300]}, {"item": "boots_rubber", "prob": 20}, {"group": "tools_toolbox", "prob": 1}, {"item": "apron_leather", "prob": 10}, {"item": "apron_cut_resistant", "prob": 10}]}, {"type": "item_group", "id": "loincloth", "items": [["loincloth", 2], ["loincloth_wool", 1], ["loincloth_fur", 1], ["loincloth_leather", 1]]}, {"type": "item_group", "id": "fireman_torso", "items": [["bunker_coat", 80], ["vest", 30]]}, {"type": "item_group", "id": "fireman_pants", "items": [["bunker_pants", 80], ["nomex_suit", 40], ["pants_cargo", 10]]}, {"type": "item_group", "id": "fireman_boots", "items": [["boots_bunker", 70], ["boots_steel", 20], ["sneakers_steel", 15], ["boots_combat", 5], ["boots_rubber", 10], ["boots_hiking", 5], ["nomex_socks", 40]]}, {"type": "item_group", "id": "fireman_gloves", "items": [["fire_gauntlets", 60], ["gloves_medical", 5], ["gloves_tactical", 20], ["gloves_rubber", 10], ["nomex_gloves", 30]]}, {"type": "item_group", "id": "fireman_head", "items": [["firehelmet", 60], ["hat_knit", 5], ["nomex_hood", 40], {"item": "hat_hard", "variant": "red_hat_hard", "prob": 15}, {"item": "hat_hard_hooded", "variant": "red_hat_hard", "prob": 6}, ["hat_noise_cancelling", 5]]}, {"type": "item_group", "id": "fireman_mask", "items": [{"item": "mask_bunker", "prob": 70, "charges": [0, -1]}, ["mask_dust", 30], {"distribution": [{"item": "mask_gas", "charges": [0, -1], "prob": 50}, {"item": "mask_gas", "charges": [0, -1], "contents-group": "gasmask_inserts_in_use", "prob": 25}, {"group": "gasmask_bag_full", "prob": 50}], "prob": 20}, {"item": "mask_gas_half", "prob": 20, "charges": [0, -1]}, {"item": "mask_filter", "prob": 10, "charges": [0, -1]}, {"item": "goggles_nv", "prob": 2, "charges": [0, 100]}, {"item": "goggles_ir", "prob": 1, "charges": [0, 100]}]}, {"type": "item_group", "id": "hazmat_torso", "items": [["cleansuit", 40], ["technician_coveralls", 60]]}, {"type": "item_group", "id": "hazmat_boots", "items": [["boots_rubber", 1]]}, {"type": "item_group", "id": "hazmat_gloves", "items": [["gloves_medical", 60], ["gloves_rubber", 40]]}, {"type": "item_group", "id": "hazmat_full", "items": [["hazmat_suit", 100]]}, {"type": "item_group", "id": "hazmat_mask", "items": [["mask_dust", 80], {"item": "mask_filter", "prob": 20, "charges": [0, -1]}, {"distribution": [{"item": "mask_gas", "charges": [0, -1], "prob": 25}, {"item": "mask_gas", "charges": [0, -1], "contents-group": "gasmask_inserts_in_use", "prob": 25}, {"group": "gasmask_bag_full", "prob": 50}], "prob": 10}]}, {"type": "item_group", "id": "hazmat_eyes", "items": [["glasses_safety", 50]]}, {"type": "item_group", "id": "survivorzed_suits", "items": [["survivor_jumpsuit", 8], ["lsurvivor_jumpsuit", 10], ["hsurvivor_jumpsuit", 6], ["wsurvivor_jumpsuit", 6], ["wsurvivor_jumpsuit_nofur", 4], ["h20survivor_jumpsuit", 2], ["touring_suit", 16], ["armor_larmor", 28], ["armor_blarmor", 14], ["armor_farmor", 8], ["armor_plarmor", 2], ["armor_riot", 10], ["armor_scrapsuit", 12], ["armor_tiresuit", 12], ["armor_chitin", 2], ["armor_junk_lightplate", 1], ["cuirass_lightplate", 2], ["armor_junk_plate", 1], {"item": "chainmail_junk_hauberk", "prob": 1}]}, {"type": "item_group", "id": "survivorzed_tops", "items": [["trenchcoat_survivor", 5], ["sleeveless_trenchcoat_survivor", 2], ["duster_survivor", 5], ["sleeveless_duster_survivor", 2], ["vest", 40], ["kevlar", 16], ["jacket_army", 5], ["jacket_army_modern", 16], ["combat_shirt", 5], ["trenchcoat", 12], ["trenchcoat_leather", 10], ["trenchcoat_fur", 5], ["sleeveless_trenchcoat", 7], ["sleeveless_trenchcoat_leather", 5], ["sleeveless_trenchcoat_fur", 2], ["duster", 12], ["duster_leather", 10], ["duster_fur", 5], ["sleeveless_duster", 7], ["sleeveless_duster_leather", 5], ["sleeveless_duster_fur", 2], ["peacoat", 14], ["greatcoat", 7], ["vest_leather", 22], ["bunker_coat", 6], ["bookplate", 10], ["tireplate", 10], ["ballistic_vest_esapi", 2], {"group": "military_ballistic_vest", "prob": 4}, ["dragonskin", 1], ["corset", 1], ["corset_waist", 1], ["football_armor", 18], ["jacket_leather", 12], ["tacjacket", 30], ["jacket_varsity", 6], ["gosling_jacket", 1], ["leather_police_jacket", 12], ["jacket_jean", 8], ["jacket_flannel", 6], ["cuirass_scrap", 12], ["cuirass_tire", 12]]}, {"type": "item_group", "id": "survivorzed_bottoms", "items": [["pants_survivor", 10], ["pants_hiking", 12], ["pants_cargo", 40], ["shorts_cargo", 22], ["pants_army", 28], ["winter_pants_army", 10], ["bunker_pants", 14], ["pants_leather", 18], ["chaps_leather", 10], ["legguard_scrap", 12], ["legguard_tire", 12]]}, {"type": "item_group", "id": "survivorzed_gloves", "items": [["gloves_lsurvivor", 10], ["gloves_survivor", 8], ["gloves_hsurvivor", 4], ["gloves_wsurvivor", 4], ["gloves_fsurvivor", 2], ["gloves_h20survivor", 1], ["gloves_fingerless", 28], ["gloves_fingerless_mod", 20], ["gloves_tactical", 12], ["gauntlets_larmor", 14], ["gauntlets_chitin", 2], ["armguard_larmor", 7], ["vambrace_larmor", 6], ["armguard_chitin", 1], ["armguard_scrap", 12], ["armguard_tire", 12], ["gloves_fur", 4], ["gloves_leather", 22], ["gloves_work", 22], ["gloves_plate", 2], ["gloves_wraps", 1]]}, {"type": "item_group", "id": "survivorzed_boots", "items": [["boots_lsurvivor", 10], ["boots_survivor", 8], ["boots_hsurvivor", 4], ["boots_wsurvivor", 4], ["boots_fsurvivor", 2], ["boots_h20survivor", 1], ["boots", 20], ["boots_scrap", 12], ["boots_steel", 28], ["sneakers_steel", 20], ["boots_denim", 2], ["boots_hiking", 24], ["boots_combat", 12], ["boots_larmor", 14], ["boots_fur", 22], ["boots_plate", 2], ["boots_bunker", 8], ["footrags", 1]]}, {"type": "item_group", "id": "survivorzed_head", "items": [["hood_lsurvivor", 10], ["hood_survivor", 8], ["hood_wsurvivor", 4], ["hood_fsurvivor", 2], ["hood_h20survivor", 1], ["helmet_army", 26], ["tac_helmet", 22], ["helmet_riot", 18], ["tac_fullhelmet", 8], ["helmet_larmor", 14], ["pickelhaube", 1], ["firehelmet", 2], ["helmet_barbute", 1], ["helmet_plate", 1], ["helmet_scrap", 12], ["survivor_goggles", 10], ["hood_rain", 14]]}, {"type": "item_group", "id": "survivorzed_extra", "items": [["daypack", 4], ["mask_lsurvivor", 10], ["mask_survivor", 8], ["mask_hsurvivor", 6], ["survivor_vest", 8], ["survivor_runner_pack", 6], ["survivor_pack", 5], ["survivor_rucksack", 4], ["survivor_duffel_bag", 3], ["dive_bag", 10], ["dry_bag", 10], ["dry_duffelbag", 10], ["dry_bag_large", 5], ["runner_bag", 20], ["molle_pack", 12], ["backpack", 40], ["backpack_denim", 12], ["backpack_leather", 32], ["mbag", 26], ["purse", 14], ["slingpack", 12], ["rucksack", 12], ["duffelbag", 8], ["quiver_takedown_bow", 1], ["mask_h20survivor", 1], ["mask_bal", 14], ["mask_hockey", 26], {"group": "gasmask_bag_full", "prob": 24}, {"item": "mask_gas_half", "prob": 24, "charges": [0, 100]}, {"item": "mask_filter", "prob": 12, "charges": [0, 100]}, ["mask_bunker", 2], ["mask_fsurvivor", 2], ["sunglasses", 12], ["fitover_sunglasses", 8], ["glasses_bal", 18], ["glasses_safety", 24], ["goggles_ski", 14], {"item": "goggles_nv", "prob": 2, "charges": [0, 100]}, {"item": "goggles_ir", "prob": 1, "charges": [0, 100]}, ["tacvest", 22], ["legrig", 22], ["tool_belt", 16], ["fanny", 12], ["dump_pouch", 6], ["ragpouch", 22], ["leather_pouch", 16], {"group": "archery_loaded_quivers", "prob": 2}, ["quiver", 10], ["nylon_quiver", 5], ["quiver_birchbark", 4], ["quiver_large", 6], ["quiver_large_birchbark", 2], ["javelin_bag", 2], ["wristwatch", 24], ["diving_watch", 16], ["pocketwatch", 14], ["holster", 6], ["nylon_holster", 8], ["bandana", 18], ["scarf", 26], ["long_knit_scarf", 15], {"item": "mask_gas_xl", "prob": 4, "charges": [0, 100]}, ["hat_boonie", 16], ["beret", 18], ["beret_wool", 14], ["balclava", 12], ["mask_survivorxl", 2], ["combatsaw_off", 1], ["ashot", 4], ["pickaxe", 1], ["makeshift_machete", 4], ["machete_gimmick", 3], ["fungicide", 10], {"group": "insecticide_bag_plastic", "prob": 10}, {"prob": 1, "group": "antifungal_bottle_plastic_pill_prescription_0_10"}, {"prob": 5, "group": "antiparasitic_bottle_plastic_pill_prescription_0_10"}, {"prob": 1, "group": "diazepam_bottle_plastic_pill_prescription_0_10"}, ["throw_extinguisher", 2], {"item": "small_repairkit", "prob": 14, "charges": [0, 500]}, ["grapnel", 6], {"item": "misc_repairkit", "prob": 8}, {"distribution": [{"group": "full_survival_kit"}, {"group": "used_survival_kit"}], "prob": 3}, {"group": "tools_toolbox", "prob": 1}, ["survivor_belt_notools", 2], ["survivor_machete", 2], ["survivor_mess_kit", 6], ["acetylene_cooker", 12], ["survivor_shavingkit", 3], ["survivor_hairtrimmer", 1], ["survivor_scope", 1], ["survnote", 30]]}, {"type": "item_group", "id": "museum_armor", "items": [{"item": "armor_junk_plate", "prob": 60, "damage": [2, 4]}, {"item": "chainmail_junk_hauberk", "prob": 60, "damage": [2, 4]}, {"item": "gloves_plate", "prob": 60, "damage": [2, 4]}, {"item": "boots_plate", "prob": 60, "damage": [2, 4]}, {"item": "armor_junk_lightplate", "prob": 45, "damage": [2, 4]}, {"item": "cuirass_lightplate", "prob": 45, "damage": [2, 4]}, {"item": "armguard_lightplate", "prob": 30, "damage": [2, 4]}, {"item": "legguard_lightplate", "prob": 30, "damage": [2, 4]}, {"item": "helmet_barbute", "prob": 50, "damage": [2, 4]}, {"item": "helmet_conical", "prob": 30, "damage": [2, 4]}, {"item": "armor_lamellar", "prob": 20, "damage": [2, 4]}, {"item": "armor_lorica", "prob": 25, "damage": [2, 4]}, {"item": "armor_samurai", "prob": 50, "damage": [2, 4]}, {"item": "helmet_kabuto", "prob": 50, "damage": [2, 4]}, {"item": "helmet_nasal", "prob": 50, "damage": [2, 4]}, {"item": "helmet_galea", "prob": 40, "damage": [2, 4]}, {"item": "gambeson", "prob": 50, "damage": [2, 4]}, {"item": "gambeson_pants", "prob": 40, "damage": [2, 4]}, {"item": "legguard_metal", "prob": 10, "damage": [2, 4]}, {"item": "helmet_corinthian", "prob": 45, "damage": [2, 4]}, {"item": "armor_cuirass", "prob": 25, "damage": [2, 4]}, {"item": "legguard_bronze", "prob": 20, "damage": [2, 4]}]}, {"type": "item_group", "id": "museum_armor_torso", "items": [{"item": "armor_junk_plate", "prob": 60, "damage": [2, 4]}, {"item": "chainmail_junk_hauberk", "prob": 60, "damage": [2, 4]}, {"item": "armor_junk_lightplate", "prob": 45, "damage": [2, 4]}, {"item": "cuirass_lightplate", "prob": 45, "damage": [2, 4]}, {"item": "armor_lamellar", "prob": 20, "damage": [2, 4]}, {"item": "armor_lorica", "prob": 25, "damage": [2, 4]}, {"item": "armor_samurai", "prob": 50, "damage": [2, 4]}, {"item": "gambeson", "prob": 50, "damage": [2, 4]}, {"item": "armor_cuirass", "prob": 25, "damage": [2, 4]}, {"item": "chainmail_junk_suit", "prob": 10, "damage": [2, 4]}, {"item": "chainmail_junk_vest", "prob": 3, "damage": [2, 4]}]}, {"type": "item_group", "id": "museum_armor_legs", "items": [{"item": "legguard_lightplate", "prob": 30, "damage": [2, 4]}, {"item": "legguard_metal", "prob": 10, "damage": [2, 4]}, {"item": "legguard_bronze", "prob": 20, "damage": [2, 4]}, {"item": "chainmail_junk_legs", "prob": 10, "damage": [2, 4]}]}, {"type": "item_group", "id": "museum_armor_feet", "items": [{"item": "boots_plate", "prob": 60, "damage": [2, 4]}, {"item": "chainmail_junk_feet", "prob": 40, "damage": [2, 4]}]}, {"type": "item_group", "id": "museum_armor_head", "items": [{"item": "helmet_barbute", "prob": 50, "damage": [2, 4]}, {"item": "helmet_conical", "prob": 30, "damage": [2, 4]}, {"item": "helmet_kabuto", "prob": 50, "damage": [2, 4]}, {"item": "helmet_nasal", "prob": 50, "damage": [2, 4]}, {"item": "helmet_galea", "prob": 40, "damage": [2, 4]}, {"item": "chainmail_junk_hood", "prob": 30, "damage": [2, 4]}]}, {"type": "item_group", "id": "museum_armor_arms", "items": [{"item": "armguard_lightplate", "prob": 30, "damage": [2, 4]}, {"item": "gloves_plate", "prob": 60, "damage": [2, 4]}, {"item": "chainmail_junk_arms", "prob": 30, "damage": [2, 4]}, {"item": "chainmail_junk_hands", "prob": 30, "damage": [2, 4]}]}, {"type": "item_group", "id": "coat_rack", "subtype": "distribution", "entries": [{"item": "jacket_light", "prob": 50}, {"item": "jacket_windbreaker", "prob": 25}, {"item": "jacket_varsity", "prob": 20}, {"item": "jacket_jean", "prob": 35}, {"item": "blazer", "prob": 35}, {"item": "jacket_leather", "prob": 30}, {"item": "leather_police_jacket", "prob": 10}, {"item": "gosling_jacket", "prob": 5}, {"item": "coat_rain", "prob": 50}, {"item": "trenchcoat", "prob": 10}, {"item": "duster", "prob": 15}, {"item": "peacoat", "prob": 30}, {"item": "tophat", "prob": 10}, {"item": "hat_ball", "prob": 40}, {"item": "fedora", "prob": 15}, {"item": "hat_cotton", "prob": 30}, {"item": "hat_knit", "prob": 20}, {"group": "costume_jackets", "prob": 1}, {"group": "costume_jackets", "prob": 50, "event": "halloween"}, {"group": "christmas_jackets", "prob": 50, "event": "christmas"}]}, {"type": "item_group", "id": "laundry", "items": [["basket_laundry", 30], ["jeans", 90], ["jeans_ripped", 10], ["jeans_skinny", 20], ["pants_checkered", 5], ["shorts", 70], ["shorts_denim", 35], ["pants", 75], ["breeches", 10], ["suit", 60], ["waistcoat", 30], ["pants_leather", 60], ["pants_cargo", 70], ["shorts_cargo", 50], ["skirt", 75], ["skirt_long", 60], ["skirt_denim", 10], ["skirt_leather", 5], ["tshirt", 70], ["tshirt_cropped", 7], ["longshirt", 80], ["polo_shirt", 65], ["dress_shirt", 60], ["denim_shirt", 30], ["tank_top", 50], ["camisole", 30], ["bra", 30], ["undershirt", 30], ["boxer_shorts", 30], {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 2}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, ["briefs", 15], ["boxer_briefs", 20], ["swim_briefs", 10], ["speedo", 5], {"group": "bikini", "prob": 20}, ["panties", 30], ["boy_shorts", 25], {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 70, "event": "independence_day"}, ["sweatshirt", 75], ["sweater", 75], ["turtleneck", 40], ["turtleneck_shirt", 25], ["hoodie", 65], ["hoodie_cropped", 6], ["jacket_light", 50], ["jacket_windbreaker", 25], ["jacket_varsity", 20], ["gosling_jacket", 5], ["jacket_jean", 35], ["vest_jean", 20], ["blazer", 35], ["gloves_light", 35], ["mittens", 30], ["thermal_socks", 2], ["thermal_gloves", 2], ["gloves_liner", 25], ["scarf", 45], ["scarf_long", 1], ["hat_cotton", 45], ["hat_knit", 25], ["hat_newsboy", 20], ["under_armor", 20], ["under_armor_shorts", 20], ["tights", 20], ["leggings", 20], ["stockings", 20], ["garter_belt", 10], ["long_underpants", 40], ["long_undertop", 40], ["union_suit", 20], ["arm_warmers", 20], ["leg_warmers", 20], ["corset", 10], ["corset_waist", 10], ["leg_bag", 8], ["pants_army", 10], {"item": "tank_top", "variant": "tank_top_camo", "prob": 20}, ["tux", 1], ["gown", 1], ["kasaya", 1], ["antar<PERSON>a", 1], ["uttarasanga", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["jersey", 40], ["maid_dress", 1], ["maid_dress_short", 1], ["halter_top", 50], ["kilt", 2], ["nanoskirt", 10], ["robe", 50], ["trenchcoat_steampunk", 5], ["santa_jacket", 1], ["santa_jacket_short", 1], ["santa_shorts", 1], ["santa_pants", 1], ["santa_gloves", 1], ["gloves_white", 5], ["santa_hat", 1], ["santa_belt", 1], ["santa_beard", 1], ["santa_dress", 1], ["santa_dress_long", 1], ["santa_dress_short", 1], ["cloak_vampire", 1], ["cloak_black", 1], ["robe_wizard", 1], ["ghost_robe", 1], ["grim_reaper_robe", 1], ["jacket_ninja", 1], ["bee_dress", 1], ["cheerleader_dress", 1], ["mummy_dress", 1], ["ninja_dress", 1], ["ninja_dress_sleeveless", 1], ["sinister_dress", 1], ["sinister_dress_short", 1], ["sinister_gown", 1], ["witch_dress", 1], ["mummy_dress", 1], ["witch_dress_long", 1], ["witch_dress_short", 1], ["nun_habit", 1], ["nun_wimple", 1], ["gloves_black", 5], ["gloves_skeleton", 1], ["gloves_claws", 1], ["wizard_hat_costume", 1], ["pointed_hat", 1], ["hood_ninja", 1], ["balaclava_skull", 1], ["mask_skull", 1], ["zombie_mask", 1], ["cheerleader_skirt", 5], ["cheerleader_skirt_short", 1], {"item": "pants", "variant": "pants_flag", "prob": 5}, ["trousers_ninja", 1], {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 5}, ["cheerleader_top_short", 1], ["flag_jumpsuit", 2], ["jumpsuit_skeleton", 1], ["mummy_jumpsuit", 1], ["bodysuit_lycra", 3], ["flag_swimsuit", 2]]}, {"type": "item_group", "id": "common_gloves", "subtype": "distribution", "entries": [{"item": "gloves_light", "prob": 15}, {"item": "gloves_leather", "prob": 15}, {"item": "gloves_golf", "prob": 15}, {"item": "gloves_wool", "prob": 15}, {"item": "gloves_wool_fingerless", "prob": 5}, {"item": "gloves_winter", "prob": 10}, {"item": "gloves_fingerless", "prob": 15}, {"item": "gloves_liner", "prob": 15}, {"item": "gloves_white", "prob": 10}, {"item": "gloves_black", "prob": 10}, {"item": "mittens", "prob": 5}, {"item": "thermal_gloves", "prob": 2}, {"group": "costume_gloves", "prob": 1}, {"group": "costume_gloves", "prob": 70, "event": "halloween"}, {"group": "christmas_common_gloves", "prob": 70, "event": "christmas"}]}, {"id": "dollar_clothes", "type": "item_group", "items": [["bandana", 25], ["boxer_shorts", 25], {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 5}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, ["boxer_briefs", 25], ["boy_shorts", 25], {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 70, "event": "independence_day"}, ["bra", 25], ["socks", 45], ["socks_ankle", 30], ["stockings", 20], ["hat_cotton", 35], ["copper_bracelet", 10], ["sunglasses", 25], ["tieclip", 15], ["hairpin", 15], ["santa_beard", 5], ["santa_hat", 4], ["vampire_fangs", 5], ["mask_skull", 4], ["ankle_wallet_pouch", 5]]}, {"id": "unisex_coat_rack", "type": "item_group", "//": "clothing found on a coat rack", "subtype": "collection", "items": [{"group": "scarfs_unisex", "prob": 40}, {"group": "bags_unisex", "prob": 20}, {"group": "coats_unisex", "prob": 70}, {"group": "hats_unisex", "prob": 70}]}, {"id": "hats_unisex", "type": "item_group", "//": "unisex hats for domestic locations", "subtype": "distribution", "items": [{"distribution": [{"item": "tricorne", "prob": 5}, {"item": "10gal_hat", "prob": 30}, {"item": "tophat", "prob": 10}, {"item": "hat_sombrero", "prob": 10}], "prob": 5}, {"distribution": [{"item": "kippah", "prob": 33}, {"item": "turban", "prob": 33}, {"item": "kufi", "prob": 33}], "prob": 10}, {"distribution": [{"item": "helmet_motor", "prob": 20}, {"item": "helmet_bike", "prob": 40}, {"group": "helmet_bike_modified", "prob": 10}, {"item": "headgear", "prob": 10}, {"item": "helmet_ball", "prob": 15}, {"item": "swim_cap", "prob": 10}, {"item": "wetsuit_hood", "prob": 5}, {"item": "wetsuit_hood_thick", "prob": 1}], "prob": 30}, {"distribution": [{"item": "hat_hunting", "prob": 20}, {"item": "hat_faux_fur", "prob": 10}, {"item": "hat_fur", "prob": 5}, {"item": "hat_boonie", "prob": 15}, {"item": "cowl_wool", "prob": 15}, {"item": "hood_rain", "prob": 25}], "prob": 50}, {"distribution": [{"item": "bowhat", "prob": 10}, {"item": "fedora", "prob": 40}, {"item": "hat_newsboy", "prob": 5}, {"item": "cowboy_hat", "prob": 20}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 2}, {"item": "cowboy_hat", "variant": "flag_cowboy_hat", "prob": 70, "event": "independence_day"}, {"item": "straw_fedora", "prob": 5}, {"item": "hat_golf", "prob": 15}, {"item": "straw_hat", "prob": 15}, {"item": "beret_wool", "prob": 5}, {"item": "beret", "prob": 10}, {"item": "porkpie", "prob": 15}], "prob": 50}, {"item": "hat_knit", "prob": 30}, {"item": "hat_cotton", "prob": 40}, {"item": "hat_ball", "prob": 50}, {"group": "costume_hats_hoods", "prob": 1}, {"group": "costume_hats_hoods", "prob": 80, "event": "halloween"}]}, {"id": "masks_unisex", "type": "item_group", "//": "unisex masks for domestic locations", "subtype": "distribution", "items": [{"item": "bondage_mask", "prob": 5}, {"item": "mask_dust", "prob": 15}, {"item": "mask_guy_fawkes", "prob": 1}, {"item": "mask_hockey", "prob": 10}, {"item": "mouthpiece", "prob": 5}, {"item": "mask_filter", "prob": 5, "charges": [0, 100]}, {"group": "costume_masks", "prob": 5}, {"group": "costume_masks", "prob": 25, "event": "halloween"}, {"item": "mask_dust", "variant": "flag_mask_dust", "prob": 20, "event": "independence_day"}]}, {"id": "socks_unisex", "type": "item_group", "//": "unisex socks", "subtype": "distribution", "items": [{"item": "socks", "prob": 70}, {"item": "socks_ankle", "prob": 50}, {"item": "socks_wool", "prob": 30}, {"item": "thermal_socks", "prob": 10}]}, {"id": "scarfs_unisex", "type": "item_group", "//": "unisex scarfs and fabric coverings", "subtype": "distribution", "items": [{"item": "bandana", "prob": 90}, {"item": "balclava", "prob": 50}, {"item": "headscarf", "prob": 50}, {"item": "scarf_fur_long", "prob": 10}, {"item": "scarf_fur", "prob": 20}, {"item": "scarf", "prob": 80}, {"item": "scarf_long", "prob": 40}, {"item": "long_knit_scarf", "prob": 40}, {"item": "knit_scarf", "prob": 80}, {"item": "keffiyeh", "prob": 30}, {"item": "mask_ski", "prob": 30}, {"item": "thermal_mask", "prob": 5}, {"item": "blindfold", "prob": 5}]}, {"id": "shirts_unisex", "type": "item_group", "//": "unisex shirts", "subtype": "distribution", "items": [{"item": "longshirt", "prob": 70}, {"item": "arm_warmers", "prob": 20}, {"item": "armguard_soft", "prob": 10}, {"item": "dress_shirt", "prob": 50}, {"item": "denim_shirt", "prob": 15}, {"item": "jersey", "prob": 30}, {"item": "polo_shirt", "prob": 30}, {"item": "thermal_shirt", "prob": 20}, {"item": "sleeveless_tunic", "prob": 10}, {"item": "sweater", "prob": 40}, {"item": "turtleneck", "prob": 15}, {"item": "turtleneck_shirt", "prob": 15}, {"item": "sweatshirt", "prob": 50}, {"item": "tank_top", "prob": 30}, {"item": "tshirt", "prob": 85}, {"item": "rashguard", "prob": 20}, {"item": "tunic", "prob": 10}, {"item": "waistcoat", "prob": 10}, {"item": "wool_hoodie", "prob": 10}, {"item": "hoodie", "prob": 40}, {"item": "hoodie_cropped", "prob": 4}, {"item": "tshirt", "variant": "flag_shirt", "prob": 50, "event": "independence_day"}, {"group": "costume_shirts", "prob": 50, "event": "halloween"}]}, {"id": "shirts_womens", "type": "item_group", "//": "womens shirts", "subtype": "distribution", "items": [{"item": "halter_top", "prob": 30}, {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 1}, {"item": "cheerleader_top_short", "prob": 1}]}, {"id": "underwear_unisex", "type": "item_group", "//": "unisex underwear", "subtype": "distribution", "items": [{"item": "thermal_suit", "prob": 15}, {"item": "thermal_outfit", "prob": 5}, {"item": "long_undertop", "prob": 30}, {"item": "under_armor", "prob": 10}, {"item": "undershirt", "prob": 40}, {"item": "under_armor_shorts", "prob": 10}, {"item": "long_underpants", "prob": 30}]}, {"id": "underwear_womens", "type": "item_group", "//": "womens underwear.", "subtype": "distribution", "items": [{"item": "boy_shorts", "prob": 30}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 2}, {"item": "boy_shorts", "variant": "flag_boy_shorts", "prob": 100, "event": "independence_day"}, {"item": "bra", "prob": 60}, {"item": "panties", "prob": 70}, {"group": "bikini", "prob": 55}, {"item": "flag_swimsuit", "prob": 5}, {"item": "camisole", "prob": 30}, {"item": "corset", "prob": 10}, {"item": "corset_waist", "prob": 10}, {"item": "sports_bra", "prob": 50}, {"item": "stockings", "prob": 20}, {"item": "garter_belt", "prob": 10}, {"item": "tights", "prob": 20}, {"group": "costume_independence_undergarments_women", "prob": 70, "event": "independence_day"}, {"group": "costume_undergarments_women", "prob": 70, "event": "halloween"}]}, {"id": "underwear_mens", "type": "item_group", "//": "mens underwear.", "subtype": "distribution", "items": [{"item": "boxer_briefs", "prob": 30}, {"item": "boxer_shorts", "prob": 50}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 5}, {"item": "boxer_shorts", "variant": "flag_boxer_shorts", "prob": 100, "event": "independence_day"}, {"item": "briefs", "prob": 50}, {"item": "trunks", "prob": 10}, {"item": "swim_briefs", "prob": 5}, {"item": "speedo", "prob": 1}]}, {"id": "pants_unisex", "type": "item_group", "//": "pants and leg coverings for domestic locations.", "subtype": "distribution", "items": [{"distribution": [{"item": "hot_pants", "prob": 40}, {"item": "hot_pants_fur", "prob": 5}, {"item": "hot_pants_leather", "prob": 35}], "prob": 10}, {"distribution": [{"item": "chaps_leather", "prob": 30}, {"item": "motorbike_pants", "prob": 35}, {"item": "breeches", "prob": 25}, {"item": "pants_ski", "prob": 45}, {"item": "pants_checkered", "prob": 55}, {"item": "pants_fur", "prob": 5}, {"item": "pants_faux_fur", "prob": 15}], "prob": 20}, {"item": "jeans", "prob": 80}, {"item": "jeans_ripped", "prob": 7}, {"item": "jeans_skinny", "prob": 15}, {"item": "jeans_red", "prob": 20}, {"item": "leg_warmers", "prob": 20}, {"item": "leggings", "prob": 30}, {"item": "legguard_hard", "prob": 3}, {"item": "pants", "prob": 70}, {"item": "pants_cargo", "prob": 60}, {"item": "pants_leather", "prob": 20}, {"item": "shorts", "prob": 50}, {"item": "shorts_cargo", "prob": 60}, {"item": "shorts_denim", "prob": 40}, {"item": "b_shorts", "prob": 60}]}, {"id": "pants_womens", "type": "item_group", "//": "womens pants and leg coverings for domestic locations.", "subtype": "distribution", "items": [{"item": "nanoskirt", "prob": 10}, {"item": "skirt", "prob": 50}, {"item": "skirt_long", "prob": 40}, {"item": "skirt_denim", "prob": 20}, {"item": "skirt_leather", "prob": 10}]}, {"id": "pants_mens", "type": "item_group", "//": "mens pants and leg coverings for domestic locations.", "subtype": "distribution", "items": [{"item": "kilt", "prob": 50}, {"item": "kilt_leather", "prob": 25}]}, {"id": "gloves_unisex", "type": "item_group", "//": "gloves and mittens for domestic locations.", "subtype": "distribution", "items": [{"item": "gloves_leather", "prob": 60}, {"item": "gloves_light", "prob": 60}, {"item": "gloves_fingerless", "prob": 40}, {"item": "gloves_wool", "prob": 25}, {"item": "gloves_wool", "prob": 10}, {"item": "gloves_winter", "prob": 15}, {"item": "mittens", "prob": 30}, {"item": "gloves_work", "prob": 20}, {"item": "gloves_golf", "prob": 10}, {"item": "wetsuit_gloves", "prob": 5}, {"item": "wetsuit_gloves_thick", "prob": 1}, {"item": "gloves_fur", "prob": 5}, {"item": "thermal_gloves", "prob": 2}, {"item": "gloves_white", "prob": 5}, {"item": "gloves_black", "prob": 5}, {"group": "costume_gloves", "prob": 1}, {"group": "costume_gloves", "prob": 70, "event": "halloween"}, {"group": "christmas_common_gloves", "prob": 70, "event": "christmas"}]}, {"id": "gloves_womens", "type": "item_group", "//": "womens gloves and mittens for domestic locations.", "subtype": "distribution", "items": [{"item": "long_glove_white", "prob": 5}]}, {"id": "bags_unisex", "type": "item_group", "//": "worn bags of all sorts for domestic buildings", "subtype": "distribution", "items": [{"distribution": [{"item": "basket_laundry", "prob": 60}, {"item": "straw_basket", "prob": 35}, {"item": "suitcase_l", "prob": 15}, {"item": "suitcase_m", "prob": 25}], "prob": 20}, {"distribution": [{"item": "2l<PERSON><PERSON>", "prob": 50}, {"item": "camelbak", "prob": 25}, {"item": "canteen", "prob": 70}], "prob": 5}, {"distribution": [{"item": "fanny", "prob": 60}, {"item": "mbag", "prob": 35}, {"item": "runner_bag", "prob": 45}, {"item": "slingpack", "prob": 65}, {"item": "purse", "prob": 70}, {"item": "briefcase", "prob": 40}, {"item": "hk_briefcase", "prob": 1}, {"item": "leg_bag", "prob": 1}, {"item": "leg_small_bag", "prob": 1}, {"item": "armrig", "prob": 1}], "prob": 60}, {"distribution": [{"item": "case_violin", "prob": 5}, {"item": "bookstrap", "prob": 5}, {"item": "camera_bag", "prob": 20}, {"item": "quiver_takedown_bow", "prob": 2}, {"item": "nylon_quiver", "prob": 12}, {"item": "quiver", "prob": 8}, {"item": "quiver_large", "prob": 15}, {"item": "solarpack", "prob": 5}, {"item": "ammo_satchel", "prob": 10}, {"item": "ammo_pouch", "prob": 10}, {"item": "chestrig", "prob": 20}, {"item": "chestrig", "variant": "flag_chestrig", "prob": 2}, {"item": "dive_bag", "prob": 25}, {"item": "dry_bag", "prob": 25}, {"item": "dry_duffelbag", "prob": 25}, {"item": "dry_bag_large", "prob": 15}, {"item": "chestrig", "variant": "flag_chestrig", "prob": 70, "event": "independence_day"}], "prob": 30}, {"distribution": [{"item": "daypack", "prob": 25}, {"item": "backpack_leather", "prob": 40}, {"item": "backpack", "prob": 85}, {"item": "backpack_denim", "prob": 5}, {"item": "backpack_hiking", "prob": 25}, {"item": "duffelbag", "prob": 80}, {"item": "travelpack", "prob": 25}, {"item": "gobag", "prob": 1}], "prob": 80}]}, {"type": "item_group", "id": "formal_set_mens", "subtype": "collection", "entries": [{"distribution": [{"collection": [{"collection": [{"item": "undershirt", "prob": 30}, {"item": "dress_shirt"}, {"item": "waistcoat", "prob": 20}]}, {"item": "breeches"}]}, {"distribution": [{"item": "suit", "prob": 50}, {"item": "tux", "prob": 50}]}]}, {"item": "dress_shoes"}, {"collection": [{"item": "tophat", "prob": 50}, {"item": "collarpin", "prob": 50}]}]}, {"type": "item_group", "id": "dresses_womens", "subtype": "distribution", "entries": [{"item": "dress", "prob": 10}, {"item": "sundress", "prob": 10}, {"item": "gown", "prob": 4}, {"item": "dress_wedding", "prob": 2}, {"group": "costume_dresses", "prob": 1}, {"group": "costume_dresses", "prob": 15, "event": "halloween"}, {"group": "christmas_dresses", "prob": 15, "event": "christmas"}]}, {"id": "suits_unisex", "type": "item_group", "//": "suits, full body coverage", "subtype": "distribution", "items": [{"item": "bondage_suit", "prob": 10}, {"item": "bodysuit_lycra", "prob": 10}, {"item": "dinosuit", "prob": 5}, {"item": "sharksuit", "prob": 5}, {"item": "yukata", "prob": 20}, {"item": "leotard", "prob": 40}, {"item": "motorbike_armor", "prob": 15}, {"item": "suit", "prob": 70}, {"item": "touring_suit", "prob": 20}, {"item": "wolfsuit", "prob": 3}, {"item": "felinesuit", "prob": 3}, {"item": "dragonsuit", "prob": 1}, {"item": "wool_suit", "prob": 40}, {"item": "wool_suit_devil", "prob": 15}, {"item": "unitard", "prob": 60}, {"item": "zentai", "prob": 5}, {"item": "wetsuit", "prob": 2}, {"item": "wetsuit_thick", "prob": 1}, {"item": "union_suit", "prob": 40}, {"item": "wetsuit_spring", "prob": 2}, {"item": "wetsuit_spring_sleeveless", "prob": 2}, {"item": "flag_jumpsuit", "prob": 50, "event": "independence_day"}, {"item": "bodysuit_lycra", "variant": "flag_suit", "prob": 50, "event": "independence_day"}, {"group": "costume_suits", "prob": 70, "event": "halloween"}]}, {"id": "suits_mens", "type": "item_group", "//": "suits, full body coverage for men.", "subtype": "distribution", "items": [{"item": "tux", "prob": 30}, {"item": "thawb", "prob": 10}, {"item": "cassock", "prob": 10}]}, {"id": "suits_womens", "type": "item_group", "//": "suits/dresses, full body coverage for women.", "subtype": "distribution", "items": [{"item": "dress_wedding", "prob": 5}, {"item": "sundress", "prob": 60}, {"item": "gown", "prob": 20}, {"item": "dress", "prob": 60}, {"item": "veil_wedding", "prob": 5}, {"item": "santa_dress_long", "prob": 1}, {"item": "sinister_dress", "prob": 1}, {"item": "sinister_gown", "prob": 1}, {"item": "witch_dress_long", "prob": 1}, {"group": "costume_dresses", "prob": 50, "event": "halloween"}, {"group": "christmas_dresses", "prob": 50, "event": "christmas"}]}, {"id": "coats_unisex", "type": "item_group", "//": "unisex coats, jackets, cloaks, etc.", "subtype": "distribution", "items": [{"distribution": [{"item": "cloak", "prob": 80}, {"item": "cloak_wool", "prob": 40}, {"item": "cloak_leather", "prob": 20}, {"item": "cloak_fur", "prob": 10}, {"item": "jedi_cloak", "prob": 1}, {"group": "costume_cloacks", "prob": 1}, {"item": "american_flag", "prob": 100, "event": "independence_day"}, {"group": "costume_cloacks", "prob": 100, "event": "halloween"}], "prob": 20}, {"distribution": [{"item": "duster", "prob": 80}, {"item": "duster_leather", "prob": 60}, {"item": "duster_faux_fur", "prob": 30}, {"item": "duster_fur", "prob": 5}, {"item": "sleeveless_duster", "prob": 70}, {"item": "sleeveless_duster_leather", "prob": 50}, {"item": "sleeveless_duster_fur", "prob": 20}, {"item": "sleeveless_duster_faux_fur", "prob": 10}], "prob": 60}, {"distribution": [{"item": "trenchcoat", "prob": 80}, {"item": "trenchcoat_leather", "prob": 60}, {"item": "trenchcoat_faux_fur", "prob": 30}, {"item": "trenchcoat_fur", "prob": 5}, {"item": "sleeveless_trenchcoat", "prob": 40}, {"item": "sleeveless_trenchcoat_leather", "prob": 30}, {"item": "sleeveless_trenchcoat_fur", "prob": 10}, {"item": "sleeveless_trenchcoat_faux_fur", "prob": 20}], "prob": 40}, {"distribution": [{"item": "coat_rain", "prob": 80}, {"item": "coat_winter", "prob": 50}, {"item": "peacoat", "prob": 50}, {"item": "greatcoat", "prob": 35}, {"item": "coat_faux_fur", "prob": 20}, {"item": "coat_fur", "prob": 15}, {"group": "costume_coats", "prob": 1}, {"group": "costume_coats", "prob": 100, "event": "halloween"}], "prob": 80}, {"distribution": [{"item": "jacket_jean", "prob": 80}, {"item": "jacket_flannel", "prob": 50}, {"item": "jacket_leather", "prob": 50}, {"item": "tacjacket", "prob": 50}, {"item": "jacket_army", "prob": 30}, {"item": "leather_police_jacket", "prob": 10}, {"item": "jacket_leather_red", "prob": 25}, {"item": "jacket_light", "prob": 60}, {"item": "jacket_windbreaker", "prob": 70}, {"item": "jacket_varsity", "prob": 30}, {"item": "gosling_jacket", "prob": 5}, {"item": "poncho", "prob": 30}, {"item": "ski_jacket", "prob": 25}, {"group": "costume_jackets", "prob": 1}, {"group": "costume_jackets", "prob": 100, "event": "halloween"}, {"group": "christmas_jackets", "prob": 50, "event": "christmas"}], "prob": 60}, {"distribution": [{"item": "kimono", "prob": 20}, {"item": "robe", "prob": 10}], "prob": 5}, {"distribution": [{"item": "vest", "prob": 80}, {"item": "vest_leather", "prob": 20}, {"item": "vest_jean", "prob": 25}, {"item": "tank_top", "variant": "tank_top_camo", "prob": 40}, {"item": "blazer", "prob": 70}], "prob": 40}, {"distribution": [{"item": "soft_3a_vest", "prob": 50}, {"item": "stab_vest", "prob": 50}], "prob": 5}, {"group": "christmas_coats_unisex", "prob": 40, "event": "christmas"}]}, {"id": "shoes_unisex", "type": "item_group", "//": "unisex shoes (there are no men's only shoes)", "subtype": "distribution", "items": [{"distribution": [{"item": "boots", "prob": 80}, {"item": "boots_hiking", "prob": 50}, {"item": "boots_steel", "prob": 50}, {"item": "sneakers_steel", "prob": 35}, {"item": "boots_combat", "prob": 45}, {"item": "boots_fur", "prob": 10}, {"item": "boots_western", "prob": 40}, {"item": "boots_winter", "prob": 30}, {"item": "knee_high_boots", "prob": 15}, {"item": "motorbike_boots", "prob": 35}, {"item": "thigh_high_boots", "prob": 5}, {"item": "boots_denim", "prob": 5}], "prob": 60}, {"distribution": [{"item": "bastsandals", "prob": 20}, {"item": "straw_sandals", "prob": 20}, {"item": "flip_flops", "prob": 40}, {"item": "flip_flops_exp", "prob": 5}, {"item": "leathersandals", "prob": 35}, {"item": "espadrilles", "prob": 20}], "prob": 60}, {"distribution": [{"item": "cleats", "prob": 20}, {"item": "golf_shoes", "prob": 20}, {"item": "dance_shoes", "prob": 30}, {"item": "roller_blades", "prob": 35}, {"item": "rollerskates", "prob": 35}, {"item": "roller_shoes_off", "prob": 45}], "prob": 20}, {"distribution": [{"item": "dress_shoes", "prob": 50}, {"item": "lowtops", "prob": 30}, {"item": "mocassins", "prob": 10}, {"item": "sneakers", "prob": 35}, {"item": "shoes_denim", "prob": 5}], "prob": 70}, {"distribution": [{"item": "boots_rubber", "prob": 50}, {"item": "clogs", "prob": 20}, {"item": "geta", "prob": 10}, {"item": "slippers", "prob": 75}], "prob": 70}]}, {"id": "shoes_womens", "type": "item_group", "//": "womens only shoes", "subtype": "distribution", "items": [{"item": "heels", "prob": 100}]}, {"id": "accessory_ring", "type": "item_group", "//": "decorative finger rings worn by people.", "subtype": "distribution", "items": [{"item": "copper_ring", "prob": 80, "count": [1, 3]}, {"item": "silver_ring", "prob": 50, "count": [1, 3]}, {"item": "gold_ring", "prob": 40, "count": [1, 3]}, {"item": "diamond_ring", "prob": 10}, {"item": "platinum_ring", "prob": 10}]}, {"id": "accessory_earring", "type": "item_group", "//": "decorative earrings and miscellaneous ear accessories worn by people.", "subtype": "distribution", "items": [{"group": "earrings_silver", "prob": 70}, {"group": "earrings_gold", "prob": 25}, {"group": "earrings_platinum", "prob": 5}]}, {"id": "accessory_bracelet", "type": "item_group", "//": "decorative bracelets worn by people.", "subtype": "distribution", "items": [{"item": "bead_bracelet", "prob": 80, "count": [1, 2]}, {"item": "bracelet_friendship", "prob": 60, "count": [1, 2]}, {"item": "copper_bracelet", "prob": 50, "count": [1, 2]}, {"item": "silver_bracelet", "prob": 40, "count": [1, 2]}, {"item": "gold_bracelet", "prob": 10, "count": [1, 2]}, {"item": "platinum_bracelet", "prob": 10}]}, {"id": "accessory_teeth", "type": "item_group", "//": "decorative or functional teeth miscellany that may be present in people's mouths.", "subtype": "distribution", "items": [{"item": "gold_dental_grill", "prob": 50, "count": [1, 2]}, {"item": "platinum_dental_grill", "prob": 30, "count": [1, 2]}, {"item": "diamond_dental_grill", "prob": 10}]}, {"id": "accessory_necklace", "type": "item_group", "//": "decorative necklaces or neck accessories worn by people.", "subtype": "distribution", "items": [{"item": "bead_necklace", "prob": 80, "count": [1, 2]}, {"item": "copper_necklace", "prob": 60, "count": [1, 2]}, {"item": "silver_necklace", "prob": 40, "count": [1, 2]}, {"item": "gold_necklace", "prob": 20, "count": [1, 2]}, {"item": "platinum_necklace", "prob": 10, "count": [1, 2]}, {"item": "copper_locket", "prob": 60, "count": [1, 2]}, {"item": "silver_locket", "prob": 40, "count": [1, 2]}, {"item": "gold_locket", "prob": 20, "count": [1, 2]}, {"item": "platinum_locket", "prob": 10, "count": [1, 2]}, {"item": "collarpin", "prob": 10}, {"item": "bronze_medal", "prob": 6}, {"item": "silver_medal", "prob": 4}, {"item": "gold_medal", "prob": 2}, {"item": "small_relic", "prob": 10}, {"item": "holy_symbol", "prob": 10}, {"item": "holy_symbol_wood", "prob": 10}]}, {"id": "accessory_cat", "type": "item_group", "//": "accessories that some say were the cause of the Cataclysm.", "subtype": "distribution", "items": [{"item": "faux_fur_cat_ears", "prob": 80}, {"item": "leather_cat_ears", "prob": 50}, {"item": "fur_cat_ears", "prob": 10}, {"item": "faux_fur_cat_tail", "prob": 80}, {"item": "leather_cat_tail", "prob": 50}, {"item": "fur_cat_tail", "prob": 10}, {"item": "faux_fur_collar", "prob": 80}, {"item": "leather_collar", "prob": 50}, {"item": "fur_collar", "prob": 10}]}, {"id": "accessory_weaponcarry", "type": "item_group", "//": "clothing designed to hold a small weapon worn by people.", "subtype": "distribution", "items": [{"item": "bootstrap", "prob": 20}, {"item": "bootsheath", "prob": 50}, {"item": "leg_sheath6", "prob": 1}, {"item": "gartersheath1", "prob": 40}, {"item": "gartersheath2", "prob": 10}, {"item": "sheath", "prob": 80}, {"item": "back_holster", "prob": 10}, {"item": "holster", "prob": 10}, {"item": "nylon_holster", "prob": 20}, {"item": "sholster", "prob": 10}, {"item": "shoulder_holster", "prob": 1}, {"item": "bholster", "prob": 10}, {"item": "bb<PERSON>ster", "prob": 8}]}, {"id": "accessory_sportsgear", "type": "item_group", "//": "assorted sportsgear worn by people.", "subtype": "distribution", "items": [{"item": "elbow_pads", "prob": 50}, {"item": "knee_pads", "prob": 50}, {"item": "iggaak", "prob": 5}, {"item": "goggles_swim", "prob": 10}, {"item": "glasses_bal", "prob": 10}, {"item": "eclipse_glasses", "prob": 5}, {"item": "goggles_ski", "prob": 10}]}, {"id": "accesories_personal_unisex", "type": "item_group", "//": "unisex personal accessories", "subtype": "collection", "items": [{"group": "accesories_personal_unisex_child", "prob": 185}, {"group": "accessory_cat", "prob": 1}, {"group": "accessory_weaponcarry", "prob": 5}, {"group": "accessory_sportsgear", "prob": 5}, {"item": "american_flag", "prob": 1}, {"item": "pride_flag", "prob": 1}, {"item": "national_flag", "prob": 1}, {"item": "state_flag", "prob": 1}, {"item": "tool_belt", "prob": 2}, {"group": "flask_liquor", "prob": 3}]}, {"id": "accesories_personal_unisex_child", "type": "item_group", "//": "unisex personal accessories that a kid could have on them", "subtype": "collection", "items": [{"group": "clothing_glasses", "prob": 50}, {"group": "clothing_watch", "prob": 15}, {"group": "accessory_ring", "prob": 15}, {"group": "accessory_earring", "prob": 15}, {"group": "accessory_bracelet", "prob": 15}, {"group": "accessory_teeth", "prob": 2}, {"group": "accessory_necklace", "prob": 15}, {"group": "accessory_sportsgear", "prob": 5}, {"item": "folding_poncho", "prob": 5}, {"item": "leather_belt", "prob": 30}, {"item": "wearable_light", "prob": 10, "charges": [0, 100]}, {"item": "binoculars", "prob": 2}, {"item": "whistle", "prob": 3}, {"item": "harmonica_holder", "prob": 1}, {"group": "costume_accessories", "prob": 1}, {"group": "costume_jewelry", "prob": 1}, {"group": "costume_accessories", "prob": 30, "event": "halloween"}, {"group": "costume_jewelry", "prob": 30, "event": "halloween"}]}, {"id": "accesories_personal_womens", "type": "item_group", "//": "womens personal accessories", "subtype": "distribution", "items": [{"group": "accesories_personal_womens_child", "prob": 380}, {"item": "ring_engagement", "prob": 5}, {"item": "ring_purity", "prob": 10}, {"item": "gold_lip_ring_loop", "prob": 2}, {"item": "silver_lip_ring_stud", "prob": 3}, {"item": "silver_lip_ring_loop", "prob": 3}, {"item": "gold_lip_ring_stud", "prob": 2}, {"item": "gold_nose_ring_loop", "prob": 2}, {"item": "gold_nose_ring_stud", "prob": 2}, {"item": "silver_nose_ring_loop", "prob": 3}, {"item": "silver_nose_ring_stud", "prob": 3}]}, {"id": "accesories_personal_womens_child", "type": "item_group", "//": "womens personal accessories that do not contain the adult-specific ones", "subtype": "distribution", "items": [{"item": "hairpin", "prob": 90}, {"item": "fc_hairpin", "prob": 70}, {"item": "copper_hairpin", "prob": 60}, {"item": "silver_hairpin", "prob": 50}, {"item": "gold_hairpin", "prob": 30}, {"item": "platinum_hairpin", "prob": 10}, {"item": "barrette", "prob": 60}, {"item": "pearl_collar", "prob": 5}, {"item": "jade_brooch", "prob": 5}, {"item": "gold_anklet", "prob": 30}, {"item": "silver_anklet", "prob": 50}, {"item": "gold_toe_ring", "prob": 30}, {"item": "silver_toe_ring", "prob": 50}, {"item": "woven_anklet", "prob": 30}, {"item": "bat_anklet", "prob": 30}, {"item": "flower_anklet", "prob": 30}]}, {"id": "accesories_personal_mens", "type": "item_group", "//": "men's personal accessories", "subtype": "distribution", "items": [{"item": "tie_bow", "prob": 30}, {"item": "tie_clipon", "prob": 10}, {"item": "tie_necktie", "prob": 80}, {"item": "tieclip", "prob": 20}, {"item": "cufflinks", "prob": 20}, {"item": "cufflinks_intricate", "prob": 5}, {"item": "ring_signet", "prob": 30}, {"item": "tie_skinny", "prob": 20}]}, {"id": "tefillin_sets", "type": "item_group", "//": "tefillin sets", "subtype": "distribution", "items": [{"group": "tefillin_right_hand", "prob": 1}, {"group": "tefillin_left_hand", "prob": 1}]}, {"id": "tefillin_right_hand", "type": "item_group", "//": "tefillin sets", "subtype": "collection", "items": [{"item": "tefillin_hand_r", "prob": 100}, {"item": "tefillin_head", "prob": 100}]}, {"id": "tefillin_left_hand", "type": "item_group", "//": "tefillin sets", "subtype": "collection", "items": [{"item": "tefillin_hand_l", "prob": 100}, {"item": "tefillin_head", "prob": 100}]}, {"type": "item_group", "id": "bottle_otc_painkiller_20", "subtype": "distribution", "//": "Includes aspirin and other similar low-grade painkillers.", "container-item": "bottle_plastic_pill_painkiller", "entries": [{"item": "aspirin", "container-item": "null", "count": 20}, {"item": "ibuprofen", "container-item": "null", "count": 20}, {"item": "acetaminophen", "container-item": "null", "count": 20}]}, {"type": "item_group", "id": "antifungal_bottle_plastic_pill_prescription_0_10", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_pill_prescription", "entries": [{"item": "antifungal", "container-item": "null", "count": [0, 10]}]}, {"type": "item_group", "id": "antiparasitic_bottle_plastic_pill_prescription_0_10", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_pill_prescription", "entries": [{"item": "antiparasitic", "container-item": "null", "count": [0, 10]}]}, {"type": "item_group", "id": "diazepam_bottle_plastic_pill_prescription_0_10", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_pill_prescription", "entries": [{"item": "diazepam", "container-item": "null", "count": [0, 10]}]}]