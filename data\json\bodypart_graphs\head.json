[{"type": "body_graph", "id": "head", "parent_bodypart": "head", "fill_sym": "#", "fill_color": "white", "rows": ["             7777777777777              ", "          7777777777777777777           ", "         777777777777777777777          ", "        66666666666666666666666         ", "        66666666666666666666666         ", "        66666666666666666666666         ", "      9 99666666666666666666600 0       ", "      999991111114444422222200000       ", "      999911111114444422222220000       ", "      999955555555444555555550000       ", "       9955555555544455555555500        ", "        95555555544444555555550         ", "         555555533333335555555          ", "          5555533333333355555           ", "           aaaaaaa333aaaaaaa            ", "            aaaaaaaaaaaaaaa             ", "            b aaaaaaaaaaa b             ", "         bb88888 aaaaa 88888bb          ", "       bbb88888888   88888888bbb        ", "           b888888888888888b            "], "parts": {"1": {"sub_body_parts": ["eyes_right"], "select_color": "red"}, "2": {"sub_body_parts": ["eyes_left"], "select_color": "red"}, "3": {"sub_body_parts": ["mouth_lips"], "select_color": "red"}, "4": {"sub_body_parts": ["mouth_nose"], "select_color": "red"}, "5": {"sub_body_parts": ["mouth_cheeks"], "select_color": "red"}, "6": {"sub_body_parts": ["head_forehead"], "select_color": "red"}, "7": {"sub_body_parts": ["head_crown"], "select_color": "red"}, "8": {"sub_body_parts": ["head_throat"], "select_color": "red"}, "9": {"sub_body_parts": ["head_ear_r"], "select_color": "red"}, "0": {"sub_body_parts": ["head_ear_l"], "select_color": "red"}, "a": {"sub_body_parts": ["mouth_chin"], "select_color": "red"}, "b": {"sub_body_parts": ["head_nape"], "select_color": "red"}}}]