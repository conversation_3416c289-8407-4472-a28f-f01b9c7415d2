version: v1

labels:
  - label: "<Enhancement / Feature>"
    matcher:
      body: "(\\s|^)#### Summary\\s*Features +\".+\"\\s*(\n|$)"

  - label: "Info / User Interface"
    matcher:
      body: "(\\s|^)#### Summary\\s*Interface +\".+\"\\s*(\n|$)"

  - label: "Mods"
    matcher:
      body: "(\\s|^)#### Summary\\s*Mods +\".+\"\\s*(\n|$)"

  - label: "Game: Balance"
    matcher:
      body: "(\\s|^)#### Summary\\s*Balance +\".+\"\\s*(\n|$)"

  - label: "<Bugfix>"
    matcher:
      body: "(\\s|^)#### Summary\\s*Bugfixes +\".+\"\\s*(\n|$)"

  - label: "<Bugfix>"
    matcher:
      body: ".*((C|c)lose(s|d)?|(F|f)ix(es|ed)?|(R|r)esolve(s|d)?):?.*#([1-9][0-9]{3,8}).*"

  - label: "Code: Performance"
    matcher:
      body: "(\\s|^)#### Summary\\s*Performance +\".+\"\\s*(\n|$)"

  - label: "Code: Infrastructure / Style / Static Analysis"
    matcher:
      body: "(\\s|^)#### Summary\\s*Infrastructure +\".+\"\\s*(\n|$)"

  - label: "Code: Build"
    matcher:
      body: "(\\s|^)#### Summary\\s*Build +\".+\"\\s*(\n|$)"

  - label: "Translation"
    matcher:
      body: "(\\s|^)#### Summary\\s*I18N +\".+\"\\s*(\n|$)"
