[{"type": "vehicle_part", "id": "ap_veh_tools_kitchen", "name": "kitchen station", "copy-from": "veh_tools_kitchen", "extend": {"flags": ["APPLIANCE"]}}, {"type": "vehicle_part", "id": "ap_veh_tools_workshop", "name": "workshop station", "copy-from": "veh_tools_workshop", "extend": {"flags": ["APPLIANCE"]}}, {"id": "ap_oven", "name": {"str": "oven"}, "categories": ["utility"], "description": "A standard electric convection-based oven, commonly used for heating and cooking food.  It's plugged in and ready to go.", "color": "dark_gray", "broken_color": "dark_gray_white", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["CARGO", "OBSTACLE", "COVERED", "FLAT_SURF", "APPLIANCE"], "pseudo_tools": [{"id": "oven"}, {"id": "hotplate", "hotkey": "h"}], "item": "oven", "looks_like": "f_oven", "breaks_into": [{"item": "sheet_metal", "count": [1, 4]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "scrap", "count": [0, 6]}, {"item": "element", "count": [1, 3]}, {"item": "cable", "charges": [1, 3]}], "size": "50 L", "type": "vehicle_part", "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"id": "ap_water_purifier", "name": {"str": "stationary water purifier"}, "categories": ["utility"], "description": "This device effectively sterilizes water.  Activate it to clean all water in a connected tank, at the cost of some electricity.", "color": "white", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 60, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "water_purifier", "hotkey": "p"}], "item": "stationary_water_purifier", "looks_like": "f_water_purifier", "breaks_into": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 2]}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}, {"item": "water_faucet", "count": [0, 1]}], "type": "vehicle_part", "variants": [{"symbols": "W", "symbols_broken": "#"}]}, {"id": "ap_arcade_machine", "name": {"str": "arcade machine"}, "categories": ["utility"], "description": "A bulky upright arcade cabinet, brightly painted and slightly worn with age.  Ready to be played.", "color": "red", "broken_color": "red", "damage_modifier": 10, "damage_reduction": {"all": 10}, "durability": 80, "flags": ["OBSTACLE", "COVERED", "HALF_CIRCLE_LIGHT", "APPLIANCE", "ENABLED_DRAINS_EPOWER", "ARCADE", "CTRL_ELECTRONIC"], "epower": "-150 W", "bonus": 10, "item": "arcade_machine", "looks_like": "f_arcade_machine", "breaks_into": [{"item": "splinter", "count": [0, 6]}, {"item": "television", "prob": 50}, {"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 10]}, {"item": "cable", "charges": [4, 10]}, {"item": "circuit", "count": [0, 4]}, {"item": "power_supply", "prob": 50}, {"item": "RAM", "count": [0, 2]}], "type": "vehicle_part", "variants": [{"symbols": "6", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_arc_furnace", "name": {"str": "arc furnace"}, "looks_like": "f_arc_furnace", "color": "blue", "categories": ["utility"], "description": "An arc furnace designed to burn a powdery mix of coke and limestone to create calcium carbide.  Plugged in and ready to go.", "broken_color": "yellow_red", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "fake_arc_furnace"}], "item": "arc_furnace", "breaks_into": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "pipe", "count": [0, 4]}], "variants": [{"symbols": "U", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_forge", "copy-from": "veh_forge", "name": {"str": "electric forge"}, "description": "An electric forge, plugged in and ready to be used for metalworking.  If you attempt to craft an item that needs a forge, you will be given the option of selecting it as a tool.", "item": "forge", "flags": ["CARGO", "OBSTACLE", "APPLIANCE"]}, {"type": "vehicle_part", "id": "ap_stereo", "flags": ["STEREO", "APPLIANCE", "ENABLED_DRAINS_EPOWER"], "copy-from": "stereo"}, {"type": "vehicle_part", "id": "ap_kiln", "description": "An electric kiln for baking brick or clay.  When plugged in and powered you could use this to harden bricks or clay.  If you attempt to craft an item that needs a kiln, you will be given the option of selecting it as a tool.", "flags": ["CARGO", "OBSTACLE", "APPLIANCE"], "item": "kiln", "copy-from": "veh_kiln", "pseudo_tools": [{"id": "kiln"}]}, {"type": "vehicle_part", "id": "ap_drill_press", "name": {"str": "drill press"}, "looks_like": "f_machinery_light", "color": "yellow_red", "categories": ["utility"], "description": "A powerful drill mounted on a slide that lets it drop precisely down.  Useful in all kinds of projects, from industrial fabrication to home woodworking.  Plugged in and ready to go.", "broken_color": "yellow_red", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "drill_press_tool"}], "item": "drill_press", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_tablesaw", "name": {"str": "table saw"}, "looks_like": "f_machinery_light", "color": "yellow_red", "categories": ["utility"], "description": "A rotating sawblade set into a large, flat table.  Used for making straight, measured cuts, it's a key tool in any carpenter's arsenal.  Plugged in and ready to go.", "broken_color": "yellow_red", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "tablesaw_tool"}], "item": "tablesaw", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_mitresaw", "name": {"str": "mitre saw"}, "looks_like": "f_machinery_light", "color": "yellow_cyan", "categories": ["utility"], "description": "A circular sawblade on an arm that can slide and rotate in several directions, this is a staple tool for nearly any carpentry.  Plugged in and ready to go.", "broken_color": "yellow_cyan", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "mitresaw_tool"}], "item": "mitresaw", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_bandsaw", "name": {"str": "band saw"}, "looks_like": "f_machinery_light", "color": "yellow_cyan", "categories": ["utility"], "description": "A ribbonlike sawblade runs in a single direction in this tool, allowing precise cuts at almost any angle.  Plugged in and ready to go.", "broken_color": "yellow_cyan", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "bandsaw_tool"}], "item": "bandsaw", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_router", "name": {"str": "router table"}, "looks_like": "f_machinery_light", "color": "yellow_green", "categories": ["utility"], "description": "This table has an inset router, a rotating motor with an exchangeable blade head for cutting specific profiles and grooves.  Plugged in and ready to go.", "broken_color": "yellow_green", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "router_tool"}], "item": "router", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_planer", "name": {"str": "planer"}, "looks_like": "f_machinery_light", "color": "yellow_white", "categories": ["utility"], "description": "A hefty tool that will take in a board and cut it smooth and flat to a specific width, particularly great if working with raw lumber stock, but also good just for shaving wood down to size.  Plugged in and ready to go.", "broken_color": "yellow_white", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "planer_tool"}], "item": "planer", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_jointer", "name": {"str": "jointer"}, "looks_like": "f_machinery_light", "color": "yellow_magenta", "categories": ["utility"], "description": "A table-shaped tool with a rotating blade that will cut down, smooth out, and square off a board to make it nice and smooth.  Plugged in and ready to go.", "broken_color": "yellow_magenta", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "jointer_tool"}], "item": "jointer", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_catalytic_cracking_reactor", "name": {"str": "catalytic cracking reactor"}, "looks_like": "f_catalytic_cracking_reactor", "variants": [{"symbols": "Y", "symbols_broken": "x"}], "color": "light_cyan", "categories": ["utility"], "description": "A makeshift extraction system, or as makeshift as you can consider something that was purpose-built in a laboratory by a group of experts.", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "catalytic_cracking_reactor_tool"}], "item": "catalytic_cracking_reactor", "breaks_into": [{"item": "element", "count": [4, 8]}, {"item": "55gal_drum", "count": [3, 5]}, {"item": "pipe", "count": [2, 5]}, {"item": "thermometer", "count": [1, 4]}, {"item": "well_pump", "count": [0, 1]}, {"item": "laptop", "count": [0, 1]}, {"item": "pump_complex", "count": [0, 3]}, {"item": "cable", "count": [3, 8]}]}, {"type": "vehicle_part", "id": "ap_hydraulic_press", "name": {"str": "hydraulic press"}, "looks_like": "f_hydraulic_press", "color": "black_red", "categories": ["utility"], "description": "If you really want to squash something a lot, this is exactly the right industrial tool for you.  Plugged in and ready to go.", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "hydraulic_press_tool"}], "item": "hydraulic_press", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "steel_lump", "count": [1, 2]}, {"item": "steel_plate", "count": [1, 2]}], "variants": [{"symbols": "7", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_air_compressor", "name": {"str": "air compressor"}, "looks_like": "f_standing_tank", "color": "black_yellow", "categories": ["utility"], "description": "This durable tank is topped with a motor that will cram as much air into the tank as possible.  Plugged in and ready to go.", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "air_compressor_tool"}], "item": "air_compressor", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 10]}, {"item": "steel_plate", "count": [2, 4]}], "variants": [{"symbols": "7", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_heater_large", "name": {"str": "large heater"}, "item": "large_space_heater", "flags": ["CTRL_ELECTRONIC", "ENABLED_DRAINS_EPOWER", "SPACE_HEATER", "APPLIANCE"], "copy-from": "mountable_heater", "requirements": {"removal": {"time": "6 m"}}}, {"type": "vehicle_part", "id": "ap_heater_small", "name": {"str": "small heater"}, "item": "small_space_heater", "flags": ["CTRL_ELECTRONIC", "ENABLED_DRAINS_EPOWER", "SPACE_HEATER", "APPLIANCE"], "copy-from": "mountable_heater_small", "requirements": {"removal": {"time": "6 m"}}}, {"id": "ap_electrolyzer_makeshift", "type": "vehicle_part", "name": {"str": "makeshift water electrolyzer"}, "item": "electrolyzer_makeshift", "description": "A makeshift appliance which decomposes water into oxygen and hydrogen via electrolysis of sodium hydroxide solution, with a power cord to connect it to an electric grid.", "categories": ["utility"], "color": "green", "broken_color": "green", "damage_modifier": 10, "damage_reduction": {"all": 20}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "electrolyzer_makeshift"}], "breaks_into": [{"item": "steel_chunk", "count": [2, 6]}, {"item": "scrap", "count": [8, 13]}, {"item": "glass_shard", "count": [16, 24]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "pipe", "count": [0, 1]}, {"item": "cable", "charges": [150, 300]}], "variants": [{"symbols": "&", "symbols_broken": "x"}]}, {"id": "ap_ammonia_machine_reactor", "type": "vehicle_part", "name": {"str": "makeshift ammonia machine reactor"}, "item": "ammonia_machine_reactor", "description": "A huge appliance built from salvaged industrial equipment, which produces ammonia via the Haber-Bosch process from hydrogen and nitrogen.  It has a power cord to connect it to an electric grid.", "categories": ["utility"], "color": "green", "broken_color": "green", "damage_modifier": 10, "damage_reduction": {"all": 40}, "durability": 400, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "ammonia_machine_reactor"}], "breaks_into": [{"item": "steel_lump", "count": [15, 25]}, {"item": "steel_chunk", "count": [15, 25]}, {"item": "scrap", "count": [15, 25]}, {"item": "pyrometer", "prob": 25}, {"item": "hd_valve", "count": [0, 2]}, {"item": "pressure_gauge", "count": [0, 1]}, {"item": "steel_grille", "count": [6, 10]}, {"item": "element", "count": [6, 10]}, {"item": "cable", "charges": [15, 30]}], "variants": [{"symbols": "&", "symbols_broken": "x"}]}, {"id": "ap_ammonia_machine_pipework", "type": "vehicle_part", "name": {"str": "makeshift ammonia machine pipework"}, "item": "ammonia_machine_pipework", "description": "A rather complicated system of thick-walled pipes, powerful compressors, and wiring, meant to interface with a reactor producing ammonia from nitrogen and hydrogen via the Haber-Bosch process, with a power cord to connect it to an electric grid.", "categories": ["utility"], "color": "green", "broken_color": "green", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 12, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "ammonia_machine_pipework"}], "breaks_into": [{"item": "steel_lump", "count": [4, 8]}, {"item": "steel_chunk", "count": [4, 8]}, {"item": "scrap", "count": [4, 8]}, {"item": "hd_pipe", "count": [6, 10]}, {"item": "pipe", "count": [1, 2]}, {"item": "pipe_fittings", "count": [0, 1]}, {"item": "hd_pipe_fittings", "count": [2, 4]}, {"item": "hd_valve", "prob": 25}, {"item": "pressure_gauge", "prob": 25}, {"item": "power_supply", "prob": 25}, {"item": "hd_compressor", "count": [0, 2]}, {"item": "sheet_metal_small", "count": [1, 3]}, {"item": "pressure_gauge", "count": [0, 1]}, {"item": "cable", "charges": [100, 200]}], "variants": [{"symbols": "&", "symbols_broken": "x"}]}, {"id": "ap_nitrogen_generator", "type": "vehicle_part", "name": {"str": "makeshift nitrogen generator"}, "item": "nitrogen_generator", "description": "A large makeshift appliance which captures nitrogen from the air for use in chemistry projects, with a power cord to connect it to an electric grid.", "categories": ["utility"], "color": "green", "broken_color": "green", "damage_modifier": 10, "damage_reduction": {"all": 20}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "nitrogen_generator"}], "breaks_into": [{"item": "steel_lump", "count": [8, 13]}, {"item": "steel_chunk", "count": [8, 13]}, {"item": "scrap", "count": [8, 13]}, {"item": "sheet_cotton", "count": [5, 10]}, {"item": "pipe", "count": [0, 1]}, {"item": "hd_pipe", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [1, 2]}, {"item": "hd_compressor", "prob": 25}, {"item": "nitrogen_membrane_filter", "prob": 25}, {"item": "cable", "charges": [4, 8]}, {"item": "salt", "count": [2000, 3000], "container-item": "null"}], "variants": [{"symbols": "&", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "power_cord", "name": {"str": "power cord"}, "categories": ["other"], "color": "yellow", "broken_color": "dark_gray", "damage_modifier": 10, "//": "Epower for POWER_TRANSFER stuff is how much percentage-wise loss there is in transmission", "durability": 120, "description": "A power cord sticking out of an appliance.  You need to plug it into a powered grid for the appliance to work properly.", "item": "power_cord", "flags": ["NO_INSTALL_HIDDEN", "NO_UNINSTALL", "UNMOUNT_ON_DAMAGE", "POWER_TRANSFER"], "variants": [{"symbols": "{", "symbols_broken": "s"}]}, {"id": "ap_wall_light", "type": "vehicle_part", "name": {"str": "wall light"}, "item": "wall_light", "categories": ["lighting"], "color": "white", "broken_color": "blue", "durability": 20, "description": "A wall-mounted lamp that provides illumination when turned on.", "epower": "-10 W", "bonus": 200, "damage_modifier": 10, "looks_like": "ap_standing_directed_floodlight", "breaks_into": [{"item": "cable", "charges": [1, 4]}, {"item": "steel_chunk", "count": [0, 2]}, {"item": "scrap", "count": [1, 2]}], "flags": ["WALL_MOUNTED", "HALF_CIRCLE_LIGHT", "APPLIANCE", "ENABLED_DRAINS_EPOWER", "CTRL_ELECTRONIC", "OBSTACLE"], "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "*", "symbols_broken": "-"}]}, {"id": "ap_standing_lamp", "type": "vehicle_part", "name": {"str": "standing lamp"}, "item": "standing_lamp", "categories": ["lighting"], "color": "white", "broken_color": "blue", "durability": 20, "description": "A tall floor lamp that provides illumination when turned on.", "epower": "-10 W", "bonus": 200, "damage_modifier": 10, "looks_like": "f_floor_lamp", "breaks_into": [{"item": "cable", "charges": [1, 4]}, {"item": "steel_chunk", "count": [0, 2]}, {"item": "scrap", "count": [1, 2]}], "flags": ["CIRCLE_LIGHT", "APPLIANCE", "ENABLED_DRAINS_EPOWER", "CTRL_ELECTRONIC"], "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "*", "symbols_broken": "-"}]}, {"id": "ap_standing_floodlight", "type": "vehicle_part", "name": {"str": "standing floodlight"}, "item": "standing_floodlight", "description": "A large, heavy light that illuminates a wide area around it when switched on.", "flags": ["CIRCLE_LIGHT", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "copy-from": "floodlight", "requirements": {"removal": {"time": "5 s"}}}, {"id": "ap_standing_directed_floodlight", "type": "vehicle_part", "name": {"str": "standing directed floodlight"}, "item": "standing_directed_floodlight", "description": "A large, heavy light that illuminates a wide semicircle around it when switched on.", "copy-from": "directed_floodlight", "flags": ["HALF_CIRCLE_LIGHT", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "requirements": {"removal": {"time": "5 s"}}}, {"id": "ap_wall_wiring", "type": "vehicle_part", "name": {"str": "wall wirings"}, "item": "wall_wiring", "categories": ["other"], "color": "white", "broken_color": "red", "durability": 20, "description": "Electric wires embedded in the wall.", "flags": ["APPLIANCE", "OBSTACLE", "WALL_MOUNTED", "WIRING"], "variants": [{"symbols": "_", "symbols_broken": "/"}]}, {"type": "vehicle_part", "id": "ap_folding_solar_panel", "name": {"str": "deployed folding solar panel"}, "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "This folding solar panel has been unfolded, and it is ready to charge connected devices, if the sun is out.", "copy-from": "solar_panel", "color": "light_blue", "broken_color": "light_gray", "proportional": {"durability": 0.5}, "epower": "120 W", "requirements": {"removal": {"time": "5 s"}}, "breaks_into": [{"item": "folding_solar_panel", "damage": [5, 5]}], "remove_as": "folding_solar_panel", "item": "folding_solar_panel_deployed"}, {"type": "vehicle_part", "id": "ap_folding_solar_panel_v2", "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "name": {"str": "deployed solar suitcase"}, "description": "This solar suitcase has been unpacked and is ready to supply power.", "copy-from": "ap_folding_solar_panel", "epower": "200 W", "breaks_into": [{"item": "folding_solar_panel_v2", "damage": [5, 5]}], "requirements": {"removal": {"time": "1 m"}}, "remove_as": "folding_solar_panel_v2", "item": "folding_solar_panel_v2_deployed"}, {"type": "vehicle_part", "id": "ap_solar_panel", "name": {"str": "small solar panel"}, "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "A small solar panel, mounted on a frame and ready to power other appliances.", "copy-from": "solar_panel", "proportional": {"epower": 1.2}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_ground_solar_panel", "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "A solar panel, mounted on a frame and ready to power other appliances.", "item": "ground_solar_panel", "copy-from": "solar_panel", "//": "4 times the panels, 1.2x matching the above appliance", "proportional": {"epower": 4.8}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}, "repair": {"skills": [["electronics", 6]], "time": "120 m", "using": [["vehicle_screw", 1], ["solar_panel", 4], ["soldering_standard", 8]]}}, "breaks_into": [{"item": "steel_lump", "count": [8, 32]}, {"item": "steel_chunk", "count": [8, 32]}, {"item": "scrap", "count": [8, 32]}, {"item": "solar_cell", "count": [4, 16]}], "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_reinforced_solar_panel", "color": "yellow", "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "A reinforced solar panel, mounted on a frame and ready to power other appliances.", "copy-from": "reinforced_solar_panel", "proportional": {"epower": 1.2}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_solar_panel_v2", "name": {"str": "small advanced solar panel"}, "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "A  small, high-performance solar panel, mounted on a frame and ready to power other appliances.", "copy-from": "solar_panel_v2", "proportional": {"epower": 1.2}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_ground_solar_panel_v2", "copy-from": "solar_panel_v2", "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "item": "ground_solar_panel_v2", "//": "4 times from 4 more panels, 1.2 times as is done above", "proportional": {"epower": 4.8}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}, "repair": {"skills": [["electronics", 8]], "time": "150 m", "using": [["vehicle_screw", 1], ["solar_panel_v2", 4], ["soldering_standard", 64]]}}, "breaks_into": [{"item": "steel_lump", "count": [8, 16]}, {"item": "steel_chunk", "count": [8, 16]}, {"item": "scrap", "count": [8, 16]}, {"item": "solar_cell_v2", "count": [4, 24]}]}, {"type": "vehicle_part", "id": "ap_reinforced_solar_panel_v2", "color": "yellow", "flags": ["SOLAR_PANEL", "OBSTACLE", "APPLIANCE"], "description": "A high-performance reinforced solar panel, mounted on a frame and ready to power other appliances.", "copy-from": "reinforced_solar_panel_v2", "proportional": {"epower": 1.2}, "requirements": {"removal": {"time": "3 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "#", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_wind_turbine", "location": "structure", "flags": ["WIND_TURBINE", "OBSTACLE", "APPLIANCE"], "description": "A mounted wind turbine, ready to power other appliances.", "copy-from": "wind_turbine", "requirements": {"removal": {"time": "15 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "T", "symbols_broken": "X"}]}, {"type": "vehicle_part", "id": "ap_xl_wind_turbine", "location": "structure", "flags": ["WIND_TURBINE", "OBSTACLE", "APPLIANCE"], "description": "A large mounted wind turbine, ready to power other appliances.", "copy-from": "xl_wind_turbine", "requirements": {"removal": {"time": "15 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "Y", "symbols_broken": "X"}]}, {"type": "vehicle_part", "id": "ap_water_wheel", "location": "structure", "flags": ["WATER_WHEEL", "OBSTACLE", "APPLIANCE"], "description": "A mounted water wheel, ready to power other appliances.", "copy-from": "water_wheel", "requirements": {"removal": {"time": "15 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "x", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_xl_water_wheel", "location": "structure", "flags": ["WATER_WHEEL", "OBSTACLE", "APPLIANCE"], "description": "A mounted large water wheel, ready to power other appliances.", "copy-from": "xl_water_wheel", "requirements": {"removal": {"time": "15 m", "using": [["vehicle_wrench_2", 1]]}}, "variants": [{"symbols": "x", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_battery_small", "name": {"str": "small grid battery"}, "color": "white", "flags": ["APPLIANCE"], "description": "A small battery to store electrical power in a static power grid.", "copy-from": "small_storage_battery", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "b", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_battery_medium", "name": {"str": "medium grid battery"}, "flags": ["APPLIANCE"], "description": "A medium battery to store electrical power in a static power grid.", "copy-from": "medium_storage_battery", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "B", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_battery", "name": {"str": "large grid battery"}, "color": "light_red", "flags": ["OBSTACLE", "APPLIANCE"], "description": "A large battery to store electrical power in a static power grid.", "copy-from": "storage_battery", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "B", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_battery_large", "name": {"str": "very large grid battery"}, "color": "red", "flags": ["OBSTACLE", "APPLIANCE"], "description": "A very large battery to store electrical power in a static power grid.", "copy-from": "large_storage_battery", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "B", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_battery_car", "name": {"str": "car grid battery"}, "categories": ["energy"], "looks_like": "battery_car", "color": "red", "flags": ["OBSTACLE", "APPLIANCE"], "description": "A car battery wired into a static power grid.", "item": "battery_car", "breaks_into": [{"item": "steel_lump", "count": [6, 9]}, {"item": "steel_chunk", "count": [6, 9]}, {"item": "scrap", "count": [6, 9]}], "durability": 120, "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "B", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_washing_machine", "looks_like": "f_washer", "flags": ["OBSTACLE", "APPLIANCE", "WASHING_MACHINE", "CARGO", "COVERED", "ENABLED_DRAINS_EPOWER"], "description": "An appliance used to clean laundry, plugged into a power supply and using internal water tanks.", "size": "135 L", "copy-from": "washing_machine", "item": "household_washing_machine", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "W", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_dishwasher", "name": {"str": "dishwasher"}, "looks_like": "f_dishwasher", "flags": ["OBSTACLE", "APPLIANCE", "DISHWASHER", "CARGO", "COVERED", "ENABLED_DRAINS_EPOWER"], "description": "An appliance used to clean dishes, plugged into a power supply and using internal tanks.", "size": "120 L", "copy-from": "dishwasher", "item": "household_dishwasher", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "D", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_autoclave", "name": {"str": "small autoclave"}, "looks_like": "f_autoclave", "color": "light_blue", "flags": ["OBSTACLE", "APPLIANCE", "AUTOCLAVE", "CARGO", "COVERED", "ENABLED_DRAINS_EPOWER"], "description": "An autoclave that can steam its contents at high enough temperatures to completely sterilize them, killing any possible contaminants.  This one is plugged into a power grid and running off internal water tanks.", "copy-from": "autoclave", "item": "autoclave", "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "D", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_foot_locker_recharge_station", "name": {"str": "foot locker recharging station"}, "categories": ["cargo"], "color": "light_green", "broken_color": "blue", "damage_modifier": 10, "durability": 40, "description": "A universal recharging station integrated into a steel storage compartment.  When turned on, it will steadily charge all rechargeable batteries (battery cells, lead-acid batteries, etc) placed directly within its storage space.", "bonus": 600, "size": "200 L", "item": "foot_locker_recharge_station", "flags": ["CARGO", "OBSTACLE", "RECHARGE", "COVERED", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"item": "cable", "charges": [1, 3]}, {"item": "steel_lump", "count": [4, 6]}, {"item": "steel_chunk", "count": [4, 8]}, {"item": "scrap", "count": [5, 7]}], "damage_reduction": {"all": 10}, "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "o", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_box_battery_charger", "name": {"str": "box battery charger"}, "categories": ["cargo"], "color": "light_green", "broken_color": "blue", "damage_modifier": 10, "durability": 20, "description": "A small device for recharging batteries integrated into a small wooden storage compartment.  When turned on, it will slowly charge all rechargeable batteries (battery cells, lead-acid batteries, etc) placed directly within its storage space.", "bonus": 15, "size": "50 L", "item": "box_battery_charger", "flags": ["CARGO", "OBSTACLE", "RECHARGE", "COVERED", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"item": "splinter", "count": [3, 5]}, {"item": "nail", "charges": [10, 15]}, {"item": "plastic_chunk", "prob": 50}, {"item": "cable", "charges": [1, 4]}, {"item": "e_scrap", "count": [0, 2]}, {"item": "scrap", "count": [1, 3]}], "damage_reduction": {"all": 5}, "requirements": {"removal": {"time": "6 m"}}, "variants": [{"symbols": "o", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_compact_ASRG_containment", "name": {"str": "advanced Stirling radioisotope generator"}, "looks_like": "f_compact_ASRG_containment", "categories": ["energy"], "color": "green_white", "broken_color": "blue", "location": "structure", "damage_modifier": 80, "durability": 400, "description": "A rather heavy brick of metal housing an ASRG, formerly designed by NASA and repurposed for terrestrial use.  Will provide about 130 W, basically forever as far as you're concerned.  Even though the alpha radiation these run off of isn't very dangerous in case of a breach, this unit appears to be armored to take a beating and is a good deal heavier owing to its composite plating.", "epower": "130 W", "item": "compact_ASRG_containment", "requirements": {"repair": {"skills": [["mechanics", 7]], "time": "60 m", "using": [["repair_welding_standard", 5], ["soldering_standard", 5]]}}, "flags": ["OBSTACLE", "RADIOACTIVE", "COVERED", "APPLIANCE", "PERPETUAL", "REACTOR"], "breaks_into": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}, {"item": "plutonium", "count": [0, 2]}, {"item": "lead", "charges": [12, 18]}], "damage_reduction": {"all": 60}, "variants": [{"symbols": "0", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_active_backup_generator", "name": {"str": "active backup generator"}, "looks_like": "f_active_backup_generator", "categories": ["energy"], "color": "green_white", "broken_color": "blue", "location": "structure", "damage_modifier": 80, "durability": 400, "description": "A ubiquitous piece of compact machinery meant for running anything from the bathroom lights to electric stoves and other necessities when grid electricity is unavailable.", "//1": "BIZARRE FIELD NAMES HERE! Weird stuff happening! Epower determines how fast it is converted, power determines the ratio of diesel(mL) to battery charges output. In other words, *power* is *fuel efficiency*.", "//2": "On a normal vehicle 250mL of diesel in an I4 with a truck alternator (what the active backup gen uses) should provide ~1692 charges (kW?) of power in about 24 minutes based on a test setup. That's a little less than 7W power, rounded up as I assume a larger dedicated generator is more efficient.", "power": "7 W", "//3": "7.5 kW alternator value, minus power consumption of the engine", "epower": "7300 W", "//4": "This value doesn't seem to do anything for the appliance version? Let's keep it commented out for future reference.", "//energy_consumption": "1000 W", "m2c": 5, "//5": "Actually accepts any diesel-type fuel but does NOT account for their relative fuel values! For diesel this is not a huge deal, only a few % inaccuracy, but for e.g. gas turbines this would be very bad!", "fuel_type": "diesel", "//6": "Appliance engine does NOT accept having these fields.", "//fuel_options": ["diesel", "biodiesel", "lamp_oil", "motor_oil", "jp8"], "//damaged_power_factor": 0.25, "item": "active_backup_generator", "requirements": {"repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 5], ["soldering_standard", 5]]}}, "flags": ["ENGINE", "E_DIESEL_FUEL", "OBSTACLE", "COVERED", "APPLIANCE", "REACTOR", "FLUIDTANK"], "breaks_into": [{"item": "scrap", "count": [4, 16]}, {"item": "steel_chunk", "count": [1, 6]}], "damage_reduction": {"all": 60}, "variants": [{"symbols": "0", "symbols_broken": "#"}]}, {"id": "app_microwave", "type": "vehicle_part", "name": {"str": "grid microwave"}, "looks_like": "microwave", "item": "microwave", "description": "A microwave connected to a power grid, ready to cook some food.", "categories": ["utility"], "location": "center", "color": "green", "broken_color": "blue", "damage_modifier": 10, "damage_reduction": {"all": 5}, "durability": 25, "flags": ["APPLIANCE", "CARGO"], "requirements": {"repair": {"skills": [["mechanics", 3], ["electronics", 2]], "time": "20 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}, "removal": {"time": "1 m"}}, "pseudo_tools": [{"id": "microwave"}], "size": 20, "breaks_into": [{"item": "plastic_chunk", "count": [4, 7]}, {"item": "steel_chunk", "count": [3, 6]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "scrap", "count": [4, 7]}, {"item": "element", "count": [0, 2]}, {"item": "glass_shard", "prob": 50}, {"item": "motor_micro", "prob": 25}], "variants": [{"symbols": "Y", "symbols_broken": "x"}]}, {"type": "vehicle_part", "id": "ap_oxygen_concentrator", "name": {"str": "oxygen concentrator"}, "categories": ["utility"], "broken_color": "dark_gray", "damage_modifier": 35, "durability": 100, "description": "A machine capable of generating pure oxygen from the air.  It has been plugged into a power grid.", "item": "oxygen_concentrator", "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "pseudo_app_oxygen_concentrator", "hotkey": "h"}], "breaks_into": [{"item": "plastic_chunk", "count": [4, 8]}, {"item": "scrap", "count": [4, 8]}, {"item": "steel_chunk", "count": [5, 10]}], "damage_reduction": {"all": 40}, "variants": [{"symbols": "&", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_hd_compressor_unit", "name": {"str": "high pressure compressor unit"}, "categories": ["utility"], "broken_color": "dark_gray", "damage_modifier": 45, "durability": 150, "description": "A machine designed to compress an input gas to a very high pressure.  It has been plugged into a power grid.", "item": "hd_compressor_unit", "flags": ["OBSTACLE", "COVERED", "APPLIANCE"], "pseudo_tools": [{"id": "pseudo_app_hd_compressor_unit", "hotkey": "h"}], "breaks_into": [{"item": "plastic_chunk", "count": [1, 2]}, {"item": "scrap", "count": [6, 12]}, {"item": "steel_chunk", "count": [8, 16]}, {"item": "sheet_metal_small", "count": [1, 4]}], "damage_reduction": {"all": 50}, "variants": [{"symbols": "&", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_glassblowers_crucible", "name": {"str": "glassblower's crucible"}, "color": "brown", "categories": ["utility"], "description": "An electric crucible, powered by your electric grid.  Designed for melting large quantities of glass for batch crafting and production of large items, it needs to be placed and plugged into a power source.", "broken_color": "yellow_red", "damage_modifier": 10, "damage_reduction": {"all": 30}, "durability": 80, "flags": ["OBSTACLE", "APPLIANCE"], "pseudo_tools": [{"id": "glassblowers_crucible_tool"}], "item": "glassblowers_crucible", "breaks_into": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "ceramic_shard", "count": [21, 42]}, {"item": "rock", "count": [6, 12]}], "variants": [{"symbols": "o", "symbols_broken": "x"}]}]