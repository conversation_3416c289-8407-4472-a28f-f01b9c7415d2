[{"type": "furniture", "id": "f_bookcase", "name": "bookcase", "symbol": "{", "description": "A simple wooden shelf for storing dozens of books.  While designed for books, it does a decent job of storing anything else that'll fit.", "color": "brown", "move_cost_mod": -1, "coverage": 80, "required_str": 9, "flags": ["FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "2x4", "count": 12}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [12, 16]}]}, "max_volume": "2000 L", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 12]}, {"item": "splinter", "count": 1}]}, "shoot": {"reduce_damage": [5, 20], "reduce_damage_laser": [5, 20], "destroy_damage": [15, 45]}}, {"type": "furniture", "id": "f_entertainment_center", "name": "entertainment center", "symbol": "{", "description": "While not quite as cool by itself as the name might imply, this large wooden cabinet can store a variety of things, like a TV and media systems, with shelving space and cupboards for anything that'll fit.", "color": "brown", "looks_like": "f_bookcase", "move_cost_mod": -1, "coverage": 80, "required_str": 10, "flags": ["FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR", "SMALL_HIDE"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "2x4", "count": 14}, {"item": "wood_panel", "count": 2}, {"item": "nail", "charges": [12, 20]}]}, "max_volume": "2000 L", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 12]}, {"item": "nail", "charges": [4, 20]}, {"item": "splinter", "count": [6, 10]}, {"item": "wood_panel", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_coffin_c", "name": "coffin", "looks_like": "f_coffin_o", "description": "A humble wooden casket for the respectful burial of the dead.  While a standard practice before this all happened, it is now a rare honor for one to be given a proper final resting place.  An honor that countless many will likely never receive.", "symbol": "0", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 40, "required_str": 14, "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "SHORT", "FLAT_SURF"], "deconstruct": {"items": [{"item": "2x4", "count": 12}, {"item": "nail", "charges": [12, 24]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "sheet_cotton", "count": 3}, {"item": "cotton_patchwork", "count": 6}]}, "max_volume": "875 L", "prying": {"result": "f_coffin_o", "message": "You wedge open the coffin.", "prying_data": {"difficulty": 5, "prying_level": 2, "noisy": true, "failure": "You pry, but the coffin remains closed."}}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "wham!", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "splinter", "count": [2, 6]}, {"item": "nail", "charges": [2, 10]}, {"item": "sheet_cotton", "count": 1}, {"item": "cotton_patchwork", "count": [8, 12]}]}}, {"type": "furniture", "id": "f_coffin_o", "name": "open coffin", "description": "A humble wooden casket for the respectful burial of the dead.  While a standard practice before this all happened, it is now a rare honor for one to be given a proper final resting place.  This one is open and unoccupied, and gazing inside fills you with a sense of melancholic weariness.", "symbol": "O", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 35, "required_str": 12, "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "SHORT"], "deconstruct": {"items": [{"item": "2x4", "count": 12}, {"item": "nail", "charges": [12, 24]}, {"item": "wood_panel", "count": [0, 1]}, {"item": "sheet_cotton", "count": 3}, {"item": "cotton_patchwork", "count": 6}]}, "max_volume": "875 L", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "wham!", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "splinter", "count": [2, 6]}, {"item": "nail", "charges": [2, 10]}, {"item": "sheet_cotton", "count": 1}, {"item": "cotton_patchwork", "count": [8, 12]}]}}, {"type": "furniture", "id": "f_crate_c", "name": "crate", "description": "A sealed wooden storage container.  Lacking any labels, it could hold just about anything inside.  If you don't have a proper tool to pry it open, smashing it is an option, albeit one that risks destroying the contents.", "symbol": "X", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "SHORT"], "examine_action": "locked_object", "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": [1, 2]}, {"item": "nail", "charges": [6, 10]}]}, "prying": {"result": "f_crate_o", "message": "You pop open the crate.", "prying_data": {"difficulty": 6, "prying_level": 1, "noisy": true, "failure": "You pry, but can't pop open the crate."}}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "wham!", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "nail", "charges": [2, 10]}]}}, {"type": "furniture", "id": "f_crate_o", "name": "open crate", "description": "An open wooden storage box, capable of holding any number of things.  The lid has been pried off and is leaned adjacent to it, and with a fresh set of nails, could be sealed back shut.", "symbol": "O", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 60, "required_str": 10, "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "SHORT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [6, 10]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "wham!", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "nail", "charges": [2, 10]}]}}, {"type": "furniture", "id": "f_cardboard_box", "name": "large cardboard box", "symbol": "X", "description": "A large box made of brown corrugated paper.  Could contain a number of things, and you could even hide inside.  Considering it only has two small flaps for carrying, it's very hard to see out of, and won't do anything to protect you if you're found.", "color": "brown", "move_cost_mod": 7, "coverage": 90, "comfort": 1, "floor_bedding_warmth": 200, "required_str": 3, "deconstruct": {"items": [{"item": "box_large", "count": 1}]}, "max_volume": "324 L", "deployed_item": "box_large", "examine_action": "deployed_furniture", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "EASY_DECONSTRUCT", "HIDE_PLACE", "NO_SIGHT", "CONTAINER"], "bash": {"str_min": 2, "str_max": 15, "sound": "crumple!", "sound_fail": "thud.", "items": [{"item": "cardboard", "count": [150, 180]}]}}, {"type": "furniture", "id": "f_cardboard_box_large_reinforced", "name": "reinforced large cardboard box", "symbol": "X", "description": "A large box made of brown corrugated paper that was doubled up for extra strength.  Could contain a number of things, and you could even hide inside.  Considering it only has two small flaps for carrying, it's very hard to see out of, and won't do anything to protect you if you're found.", "color": "brown", "looks_like": "f_cardboard_box", "move_cost_mod": 7, "coverage": 90, "comfort": 1, "floor_bedding_warmth": 200, "required_str": 3, "deconstruct": {"items": [{"item": "box_large_reinforced", "count": 1}]}, "max_volume": "324 L", "deployed_item": "box_large_reinforced", "examine_action": "deployed_furniture", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "PLACE_ITEM", "ORGANIC", "EASY_DECONSTRUCT", "HIDE_PLACE", "NO_SIGHT", "CONTAINER"], "bash": {"str_min": 3, "str_max": 16, "sound": "crumple!", "sound_fail": "thud.", "items": [{"item": "cardboard", "count": [300, 360]}]}}, {"type": "furniture", "id": "f_dresser", "name": "dresser", "symbol": "{", "description": "A simple wooden cabinet with a column of short drawers.  While intended for storing clothes, there's nothing stopping you from storing whatever fits.", "color": "brown", "move_cost_mod": -1, "coverage": 70, "required_str": 8, "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR", "MOUNTABLE"], "deconstruct": {"items": [{"item": "2x4", "count": 6}, {"item": "wood_panel", "count": [0, 1]}, {"item": "nail", "charges": [6, 8]}]}, "max_volume": "2000 L", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 8]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_glass_cabinet", "name": "glass front cabinet", "symbol": "[", "looks_like": "f_display_rack", "description": "A tall metal cabinet with a sheet of glass across the front for viewing the contents.  Often used for displaying rare, visually pleasing, or otherwise valuable goods, it's odd that it doesn't have a lock.", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 8, "flags": ["PLACE_ITEM", "TRANSPARENT", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [4, 16]}, {"item": "glass_sheet", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "pipe", "count": [0, 4]}]}, "max_volume": "400 L", "bash": {"str_min": 12, "str_max": 40, "sound": "glass breaking!", "sound_fail": "crunch!", "items": [{"item": "2x4", "count": [1, 4]}, {"item": "splinter", "count": [1, 4]}, {"item": "nail", "charges": [4, 8]}, {"item": "glass_shard", "count": [16, 100]}, {"item": "scrap", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_gunsafe_o", "name": "open gun safe", "description": "A large, heavy container with thick metal walls, designed to securely store firearms, weapon mods, and ammunition.  This one is wide open.", "symbol": "X", "color": "light_gray", "looks_like": "f_gunsafe_ml", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "close": "f_gunsafe_c", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE"], "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_gunsafe_c", "name": "closed gun safe", "description": "A large, heavy container with thick metal walls and a rotary combination lock, designed to securely store firearms, weapon mods, and ammunition.  This one is closed, but not locked.", "symbol": "X", "color": "light_gray", "looks_like": "f_gunsafe_ml", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "open": "f_gunsafe_o", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE"], "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_gunsafe_ml", "name": "locked gun safe", "description": "A large, heavy container with thick metal walls and a rotary combination lock, designed to securely store firearms, weapon mods, and ammunition.  If you had something to pick its lock with, you could probably open it.", "symbol": "X", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE", "PICKABLE"], "lockpick_result": "f_gunsafe_o", "lockpick_message": "With a satisfying click, the lock on the gun safe door opens.", "examine_action": "locked_object_pickable", "oxytorch": {"result": "f_safe_o", "duration": "14 seconds"}, "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_gunsafe_mj", "name": "jammed gun safe", "looks_like": "f_gunsafe_ml", "description": "A large, heavy container with thick metal walls and a rotary combination lock, designed to securely store firearms, weapon mods, and ammunition.  Unfortunately, the lock is completely broken, and short of some pretty serious machinery, you have no possible way of opening it.", "symbol": "X", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE"], "oxytorch": {"result": "f_safe_o", "duration": "14 seconds"}, "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_gun_safe_el", "name": "electronic gun safe", "looks_like": "f_gunsafe_ml", "description": "A large, heavy container with thick metal walls and an electronic locking system, designed to securely store firearms, weapon mods, and ammunition.  If you had some way of hacking into it, you could probably crack it open.", "symbol": "X", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE"], "oxytorch": {"result": "f_safe_o", "duration": "14 seconds"}, "examine_action": "gunsafe_el", "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_locker", "name": "locker", "symbol": "{", "description": "A tall sheet metal cabinet, useful for storing just about anything that'll fit.", "color": "light_gray", "move_cost_mod": -1, "coverage": 90, "required_str": 9, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "sheet_metal", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "lock", "count": [0, 1]}, {"item": "pipe", "count": [4, 8]}]}, "max_volume": "500 L", "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 10]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "pipe", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_mailbox", "name": "mailbox", "symbol": "P", "description": "A small metal box on top of a wooden post, designed to receive mail deliveries.  Although considering the circumstances, it will likely never see proper use again.", "color": "light_gray", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "CONTAINER", "PLACE_ITEM", "MOUNTABLE"], "deconstruct": {"items": [{"item": "wooden_post", "count": 1}, {"item": "nail", "charges": [2, 5]}, {"item": "sheet_metal", "count": 1}]}, "max_volume": "10 L", "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "nail", "charges": [1, 5]}, {"item": "splinter", "count": [4, 16]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "scrap", "count": [4, 10]}]}}, {"type": "furniture", "id": "f_clothing_rail", "name": "clothing rail", "looks_like": "f_rack", "description": "A metal frame on a set of wheels used for hanging large amounts of clothes.  Usually used in theater or retail environments, it's easy to use and quick to access.", "symbol": "{", "color": "light_gray", "move_cost_mod": -1, "coverage": 10, "required_str": 8, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "PLACE_ITEM", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "pipe", "count": [4, 6]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "wheel_caster", "count": 1}]}, "max_volume": "1750 L", "bash": {"str_min": 6, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "pipe", "count": 1}, {"item": "wheel_caster", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_rack", "name": "display rack", "description": "A sheet metal shelving unit, with the storage surfaces angled in such a way as to show off the items stored.", "symbol": "{", "color": "light_gray", "move_cost_mod": -1, "coverage": 70, "required_str": 8, "hacksaw": {"duration": "2 minutes", "message": "You finish cutting the metal.", "byproducts": [{"item": "pipe", "count": [1, 3]}, {"item": "steel_chunk", "count": 1}]}, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE"], "rotates_to": "INDOORFLOOR", "oxytorch": {"duration": "1 seconds", "byproducts": [{"item": "steel_chunk", "count": [2, 6]}]}, "deconstruct": {"items": [{"item": "pipe", "count": [6, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [0, 4]}]}, "max_volume": "1750 L", "bash": {"str_min": 10, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "pipe", "count": 1}]}}, {"type": "furniture", "id": "f_rack_l", "looks_like": "f_rack", "name": "locked display rack", "description": "A sheet metal shelving unit, with the storage surfaces angled in such a way as to show off the items stored.  Metal fasteners with a lock hold the items securely to the rack.", "symbol": "{", "color": "light_gray", "move_cost_mod": -1, "coverage": 70, "required_str": 8, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE", "SEALED", "PICKABLE"], "rotates_to": "INDOORFLOOR", "lockpick_result": "f_rack", "lockpick_message": "With a satisfying click, the lock opens.", "examine_action": "locked_object_pickable", "deconstruct": {"items": [{"item": "pipe", "count": [6, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [0, 4]}]}, "max_volume": "1750 L", "bash": {"str_min": 10, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "pipe", "count": 1}]}}, {"type": "furniture", "id": "f_rack_wood", "name": "wooden rack", "symbol": "{", "description": "A wooden shelving unit with angled storage surfaces designed to show off whatever is stored on it.", "color": "brown", "move_cost_mod": -1, "coverage": 70, "required_str": 7, "looks_like": "f_bookcase", "flags": ["FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR", "TRANSPARENT"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "2x4", "count": 12}, {"item": "wood_panel", "count": [1, 2]}, {"item": "nail", "charges": [32, 40]}]}, "max_volume": "1500 L", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [2, 6]}, {"item": "nail", "charges": [20, 40]}, {"item": "splinter", "count": 12}]}}, {"type": "furniture", "id": "f_rack_coat", "name": "coat rack", "description": "A tall wooden pole with a set of hooks used to store outdoor jackets and hats to allow easy access.", "symbol": "Y", "color": "brown", "move_cost_mod": -1, "coverage": 30, "required_str": 4, "flags": ["TRANSPARENT", "FLAMMABLE", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE"], "deconstruct": {"items": [{"item": "nail", "charges": [2, 6]}, {"item": "2x4", "count": 1}, {"item": "stick_long", "count": 1}]}, "max_volume": "30 L", "bash": {"str_min": 6, "str_max": 30, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "splinter", "count": [3, 8]}, {"item": "nail", "charges": [1, 3]}, {"item": "2x4", "count": 1}]}}, {"type": "furniture", "id": "f_recycle_bin", "name": "recycle bin", "description": "A large plastic bin painted green with a 'recycle' symbol emblazoned on it.  While intended to store discarded things to be processed back into a factory, the drastic change in priorities as of late means that these may hold valuable materials.", "symbol": "{", "color": "light_green", "move_cost_mod": -1, "coverage": 40, "required_str": 5, "max_volume": "200 L", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "CONTAINER", "PLACE_ITEM", "MOUNTABLE", "SHORT", "SMALL_HIDE"], "bash": {"str_min": 8, "str_max": 30, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "plastic_chunk", "count": [2, 7]}, {"item": "pipe", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_safe_c", "name": "safe", "looks_like": "f_gunsafe_ml", "description": "A small, heavy, and near-unbreachable metal box with a rotary combination lock.  This one isn't actually locked, just closed.", "symbol": "X", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE"], "open": "f_safe_o", "examine_action": "open_safe", "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_safe_l", "name": "safe", "looks_like": "f_safe_c", "symbol": "X", "description": "A small, heavy, and near-unbreachable metal box with a rotary combination lock.  With something to listen really closely and a hell of a lot of time, you might be able to crack it.", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "PLACE_ITEM", "MOUNTABLE"], "oxytorch": {"result": "f_safe_o", "duration": "14 seconds"}, "examine_action": "safe", "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_safe_o", "name": "open safe", "looks_like": "f_safe_c", "description": "A small, heavy, and near-unbreachable metal box with a rotary combination lock, albeit significantly less secure with the door open.", "symbol": "O", "color": "light_gray", "move_cost_mod": -1, "coverage": 30, "required_str": 14, "max_volume": "250 L", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE"], "close": "f_safe_c", "bash": {"str_min": 40, "str_max": 200, "sound": "screeching metal!", "sound_fail": "whump!", "items": [{"item": "steel_chunk", "count": [1, 5]}, {"item": "scrap", "count": [1, 5]}, {"item": "rock", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_trashcan", "name": "trash can", "symbol": "&", "description": "A plastic bin for storing discarded waste as to be disposed of later.  Although, considering the circumstances, it might be worth seeing what's inside.", "color": "light_cyan", "move_cost_mod": 1, "required_str": 5, "max_volume": "40 L", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "CONTAINER", "PLACE_ITEM", "MOUNTABLE", "SHORT", "SMALL_HIDE", "EASY_DECONSTRUCT"], "bash": {"str_min": 1, "str_max": 1, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "trashcan", "count": 1, "damage": [2, 4]}]}, "deconstruct": {"items": [{"item": "trashcan", "count": 1}]}}, {"type": "furniture", "id": "f_metal_trashcan", "name": "metal trash can", "symbol": "&", "looks_like": "f_trashcan", "description": "A crude metal bin typically used to dispose of waste.", "//": "The constructed trash can variant, deliberately looks like the original. Does not spawn naturally, although feel free to add it to worldgen.", "color": "light_cyan", "move_cost_mod": 1, "required_str": 5, "max_volume": "20 L", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE", "SHORT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "sheet_metal_small", "count": [10, 14]}, {"item": "steel_chunk", "count": [1, 2]}, {"item": "scrap", "count": [6, 12]}]}, "bash": {"str_min": 9, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "sheet_metal_small", "count": [3, 6]}, {"item": "steel_chunk", "count": [6, 8]}, {"item": "scrap", "count": [8, 16]}]}}, {"type": "furniture", "id": "f_wardrobe", "name": "wardrobe", "looks_like": "f_dresser", "description": "A very large wooden cabinet for storing clothes, effectively an upright closet.  Could technically be used to store anything else that would fit, though.", "symbol": "{", "color": "i_brown", "move_cost_mod": -1, "coverage": 85, "required_str": 9, "flags": ["CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR", "MOUNTABLE", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "2x4", "count": 20}, {"item": "nail", "charges": [16, 24]}, {"item": "pipe", "count": 2}]}, "max_volume": "500 L", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [8, 12]}, {"item": "nail", "charges": [8, 14]}, {"item": "splinter", "count": [4, 10]}, {"item": "pipe", "count": [0, 1]}, {"item": "scrap", "count": [2, 5]}]}}, {"type": "furniture", "id": "f_torah_ark", "name": "Torah Ark", "description": "An ornately-carved cabinet designed to hold a Torah scroll.  This one is decorated with a tree motif, referencing Proverbs 3:18.", "symbol": "{", "color": "i_brown", "move_cost_mod": -1, "coverage": 85, "required_str": 9, "flags": ["CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "BLOCKSDOOR", "MOUNTABLE"], "deconstruct": {"items": [{"item": "2x4", "count": 20}, {"item": "nail", "charges": [16, 24]}, {"item": "pipe", "count": 2}]}, "max_volume": "100 L", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [8, 12]}, {"item": "nail", "charges": [8, 14]}, {"item": "splinter", "count": [4, 10]}, {"item": "pipe", "count": [0, 1]}, {"item": "scrap", "count": [2, 5]}]}}, {"type": "furniture", "id": "f_filing_cabinet", "name": "filing cabinet", "looks_like": "f_rack", "description": "A rack of metal drawers designed to hold various files and paperwork.  Paperwork that has more than likely lost all relevance and value by now.", "symbol": "}", "color": "dark_gray", "move_cost_mod": 2, "coverage": 70, "required_str": 7, "flags": ["PLACE_ITEM", "TRANSPARENT", "CONTAINER", "BLOCKSDOOR", "MOUNTABLE", "NO_SELF_CONNECT"], "deconstruct": {"items": [{"item": "sheet_metal", "count": [2, 6]}, {"item": "scrap", "count": [2, 6]}, {"item": "lock", "count": [0, 1]}]}, "max_volume": "200 L", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [0, 6]}, {"item": "sheet_metal", "count": [0, 4]}]}}, {"type": "furniture", "id": "f_utility_shelf", "name": "utility shelf", "symbol": "{", "looks_like": "f_rack", "description": "A simple heavy-duty plastic and metal shelving unit, intended to store tools and materials for easy access to workers.", "color": "light_gray", "move_cost_mod": -1, "coverage": 55, "required_str": 6, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "EASY_DECONSTRUCT", "TRANSPARENT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "sheet_metal_small", "count": [4, 6]}, {"item": "plastic_chunk", "count": [2, 12]}, {"item": "pipe", "count": [4, 8]}]}, "max_volume": "1500 L", "bash": {"str_min": 16, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "plastic_chunk", "count": [1, 6]}, {"item": "sheet_metal_small", "count": [1, 4]}, {"item": "pipe", "count": 1}]}}, {"type": "furniture", "id": "f_warehouse_shelf", "name": "warehouse shelf", "description": "A huge, sturdy steel shelf for storing pallets of crates in warehouses.", "symbol": "{", "looks_like": "f_utility_shelf", "color": "light_gray", "move_cost_mod": -1, "coverage": 95, "required_str": 10, "flags": ["FLAMMABLE_HARD", "PLACE_ITEM", "BLOCKSDOOR", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "pipe", "count": [6, 12]}, {"item": "sheet_metal", "count": [4, 8]}, {"item": "sheet_metal_small", "count": [0, 4]}]}, "max_volume": "3500 L", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [3, 12]}, {"item": "steel_chunk", "count": [3, 6]}, {"item": "sheet_metal_small", "count": [6, 10]}, {"item": "pipe", "count": 4}]}}, {"type": "furniture", "id": "f_wood_keg", "name": "wooden keg", "looks_like": "f_standing_tank", "description": "A large standing wooden barrel, completely watertight.  Good for storing liquids of all kinds.", "symbol": "H", "color": "brown", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "flags": ["NOITEM", "SEALED", "ALLOW_FIELD_EFFECT", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "LIQUIDCONT"], "examine_action": "keg", "keg_capacity": 500, "deconstruct": {"items": [{"item": "2x4", "count": 18}, {"item": "nail", "charges": [7, 14]}, {"item": "water_faucet", "count": 1}, {"item": "sheet_metal_small", "count": [12, 20]}, {"item": "scrap", "count": [5, 10]}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [6, 12]}, {"item": "nail", "charges": [4, 8]}, {"item": "water_faucet", "prob": 50}, {"item": "sheet_metal_small", "count": [6, 12]}, {"item": "scrap", "count": [10, 20]}, {"item": "splinter", "count": 1}]}}, {"type": "furniture", "id": "f_displaycase", "name": "display case", "looks_like": "f_rack", "description": "A secure wooden case at about waist-height, with glass paneling on top.  Useful for storing valuable things while still showing them off.  Not actually as secure as it looks, as the display windows are easily broken.", "symbol": "#", "color": "light_cyan", "move_cost_mod": 2, "coverage": 80, "required_str": 9, "flags": ["TRANSPARENT", "SEALED", "PLACE_ITEM", "PICKABLE"], "rotates_to": "INDOORFLOOR", "lockpick_result": "f_displaycase_o", "lockpick_message": "With a satisfying click, the lock and the display case opens.", "examine_action": "locked_object_pickable", "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_displaycase_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_displaycase_o", "name": "open display case", "looks_like": "f_rack", "description": "A secure wooden case at about waist-height, with glass paneling on top.  Useful for storing valuable things while still showing them off.  Not actually as secure as it looks, as the display windows are easily broken.  This one has had its lock picked and is open.", "symbol": "#", "color": "light_cyan", "move_cost_mod": 2, "coverage": 80, "required_str": 9, "flags": ["TRANSPARENT", "PLACE_ITEM"], "rotates_to": "INDOORFLOOR", "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_displaycase_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_displaycase_b", "name": "broken display case", "looks_like": "f_rack", "description": "A secure wooden case at about waist-height, with glass paneling on top.  Would be useful for storing valuable things while still showing them off, if the glass hadn't been shattered.  Careful not to cut yourself when looting.", "symbol": "#", "color": "light_gray", "move_cost_mod": 2, "coverage": 80, "required_str": 9, "flags": ["TRANSPARENT", "PLACE_ITEM"], "rotates_to": "INDOORFLOOR", "bash": {"str_min": 8, "str_max": 30, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [3, 6]}, {"item": "splinter", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_board_map", "name": "map board", "looks_like": "f_bulletin", "description": "A secure metal board with a locked glass panel used to display a map.", "symbol": "6", "color": "light_gray", "move_cost_mod": -1, "coverage": 75, "required_str": -1, "flags": ["TRANSPARENT", "SEALED", "PLACE_ITEM", "PICKABLE"], "deconstruct": {"items": [{"item": "pipe", "count": 4}, {"item": "sheet_metal", "count": 1}, {"item": "hinge", "count": 2}, {"item": "glass_sheet", "count": 1}]}, "lockpick_result": "f_board_map_o", "lockpick_message": "With a satisfying click, the locked glass panel opens.", "examine_action": "locked_object_pickable", "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_board_map_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_board_map_o", "name": "open map board", "looks_like": "f_bulletin", "description": "A successfully picked metal board with an open glass panel used to display a map.", "symbol": "6", "color": "light_gray", "move_cost_mod": -1, "coverage": 75, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "pipe", "count": 4}, {"item": "sheet_metal", "count": 1}, {"item": "hinge", "count": 2}, {"item": "glass_sheet", "count": 1}]}, "rotates_to": "INDOORFLOOR", "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_board_map_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_board_map_b", "name": "broken map board", "looks_like": "f_bulletin", "description": "A metal board with a smashed glass panel used to display a map.", "symbol": "6", "color": "light_gray", "move_cost_mod": -1, "coverage": 75, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "pipe", "count": 4}, {"item": "sheet_metal", "count": 1}, {"item": "hinge", "count": [0, 1]}]}, "rotates_to": "INDOORFLOOR", "bash": {"str_min": 10, "str_max": 20, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 4]}, {"item": "sheet_metal_small", "count": [2, 4]}, {"item": "hinge", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_fireman_cabinet", "name": "fireman's cabinet", "looks_like": "f_displaycase", "description": "A metallic cabinet painted to stand out, visibly holding tools behind a thin panel of tempered glass.", "symbol": "#", "color": "light_red", "move_cost_mod": -1, "coverage": 80, "required_str": -1, "flags": ["TRANSPARENT", "SEALED", "PLACE_ITEM", "PICKABLE"], "lockpick_result": "f_fireman_cabinet_o", "lockpick_message": "With a satisfying click, the lock on the cabinet opens.", "examine_action": "locked_object_pickable", "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_fireman_cabinet_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_fireman_cabinet_o", "name": "unlocked fireman's cabinet", "looks_like": "f_displaycase", "description": "A metallic cabinet painted to stand out.  Its glass panel door was left unlocked and slightly ajar.", "symbol": "#", "color": "light_red", "move_cost_mod": -1, "coverage": 80, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "bash": {"str_min": 6, "str_max": 20, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 12, "furn_set": "f_fireman_cabinet_b", "items": [{"item": "glass_shard", "count": [25, 50]}]}}, {"type": "furniture", "id": "f_fireman_cabinet_b", "name": "open fireman's cabinet", "looks_like": "f_displaycase_b", "description": "A metallic cabinet painted to stand out.  Its protective glass panel lies broken from some past emergency or looting attempt.", "symbol": "#", "color": "light_red", "move_cost_mod": -1, "coverage": 80, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "bash": {"str_min": 10, "str_max": 20, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 8]}, {"item": "pipe", "count": [1, 3]}, {"item": "water_faucet", "prob": 50}]}}, {"type": "furniture", "id": "f_standing_tank", "name": "standing tank", "description": "A huge metal tank that can be used to safely store large amounts of liquid.", "symbol": "O", "color": "light_gray", "move_cost_mod": -1, "coverage": 90, "required_str": -1, "flags": ["CONTAINER", "LIQUIDCONT", "NOITEM", "SEALED", "TRANSPARENT"], "deconstruct": {"items": [{"item": "metal_tank", "count": 4}, {"item": "water_faucet", "count": 1}]}, "examine_action": "keg", "keg_capacity": 1200, "bash": {"str_min": 10, "str_max": 20, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 32]}, {"item": "water_faucet", "prob": 50}]}}, {"type": "furniture", "id": "f_storage_tank", "name": "bulk storage tank", "description": "A 2 meters high tank that can store up to 1500 liters of liquid.  That's a lot.", "symbol": "O", "looks_like": "f_standing_tank", "color": "light_gray", "move_cost_mod": -1, "coverage": 95, "required_str": -1, "flags": ["CONTAINER", "LIQUIDCONT", "NOITEM", "SEALED"], "deconstruct": {"items": [{"item": "sheet_metal", "count": 24}, {"item": "water_faucet", "count": 1}]}, "examine_action": "keg", "keg_capacity": 6000, "bash": {"str_min": 16, "str_max": 32, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [40, 160]}, {"item": "water_faucet", "prob": 50}]}}, {"type": "furniture", "id": "f_gas_tank", "name": "fuel tank", "description": "A tank filled with gasoline.", "looks_like": "f_standing_tank", "symbol": "Q", "color": "brown_red", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "LIQUIDCONT", "REDUCE_SCENT"], "bash": {"str_min": 40, "str_max": 100, "explosive": 20, "sound": "metal screeching!", "sound_fail": "clang!", "furn_set": "f_gas_tank_smashed"}}, {"type": "furniture", "id": "f_gas_tank_smashed", "name": "broken fuel tank", "description": "A broken tank which was filled with gasoline.", "looks_like": "f_wreckage", "symbol": "Q", "color": "light_red", "move_cost_mod": 6, "coverage": 30, "required_str": -1, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT"], "bash": {"str_min": 40, "str_max": 100, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "sheet_metal", "count": [1, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "furniture", "id": "f_diesel_tank", "name": "fuel tank", "description": "A tank filled with diesel.", "looks_like": "f_standing_tank", "symbol": "Q", "color": "brown_green", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "LIQUIDCONT", "REDUCE_SCENT"], "bash": {"str_min": 40, "str_max": 100, "explosive": 20, "sound": "metal screeching!", "sound_fail": "clang!", "furn_set": "f_diesel_tank_smashed"}}, {"type": "furniture", "id": "f_diesel_tank_smashed", "name": "broken fuel tank", "description": "A broken tank which was filled with diesel.", "looks_like": "f_wreckage", "symbol": "Q", "color": "light_green", "move_cost_mod": 6, "coverage": 30, "required_str": -1, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT"], "bash": {"str_min": 40, "str_max": 100, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "sheet_metal", "count": [1, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "furniture", "id": "f_dumpster", "name": "dumpster", "description": "A large metal dumpster that will likely not be getting picked up by the city's waste management any time soon.  Despite the unpleasant nature of climbing inside, it could make for a viable hiding spot.", "symbol": "{", "color": "green", "move_cost_mod": 3, "coverage": 70, "required_str": 16, "max_volume": "1500 L", "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "HIDE_PLACE", "NO_SIGHT", "FIRE_CONTAINER", "LIQUIDCONT"], "deconstruct": {"items": [{"item": "frame", "count": [2, 3]}, {"item": "sheet_metal", "count": [10, 12]}, {"item": "sheet_metal_small", "count": [60, 72]}, {"item": "steel_lump", "count": [12, 17]}, {"item": "steel_chunk", "count": [27, 33]}, {"item": "scrap", "count": [44, 55]}]}, "bash": {"str_min": 8, "str_max": 45, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "frame", "count": [0, 2]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "sheet_metal_small", "count": [30, 90]}, {"item": "steel_lump", "count": [10, 26]}, {"item": "steel_chunk", "count": [61, 173]}, {"item": "scrap", "count": [57, 164]}]}}, {"type": "furniture", "id": "f_butter_churn", "name": "butter churn", "description": "A metal tube with a built-in mixer for making butter.  Rather than needing electricity, it is pedal-driven, allowing use without power.", "symbol": "H", "color": "light_cyan", "crafting_pseudo_item": "butter_churn", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "flags": ["NOITEM", "SEALED", "ALLOW_FIELD_EFFECT", "TRANSPARENT", "FLAMMABLE", "CONTAINER", "LIQUIDCONT", "EASY_DECONSTRUCT"], "examine_action": "keg", "keg_capacity": 45, "deconstruct": {"items": [{"item": "churn", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [6, 12]}, {"item": "nail", "charges": [4, 8]}, {"item": "water_faucet", "prob": 50}, {"item": "sheet_metal_small", "count": [6, 12]}, {"item": "scrap", "count": [10, 20]}, {"item": "splinter", "count": [1, 20]}]}}, {"type": "furniture", "id": "f_metal_crate_r", "name": "riveted metal crate", "looks_like": "f_metal_crate_c", "description": "This huge box is made of a dull metal, riveted together.  There is no obvious opening mechanism, and the rivets don't match any of your tools.  The only way in would be to smash it.", "//": "To-do: There should be an Exodii riveting tool that can convert this to f_metal_crate_c", "symbol": "X", "color": "light_gray", "move_cost_mod": 5, "coverage": 80, "required_str": 12, "max_volume": "1000 L", "flags": ["CONTAINER", "SEALED", "BLOCKSDOOR", "MOUNTABLE"], "bash": {"str_min": 30, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "sheet_metal", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [2, 10]}]}}, {"type": "furniture", "id": "f_metal_crate_c", "name": "sealed metal crate", "looks_like": "t_crate_c", "description": "A huge, tightly sealed storage crate made from welded and riveted sheet metal.  The sealing mechanism is too tight to open bare-handed and would need some kind of prying instrument to release.", "symbol": "X", "color": "light_gray", "move_cost_mod": 5, "coverage": 80, "required_str": 12, "max_volume": "1000 L", "flags": ["CONTAINER", "SEALED", "BLOCKSDOOR", "MOUNTABLE", "FLAT_SURF"], "examine_action": "locked_object", "prying": {"result": "f_metal_crate_o", "message": "You wrench open the metal crate.", "byproducts": [{"item": "sheet_metal", "count": 1}], "prying_data": {"difficulty": 4, "prying_level": 1, "noisy": true, "failure": "The lid is sealed more tightly than you thought."}}, "bash": {"str_min": 30, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "sheet_metal", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [2, 10]}]}}, {"type": "furniture", "id": "f_metal_crate_o", "name": "open metal crate", "looks_like": "f_metal_crate_c", "description": "This large metal crate's lid is unsealed, and hinges open easily to reveal a number of storage shelves inside.  Once open, the side panels also swing wider for easy access.", "symbol": "X", "color": "light_gray", "move_cost_mod": 5, "coverage": 80, "required_str": 12, "max_volume": "1000 L", "flags": ["CONTAINER", "PLACE_ITEM", "NO_SIGHT", "HIDE_PLACE", "BLOCKSDOOR", "MOUNTABLE", "SMALL_HIDE"], "bash": {"str_min": 30, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 12]}, {"item": "pipe", "count": [0, 3]}, {"item": "sheet_metal", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [2, 10]}]}}, {"type": "furniture", "id": "f_chest", "description": "A sealed wooden storage container.  Lacking any labels, it could hold just about anything inside.  It has a simple metal latch.", "name": "wooden chest", "symbol": "X", "color": "white", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "looks_like": "f_crate_c", "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "PLACE_ITEM", "ORGANIC", "MOUNTABLE", "SHORT"], "deconstruct": {"items": [{"item": "wood_panel", "count": 5}, {"item": "hinge", "count": 2}, {"item": "nail", "charges": [6, 10]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "wham!", "items": [{"item": "2x4", "count": [1, 5]}, {"item": "hinge", "count": 2}, {"item": "nail", "charges": [2, 10]}]}}, {"type": "furniture", "id": "f_foot_locker", "name": "steel foot locker", "description": "A steel storage box, capable of holding any number of things.  The lid has a small lock.", "symbol": "O", "color": "light_gray", "move_cost_mod": 1, "coverage": 20, "required_str": 10, "looks_like": "f_crate_c", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE", "SHORT", "EASY_DECONSTRUCT"], "deployed_item": "foot_locker", "examine_action": "deployed_furniture", "max_volume": "68208 ml", "deconstruct": {"items": [{"item": "foot_locker", "count": 1}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "sheet_metal_small", "count": [3, 8]}, {"item": "lock", "count": [0, 1]}, {"item": "scrap", "charges": [3, 15]}, {"item": "hinge", "count": [0, 2]}, {"item": "nuts_bolts", "charges": [2, 8]}]}}, {"type": "furniture", "id": "f_foot_locker_aluminum", "name": "aluminum foot locker", "description": "An aluminum storage box, capable of holding any number of things.  The lid has a small lock.", "symbol": "O", "color": "white", "move_cost_mod": 1, "coverage": 20, "required_str": 10, "looks_like": "f_foot_locker", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE", "SHORT", "EASY_DECONSTRUCT"], "deployed_item": "foot_locker_aluminum", "examine_action": "deployed_furniture", "max_volume": "67669 ml", "deconstruct": {"items": [{"item": "foot_locker_aluminum", "count": 1}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "material_aluminium_ingot", "count": [3, 8]}, {"item": "lock", "count": [0, 1]}, {"item": "scrap_aluminum", "count": [3, 15]}, {"item": "hinge", "count": [0, 2]}, {"item": "nuts_bolts", "charges": [2, 8]}]}}, {"type": "furniture", "id": "f_432gal_drum_rubber", "name": "big collapsible drum", "looks_like": "f_standing_tank", "description": "A black collapsible drum with a valve and D-Rings.  Yellow text on its top clearly says \"FLAMMABLE\".", "symbol": "0", "bgcolor": "white", "move_cost_mod": -1, "coverage": 100, "required_str": -1, "flags": ["CONTAINER", "PLACE_ITEM", "LIQUIDCONT", "NOITEM", "SEALED", "EASY_DECONSTRUCT"], "examine_action": "keg", "keg_capacity": 6540, "deconstruct": {"items": [{"item": "432gal_drum_rubber", "count": 1}]}, "bash": {"str_min": 90, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "rubber_tire_chunk", "count": [40, 60]}, {"item": "scrap", "count": [50, 80]}, {"item": "water_faucet", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_pallet_belted", "name": "packed pallet", "description": "A wooden pallet loaded with cargo, wrapped with a watertight cover, and belted to secure the contents.", "symbol": "X", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 60, "required_str": 40, "max_volume": "1000 L", "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "ALLOW_FIELD_EFFECT", "FLAMMABLE", "PLACE_ITEM", "MOUNTABLE", "SHORT", "SMALL_HIDE"], "examine_action": "locked_object", "//": "probably should remove locked_object, because it mention you can pry it when actually you can't", "deconstruct": {"items": [{"item": "rope_6", "count": 6}, {"item": "plastic_sheet", "count": 5}, {"item": "pallet", "count": 1}]}, "bash": {"str_min": 9, "str_max": 50, "sound": "rip!", "sound_fail": "wham!", "furn_set": "f_pallet_belted_open", "items": [{"item": "rope_6", "count": [1, 2]}, {"item": "plastic_sheet", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_pallet_belted_open", "name": "damaged pallet", "description": "A wooden pallet, containing cargo and possessing a torn cover.  You can reach the contents, but it's awfully hard due to the belts still being in place.", "symbol": "O", "bgcolor": "brown", "move_cost_mod": -1, "coverage": 60, "required_str": 40, "max_volume": "1000 L", "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE", "PLACE_ITEM", "MOUNTABLE", "SHORT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "rope_6", "count": 4}, {"item": "plastic_sheet", "count": 3}, {"item": "pallet", "count": 1}]}, "bash": {"str_min": 9, "str_max": 50, "sound": "rip!", "sound_fail": "wham!", "items": [{"item": "rope_6", "count": [2, 4]}, {"item": "plastic_sheet", "count": [1, 3]}, {"item": "pallet", "count": 1}]}}]