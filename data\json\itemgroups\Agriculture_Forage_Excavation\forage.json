[{"id": "forage_spring", "type": "item_group", "subtype": "distribution", "entries": [{"item": "young_leaves", "prob": 50, "count": [1, 4]}, {"item": "plant_stalk", "prob": 20, "count": [1, 3]}, {"group": "any_wild_egg", "prob": 15, "count": [2, 5]}, {"item": "groundnut", "prob": 5, "count": [1, 8]}, {"item": "snail_garden", "prob": 10, "count": [1, 3]}, {"item": "wild_garlic", "prob": 5, "count": [2, 7]}, {"item": "rhubarb", "prob": 5, "count": [2, 5]}, {"item": "wild_herbs", "prob": 10, "count": 20}, {"item": "thyme", "prob": 5, "count": [1, 2]}, {"item": "garlic", "prob": 1}, {"item": "seed_garlic", "prob": 4, "count": [1, 2]}, {"item": "seed_canola", "prob": 5, "count": [1, 2]}, {"item": "dogbane", "prob": 5, "count": [1, 2]}, {"item": "carrot_wild", "prob": 5, "count": [3, 6]}, {"item": "bee_balm", "prob": 5, "count": [4, 8]}, {"item": "mugwort", "prob": 5, "count": [1, 2]}, {"item": "swarm_bees", "prob": 2}]}, {"id": "forage_summer", "type": "item_group", "subtype": "distribution", "entries": [{"item": "mayapple", "prob": 10, "count": [1, 3]}, {"item": "plant_stalk", "prob": 10, "count": [1, 3]}, {"item": "groundnut", "prob": 3, "count": [1, 6]}, {"group": "forage_mushroom", "prob": 25}, {"group": "any_wild_egg", "prob": 5, "count": [2, 5]}, {"group": "egg_reptile_forest", "prob": 5, "count": [2, 5]}, {"item": "snail_garden", "prob": 10, "count": [1, 3]}, {"item": "wild_herbs", "prob": 20, "count": 20}, {"item": "buckwheat", "prob": 5, "count": [2, 8]}, {"item": "thyme", "prob": 5, "count": [1, 3]}, {"item": "garlic", "prob": 1}, {"item": "seed_garlic", "prob": 4, "count": [1, 6]}, {"item": "seed_canola", "prob": 10, "count": [1, 3]}, {"item": "dogbane", "prob": 10, "count": [1, 3]}, {"item": "bee_balm", "prob": 10, "count": [1, 3]}, {"item": "mugwort", "prob": 10, "count": [1, 3]}, {"item": "salsify_raw", "prob": 5, "count": [1, 2]}]}, {"id": "forage_autumn", "type": "item_group", "subtype": "distribution", "entries": [{"item": "mayapple", "prob": 8, "count": [1, 2]}, {"item": "plant_stalk", "prob": 5, "count": [1, 3]}, {"item": "groundnut", "prob": 2, "count": [1, 9]}, {"group": "forage_mushroom", "prob": 50}, {"item": "wild_herbs", "prob": 10, "count": 20}, {"item": "buckwheat", "prob": 15, "count": [4, 8]}, {"item": "thyme", "prob": 5, "count": [1, 3]}, {"item": "garlic", "prob": 1}, {"item": "snail_garden", "prob": 10, "count": [1, 3]}, {"item": "seed_garlic", "prob": 4, "count": [1, 6]}, {"item": "seed_canola", "prob": 5, "count": [1, 3]}, {"item": "dogbane", "prob": 5, "count": [1, 3]}, {"item": "carrot_wild", "prob": 5, "count": [3, 6]}, {"item": "bee_balm", "prob": 5, "count": [1, 3]}, {"item": "mugwort", "prob": 5, "count": [1, 3]}, {"item": "salsify_raw", "prob": 5, "count": [1, 3]}]}, {"id": "forage_winter", "type": "item_group", "subtype": "distribution", "entries": [{"item": "wintergreen_berry", "prob": 2, "count": [1, 3]}]}, {"id": "forage_mushroom", "type": "item_group", "subtype": "distribution", "entries": [{"item": "mushroom", "prob": 4, "count": [1, 3]}, {"item": "mushroom_morel", "prob": 2, "count": [1, 4]}]}]