[{"type": "EXTERNAL_OPTION", "name": "DISPERSION_PER_GUN_DAMAGE", "info": "Value that adds to weapon dispersion per weapon damage value.", "stype": "int", "value": 30}, {"type": "EXTERNAL_OPTION", "name": "GUN_DISPERSION_DIVIDER", "info": "Value that divides total weapon dispersion.  15 means that weapons became 15 times more accurate than default.", "stype": "float", "value": 18}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_MAX_STAMINA_BASE", "info": "Sets the base max stamina value of the player, before cardio modifiers.", "stype": "int", "value": 3500}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_CARDIOFIT_STAMINA_SCALING", "info": "Sets the effect of cardio on maximum stamina.", "stype": "int", "value": 5}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_BASE_STAMINA_REGEN_RATE", "info": "Sets base stamina regeneration per turn of the player, before cardio modifiers.  May be used as an offset in stamina draining effects.", "stype": "float", "value": 20}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_BASE_STAMINA_BURN_RATE", "info": "Sets base stamina burn per 100 moves of the walking player.", "stype": "int", "value": 15}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_HUNGER_RATE", "info": "Set base hunger rate per 5 minutes.  Default: 1.0", "stype": "float", "value": 1.0}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_THIRST_RATE", "info": "Set base thirst rate per 5 minutes.  Default: 1.0", "stype": "float", "value": 1.0}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_SLEEPINESS_RATE", "info": "Set base sleepiness rate per 5 minutes.  Default: 1.0", "stype": "float", "value": 1.0}, {"type": "EXTERNAL_OPTION", "name": "PLAYER_HEALING_RATE", "info": "Set base player healing rate per turn.  Default: 0.0001", "stype": "float", "value": 0.0001}, {"type": "EXTERNAL_OPTION", "name": "NPC_HEALING_RATE", "info": "Set base NPC healing rate per turn.  Default: 0.0001", "stype": "float", "value": 0.0001}, {"type": "EXTERNAL_OPTION", "name": "NO_FAULTS", "info": "Disables vehicle part faults.  If true, disables vehicle part faults, vehicle parts will be totally reliable unless destroyed, and can only be repaired via replacement.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "NO_NPC_FOOD", "info": "Disables tracking food, thirst and ( partially ) fatigue for NPCs.  If true, NPCs won't need to eat or drink and will only get tired enough to sleep, not to get penalties.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "NO_VITAMINS", "info": "Disables tracking vitamins in food items.  If true, disables vitamin tracking and vitamin disorders.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "CBM_SLOTS_ENABLED", "info": "Enables CBM slots mechanics.  If true CBM slots are enabled.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "PAIN_PENALTY_MOD_STR", "info": "Scales how much pain decrease your stat.  0.005 means 0.5% of stat loss per one unit of pain.", "stype": "float", "value": 0.005}, {"type": "EXTERNAL_OPTION", "name": "PAIN_PENALTY_MOD_DEX", "info": "Scales how much pain decrease your stat.  0.0075 means 0.75% of stat loss per one unit of pain.", "stype": "float", "value": 0.0075}, {"type": "EXTERNAL_OPTION", "name": "PAIN_PENALTY_MOD_INT", "info": "Scales how much pain decrease your stat.  0.01 means 1% of stat loss per one unit of pain.", "stype": "float", "value": 0.01}, {"type": "EXTERNAL_OPTION", "name": "PAIN_PENALTY_MOD_PER", "info": "Scales how much pain decrease your stat.  0.01 means 1% of stat loss per one unit of pain.", "stype": "float", "value": 0.01}, {"type": "EXTERNAL_OPTION", "name": "SPAWN_CITY_HORDE_THRESHOLD", "info": "Minimum city size to guarantee extra zombies are spawned in cities.  0 means all cities spawn extra zombies.  Negative values disable extra city zombies.", "stype": "int", "value": 4}, {"type": "EXTERNAL_OPTION", "name": "SPAWN_CITY_HORDE_SMALL_CITY_CHANCE", "info": "Probability of a city smaller than SPAWN_HORDE_THRESHOLD having city zombies spawned, express in 'one in x' fashion .", "stype": "int", "value": 16}, {"type": "EXTERNAL_OPTION", "name": "SPAWN_CITY_HORDE_SPREAD", "info": "A scaling factor that determines how far from the center of cities extra zombies spawn, multiplied by city size, when city hordes are indicated.", "stype": "float", "value": 1.5}, {"type": "EXTERNAL_OPTION", "name": "SPAWN_CITY_HORDE_SCALAR", "info": "A scaling factor that determines how many zombies are spawned in cites, multiplied by city size, when city hordes are indicated.", "stype": "float", "value": 80.0}, {"type": "EXTERNAL_OPTION", "name": "SPAWN_ANIMAL_DENSITY", "info": "A scaling factor that determines density of wild, formerly domesticated and mutated animal spawns.", "stype": "float", "value": 1.0}, {"type": "EXTERNAL_OPTION", "name": "DISABLE_ANIMAL_CLASH", "info": "Disable additional spawn of large groups of monsters fighting each other on swamps.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "SPEEDYDEX_MIN_DEX", "info": "The minimum dex required for speedydex mod to add speed", "stype": "int", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "SPEEDYDEX_DEX_SPEED", "info": "The amount of moves gained per dex above SPEEDYDEX_MIN_DEX", "stype": "int", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "DISABLE_ROBOT_RESPONSE", "info": "Disables robot spawning from alerts and from being wanted.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "PORTAL_STORM_IGNORE_NPC", "info": "Portal storm enemies will ignore NPCs no matter what.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "CRAZY", "info": "A boolean specifically for Crazy Cataclysm.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "STATS_THROUGH_KILLS", "info": "Stats through Kills.  A mod that allows your stats to increase by killing zombies.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "WEARY_BMR_MULT", "info": "What portion of BMR is used as the base for determining weariness threshold.", "stype": "float", "value": 0.54}, {"type": "EXTERNAL_OPTION", "name": "WEARY_THRESH_SCALING", "info": "What the weariness threshold is multiplied by to gain each successive level.", "stype": "float", "value": 0.75}, {"type": "EXTERNAL_OPTION", "name": "WEARY_INITIAL_STEP", "info": "What the weariness threshold is multiplied by for the first level of weariness.", "stype": "float", "value": 1.0}, {"type": "EXTERNAL_OPTION", "name": "WEARY_RECOVERY_MULT", "info": "What percentage of calorie intake and expenditure is reduced during each reduction tick.", "stype": "float", "value": 0.05}, {"type": "EXTERNAL_OPTION", "name": "INT_BASED_LEARNING_BASE_VALUE", "info": "The amount of INT which is used as base point for focus adjustments", "stype": "int", "value": 8}, {"type": "EXTERNAL_OPTION", "name": "INT_BASED_LEARNING_FOCUS_ADJUSTMENT", "info": "The amount of focus gained per each INT point above INT_BASED_LEARNING_BASE_VALUE", "stype": "int", "value": 5}, {"type": "EXTERNAL_OPTION", "name": "GENERIC_PROFESSION_ID", "info": "The profession selected by default in the character creator menu.", "stype": "string_input", "value": "unemployed"}, {"type": "EXTERNAL_OPTION", "name": "GENERIC_SCENARIO_ID", "info": "The scenario selected by default in the character creator menu.", "stype": "string_input", "value": "evacuee"}, {"type": "EXTERNAL_OPTION", "name": "WORKBENCH_ALL_OPTIONS", "info": "If false, examining workbench-type furniture will auto-execute pickup and undeploy (where applicable) actions, no crafting-related options will be listed.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "SELECT_STARTING_CITY", "info": "Allows to select starting city in new character menu.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PREGENERATED_PATH", "info": "Path to pregenerated overmap files.  Should include mod folder name and folder where omap files are stored.", "stype": "string_input", "value": ""}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_POPULATE_OUTSIDE_CONNECTIONS_FROM_NEIGHBORS", "info": "Allows to populate outside connections from neighbors.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_RIVERS", "info": "Allows to place procgen rivers during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_LAKES", "info": "Allows to place procgen lakes during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_OCEANS", "info": "Allows to place procgen oceans during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_FORESTS", "info": "Allows to place procgen forests during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_FOREST_INCREASE_NORTH", "info": "Rate at which forest coverage of the map increases to the North, based on regional map settings thresholds.  0=no increase.", "stype": "float", "value": 0.04}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_FOREST_INCREASE_EAST", "info": "Rate at which forest coverage of the map increases to the East.  0=no increase.", "stype": "float", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_FOREST_INCREASE_WEST", "info": "Rate at which forest coverage of the map increases to the West.  0=no increase.", "stype": "float", "value": 0.02}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_FOREST_INCREASE_SOUTH", "info": "Rate at which forest coverage of the map increases to the South.  0=no increase.", "stype": "float", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_FOREST_LIMIT", "info": "Caps how high the forest threshold can rise.  No cities form at values over 0.4", "stype": "float", "value": 0.395}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_SWAMPS", "info": "Allows to place procgen swamps during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_RAVINES", "info": "Allows to place procgen ravines during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_CITIES", "info": "Allows to place cities during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_FOREST_TRAILS", "info": "Allows to place procgen forest trails during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_ROADS", "info": "Allows to place procgen roads during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_RAILROADS", "info": "Allows to place procgen railroads during overmap generation.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_RAILROADS_BEFORE_ROADS", "info": "Defines whether to place railroads before roads.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_SPECIALS", "info": "Allows to place overmap specials during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_PLACE_FOREST_TRAILHEADS", "info": "Allows to place procgen forest trailheads during overmap generation.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_URBAN_INCREASE_NORTH", "info": "Rate at which urbanity of the map increases to the North.  0=no increase.  Negative numbers are technically allowed but may cause weird effects.", "stype": "int", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_URBAN_INCREASE_EAST", "info": "Rate at which urbanity of the map increases to the East.  0=no increase.", "stype": "int", "value": 10}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_URBAN_INCREASE_WEST", "info": "Rate at which urbanity of the map increases to the South.  0=no increase.", "stype": "int", "value": 0}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_URBAN_INCREASE_SOUTH", "info": "Rate at which urbanity of the map increases to the West.  0=no increase.", "stype": "int", "value": 5}, {"type": "EXTERNAL_OPTION", "name": "OVERMAP_MAXIMUM_URBANITY", "info": "How urban do we allow our megacities to go?  Mostly functions as a multiple of map settings city size.", "stype": "int", "value": 8}, {"type": "EXTERNAL_OPTION", "name": "OVERRIDE_VEHICLE_INIT_STATE", "info": "If true, initial status and amount of fuel for all spawned vehicles will be overridden by values of VEHICLE_STATUS_AT_SPAWN and VEHICLE_FUEL_AT_SPAWN options.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "VEHICLE_STATUS_AT_SPAWN", "info": "All vehicles spawn in this status.  -1 is lightly damaged (default), 0 is undamaged, 1 is disabled (destroyed tires or engine).  Values are read only if OVERRIDE_VEHICLE_INIT_STATE is set to true.", "stype": "int", "value": -1}, {"type": "EXTERNAL_OPTION", "name": "VEHICLE_FUEL_AT_SPAWN", "info": "All vehicles spawn with this percentage of fuel.  0 is empty, 100 is fully loaded, -1 is random amount from 7% to 35% (default).  Values are read only if OVERRIDE_VEHICLE_INIT_STATE is set to true.", "stype": "int", "value": -1}, {"type": "EXTERNAL_OPTION", "name": "OUTSIDE_DEFINED_OMAP_OMT", "info": "If .omap s are being used this is the overmap terrain used outside of the defined overmaps.", "stype": "string_input", "value": "lake_surface"}, {"type": "EXTERNAL_OPTION", "name": "EMP_DISABLE_ELECTRONICS", "info": "Whether EMP effects will permanently disable items.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "CAPITALISM", "info": "NPCs will accept your bank balance as payment in trades.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "ZOMBIFY_INTO_ENABLED", "info": "Enables the functionality of the \"zombify_into\" JSON field in monster definitions.", "stype": "bool", "value": true}, {"type": "EXTERNAL_OPTION", "name": "DESCRIBE_MUSIC_FREQUENCY", "info": "Determines frequency (in minutes) of displaying music description in sidebar when listening to MP3-players and the like.", "stype": "int", "value": 5}, {"type": "EXTERNAL_OPTION", "name": "MIN_CATCHUP_EXP_PER_POST_CATA_DAY", "info": "NPCs generated post-cataclysm will, when initially generated, receive this amount of exp to a random skill for each day that has passed since the Cataclysm.", "stype": "int", "value": 50}, {"type": "EXTERNAL_OPTION", "name": "MAX_CATCHUP_EXP_PER_POST_CATA_DAY", "info": "Same as above.  This is the second value of rng(min, max) to determine base exp (before other character modifiers).", "stype": "int", "value": 150}, {"type": "EXTERNAL_OPTION", "name": "EXTRA_NPC_SKILL_LEVEL_CAP", "info": "The maximum skill level randomly generated NPCs can achieve via MIN_CATCHUP_EXP_PER_POST_CATA_DAY and MAX_CATCHUP_EXP_PER_POST_CATA_DAY.", "stype": "int", "value": 7}, {"type": "EXTERNAL_OPTION", "name": "ETERNAL_WEATHER", "info": "Sets eternal weather.  Possible values are 'normal', 'clear', 'cloudy', 'light_drizzle', 'drizzle', 'rain', 'rainstorm', 'thunder', 'lightning', 'flurries', 'snowing', 'snowstorm', 'early_portal_storm', 'portal_storm'.  'normal' clears all eternal weather overrides and sets normal weather pattern.  Requires restart of the game after changing value to take effect.", "stype": "string_input", "value": "normal"}, {"type": "EXTERNAL_OPTION", "name": "SHOW_MUTATION_SELECTOR", "info": "When mutating, displays a menu which allows players to pick from a list of possible mutations.", "stype": "bool", "value": false}, {"type": "EXTERNAL_OPTION", "name": "LATITUDE", "info": "Sets the latitude used for sunrise/sunset times.  Defaults to Boston.", "stype": "float", "value": 42.36}, {"type": "EXTERNAL_OPTION", "name": "LONGITUDE", "info": "Sets the longitude used for sunrise/sunset times.  Defaults to Boston.", "stype": "float", "value": -71.06}]