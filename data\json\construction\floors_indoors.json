[{"type": "construction", "id": "constr_carpet_conc_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["g_carpet", 1]]], "pre_terrain": "t_thconc_floor", "post_terrain": "t_carpet_concrete_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_conc_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["p_carpet", 1]]], "pre_terrain": "t_thconc_floor", "post_terrain": "t_carpet_concrete_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_conc_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["r_carpet", 1]]], "pre_terrain": "t_thconc_floor", "post_terrain": "t_carpet_concrete_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_conc_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["y_carpet", 1]]], "pre_terrain": "t_thconc_floor", "post_terrain": "t_carpet_concrete_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["g_carpet", 1]]], "pre_terrain": "t_floor", "post_terrain": "t_carpet_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_gray_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["g_carpet", 1]]], "pre_terrain": "t_linoleum_gray", "post_terrain": "t_carpet_linoleum_gray_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_gray_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["p_carpet", 1]]], "pre_terrain": "t_linoleum_gray", "post_terrain": "t_carpet_linoleum_gray_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_gray_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["r_carpet", 1]]], "pre_terrain": "t_linoleum_gray", "post_terrain": "t_carpet_linoleum_gray_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_gray_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["y_carpet", 1]]], "pre_terrain": "t_linoleum_gray", "post_terrain": "t_carpet_linoleum_gray_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_white_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["g_carpet", 1]]], "pre_terrain": "t_linoleum_white", "post_terrain": "t_carpet_linoleum_white_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_white_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["p_carpet", 1]]], "pre_terrain": "t_linoleum_white", "post_terrain": "t_carpet_linoleum_white_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_white_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["r_carpet", 1]]], "pre_terrain": "t_linoleum_white", "post_terrain": "t_carpet_linoleum_white_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_linoleum_white_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["y_carpet", 1]]], "pre_terrain": "t_linoleum_white", "post_terrain": "t_carpet_linoleum_white_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_metal_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["g_carpet", 1]]], "pre_terrain": "t_metal_floor", "post_terrain": "t_carpet_metal_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_metal_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["p_carpet", 1]]], "pre_terrain": "t_metal_floor", "post_terrain": "t_carpet_metal_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_metal_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["r_carpet", 1]]], "pre_terrain": "t_metal_floor", "post_terrain": "t_carpet_metal_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_metal_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["y_carpet", 1]]], "pre_terrain": "t_metal_floor", "post_terrain": "t_carpet_metal_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_primitive_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["g_carpet", 1]]], "pre_terrain": "t_floor_primitive", "post_terrain": "t_carpet_primitive_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_primitive_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["p_carpet", 1]]], "pre_terrain": "t_floor_primitive", "post_terrain": "t_carpet_primitive_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_primitive_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["r_carpet", 1]]], "pre_terrain": "t_floor_primitive", "post_terrain": "t_carpet_primitive_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_primitive_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["y_carpet", 1]]], "pre_terrain": "t_floor_primitive", "post_terrain": "t_carpet_primitive_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_rock_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["g_carpet", 1]]], "pre_terrain": "t_rock_floor", "post_terrain": "t_carpet_rock_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_rock_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["p_carpet", 1]]], "pre_terrain": "t_rock_floor", "post_terrain": "t_carpet_rock_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_rock_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["r_carpet", 1]]], "pre_terrain": "t_rock_floor", "post_terrain": "t_carpet_rock_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_rock_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["y_carpet", 1]]], "pre_terrain": "t_rock_floor", "post_terrain": "t_carpet_rock_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_strconc_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["g_carpet", 1]]], "pre_terrain": "t_strconc_floor", "post_terrain": "t_carpet_strconcrete_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["p_carpet", 1]]], "pre_terrain": "t_floor", "post_terrain": "t_carpet_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_strconc_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["p_carpet", 1]]], "pre_terrain": "t_strconc_floor", "post_terrain": "t_carpet_strconcrete_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_strconc_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["r_carpet", 1]]], "pre_terrain": "t_strconc_floor", "post_terrain": "t_carpet_strconcrete_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_strconc_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "components": [[["duct_tape", 5]], [["y_carpet", 1]]], "pre_terrain": "t_strconc_floor", "post_terrain": "t_carpet_strconcrete_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_wood_green", "group": "carpet_floor_green", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["g_carpet", 1]]], "pre_terrain": "t_floor_noroof", "post_terrain": "t_carpet_wood_green", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["r_carpet", 1]]], "pre_terrain": "t_floor", "post_terrain": "t_carpet_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_wood_purple", "group": "carpet_floor_purple", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["p_carpet", 1]]], "pre_terrain": "t_floor_noroof", "post_terrain": "t_carpet_wood_purple", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_wood_red", "group": "carpet_floor_red", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["r_carpet", 1]]], "pre_terrain": "t_floor_noroof", "post_terrain": "t_carpet_wood_red", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_wood_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["y_carpet", 1]]], "pre_terrain": "t_floor_noroof", "post_terrain": "t_carpet_wood_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_carpet_yellow", "group": "carpet_floor_yellow", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["nails", 5, "LIST"]], [["y_carpet", 1]]], "pre_terrain": "t_floor", "post_terrain": "t_carpet_yellow", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_dirtfloor", "group": "build_dirt_floor", "category": "CONSTRUCT", "required_skills": [["fabrication", 3]], "time": "15 m", "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "t_dirtfloor"}, {"type": "construction", "id": "constr_drystone_wall", "group": "build_dry_stone_wall", "//": "Step 2: the full wall", "category": "CONSTRUCT", "required_skills": [["fabrication", 3]], "time": "580 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["field_stone", 500]]], "pre_terrain": "t_drystone_wall_half", "post_terrain": "t_drystone_wall"}, {"type": "construction", "id": "constr_drystone_wall_half", "group": "build_dry_stone_wall", "//": "Step 1: half the wall", "category": "CONSTRUCT", "required_skills": [["fabrication", 1]], "time": "280 m", "qualities": [[{"id": "DIG", "level": 1}]], "components": [[["field_stone", 500]]], "pre_special": "check_empty", "post_terrain": "t_drystone_wall_half"}, {"type": "construction", "id": "constr_floor", "group": "build_floor_with_treated_roof", "//": "Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 3]], "time": "120 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_W", "level": 2}]], "components": [[["wood_sheet", 1], ["wood_panel", 2]], [["2x4", 6]], [["nails", 40, "LIST"]]], "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "t_floor"}, {"type": "construction", "id": "constr_floor_resin", "group": "extrude_resin_floor_and_roof", "//": "Obsolete group name.", "category": "CONSTRUCT", "skill": "fabrication", "difficulty": 2, "time": "90 m", "qualities": [{"id": "SMOOTH", "level": 1}], "components": [[["alien_pod_resin", 2]]], "pre_special": "check_empty", "post_terrain": "t_floor_resin"}, {"type": "construction", "id": "constr_floor_primitive", "group": "build_primitive_floor_with_log_sod_roof", "//": "Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 4]], "time": "60 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["log", 2]], [["stick", 4], ["2x4", 8], ["stick_long", 2]]], "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "t_floor_primitive"}, {"type": "construction", "id": "constr_floor_waxed", "group": "wax_floor", "category": "DECORATE", "required_skills": [["fabrication", 1]], "time": "10 m", "tools": [[["mop", -1], ["paint_brush", -1]], [["surface_heat", 2, "LIST"]]], "components": [[["wax", 5]]], "pre_terrain": "t_floor", "post_terrain": "t_floor_waxed"}, {"type": "construction", "id": "constr_jackhammer_resin_cage", "group": "jackhammer_resin_cage", "category": "DIG", "required_skills": [["fabrication", 0]], "time": "30 m", "tools": [[["jackhammer", 300], ["elec_jackhammer", 3960]]], "activity_level": "EXTRA_EXERCISE", "pre_terrain": "t_wall_resin_cage", "post_terrain": "t_floor_resin", "pre_note": "Your power tool is effective at cutting through the resin cage, but it's still a hard process.  Mi-go structures are dangerously hot -- you'll likely need to take breaks outside."}, {"type": "construction", "id": "constr_junk_floor", "group": "build_junk_metal_floor", "category": "CONSTRUCT", "required_skills": [["fabrication", 4]], "time": "120 m", "qualities": [[{"id": "SAW_M", "level": 1}], [{"id": "HAMMER", "level": 1}], [{"id": "WRENCH", "level": 2}], [{"id": "DRILL", "level": 1}]], "components": [[["lc_wire", 16], ["spike", 16], ["nails", 32, "LIST"]], [["sheet_metal_small", 20], ["scrap", 40]], [["sheet_metal", 4], ["steel_plate", 2], ["frame", 2], ["hdframe", 1], ["xlframe", 4], ["pipe", 12]]], "pre_special": "check_empty", "post_terrain": "t_junk_floor"}, {"type": "construction", "id": "constr_mine_resin_cage", "group": "mine_resin_cage", "category": "DIG", "required_skills": [["fabrication", 0]], "time": "70 m", "tools": [[["pickaxe_list", 1, "LIST"]]], "activity_level": "EXTRA_EXERCISE", "pre_terrain": "t_wall_resin_cage", "post_terrain": "t_floor_resin", "pre_note": "This is a slow, hard process that could be accelerated with power tools.  Mi-go structures are dangerously hot -- you'll likely need to take breaks outside."}, {"type": "construction", "id": "constr_ov_smreb_cage_thconc_floor", "group": "build_concrete_floor_with_roof", "//": "Step 1: rebar cage & supports", "category": "CONSTRUCT", "required_skills": [["fabrication", 5]], "time": "120 m", "using": [["welding_standard", 60]], "components": [[["rebar", 8]], [["2x4", 12]], [["pipe", 4]], [["pipe_fittings", 2]]], "pre_terrain": "t_pit_shallow", "post_terrain": "t_ov_smreb_cage"}, {"type": "construction", "id": "constr_ov_reb_cage_strconc_floor", "group": "build_reinforced_concrete_floor_with_roof", "//": "Step 1: rebar cage & supports. Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 6]], "time": "180 m", "using": [["welding_standard", 60]], "//1": "4cm of weld per rebar and 3cm per pipe", "components": [[["rebar", 12]], [["2x4", 12]], [["pipe", 4]]], "pre_terrain": "t_pit", "post_terrain": "t_ov_reb_cage"}, {"type": "construction", "id": "constr_palisade", "group": "build_palisade_wall", "category": "CONSTRUCT", "required_skills": [["fabrication", 4], ["survival", 2]], "time": "120 m", "qualities": [[{"id": "DIG", "level": 2}]], "components": [[["log", 3]], [["lc_wire", 6], ["wire_barbed", 4], ["rope_natural_short", 2, "LIST"], ["chain", 1], ["vine_30", 1]]], "pre_terrain": "t_pit", "post_terrain": "t_palisade"}, {"type": "construction", "id": "constr_ponywall", "group": "build_pony_wall", "//": "a thin half-wall used to divide rooms.", "category": "CONSTRUCT", "required_skills": [["fabrication", 2]], "time": "50 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_W", "level": 2}]], "components": [[["2x4", 5]], [["wood_panel", 1]], [["nails", 20, "LIST"]]], "pre_special": "check_empty", "post_terrain": "t_ponywall"}, {"type": "construction", "id": "constr_revert_floor_waxed", "group": "remove_wax_from_floor", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "10 m", "tools": [["chipper"]], "pre_terrain": "t_floor_waxed", "post_terrain": "t_floor"}, {"type": "construction", "id": "constr_revert_floor_waxed_y", "group": "remove_wax_from_floor", "category": "DECORATE", "required_skills": [["fabrication", 0]], "time": "10 m", "tools": [["chipper"]], "pre_terrain": "t_floor_waxed_y", "post_terrain": "t_floor"}, {"type": "construction", "id": "constr_rock_wall", "group": "build_stone_wall", "//": "Step 2: the full wall", "category": "CONSTRUCT", "required_skills": [["fabrication", 6], ["survival", 2]], "time": "180 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "DIG", "level": 2}]], "components": [[["rock", 12]], [["mortar_build", 1], ["mortar_lime", 1]]], "pre_terrain": "t_rock_wall_half", "post_terrain": "t_rock_wall"}, {"type": "construction", "id": "constr_rock_wall_half", "group": "build_stone_wall", "//": "Step 1: half the wall", "category": "CONSTRUCT", "required_skills": [["fabrication", 6], ["survival", 2]], "time": "180 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "DIG", "level": 2}]], "components": [[["rock", 12]], [["pebble", 50]], [["mortar_build", 1], ["mortar_lime", 1]]], "pre_special": "check_empty", "post_terrain": "t_rock_wall_half"}, {"type": "construction", "id": "constr_scrap_floor", "group": "build_scrap_floor_with_roof", "//": "Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 5]], "time": "90 m", "qualities": [{"id": "GLARE", "level": 1}], "tools": [[["oxy_torch", 10], ["welder", 50], ["welder_crude", 75], ["toolset", 75]]], "components": [[["steel_plate", 2]]], "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "t_scrap_floor"}, {"type": "construction", "id": "constr_strconc_floor", "group": "build_reinforced_concrete_floor_with_roof", "//": "Step 3: finish roof & floor. Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 6]], "time": "180 m", "tools": [[["concrete_mix_tool", 50]]], "qualities": [[{"id": "SMOOTH", "level": 2}]], "components": [[["concrete", 6]], [["water", 3], ["water_clean", 3]]], "pre_terrain": "t_strconc_floor_halfway", "post_terrain": "t_strconc_floor"}, {"type": "construction", "id": "constr_strconc_floor_halfway", "group": "build_reinforced_concrete_floor_with_roof", "//": "Step 2: start roof & floor. Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 6]], "time": "180 m", "tools": [[["concrete_mix_tool", 50]]], "qualities": [[{"id": "SMOOTH", "level": 2}]], "components": [[["concrete", 6]], [["water", 3], ["water_clean", 3]]], "pre_terrain": "t_ov_reb_cage", "post_terrain": "t_strconc_floor_halfway"}, {"type": "construction", "id": "constr_thconc_floor", "group": "build_concrete_floor_with_roof", "//": "Step 2: roof & floor. Obsolete group name.", "category": "CONSTRUCT", "required_skills": [["fabrication", 5]], "time": "120 m", "tools": [[["concrete_mix_tool", 50]]], "qualities": [[{"id": "SMOOTH", "level": 2}]], "components": [[["concrete", 4]], [["water", 4], ["water_clean", 4]]], "pre_terrain": "t_ov_smreb_cage", "post_terrain": "t_thconc_floor"}]