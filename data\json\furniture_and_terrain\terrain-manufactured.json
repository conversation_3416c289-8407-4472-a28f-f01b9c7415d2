[{"type": "terrain", "id": "t_recycler", "name": "metal compactor", "description": "A hydraulic machine for squeezing scrap into more compact chunks.  Useless now that there's no power.", "looks_like": "f_machinery_heavy", "symbol": "&", "color": "green", "move_cost": 0, "max_volume": "2000 L", "flags": ["TRANSPARENT", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_thconc_floor", "items": [{"item": "steel_lump", "count": [0, 2]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 12]}]}}, {"type": "terrain", "id": "t_gas_pump", "name": "gasoline pump", "looks_like": "f_machinery_heavy", "description": "Precious GASOLINE.  The former world bowed to their petroleum god as it led them to their ruin.  There's plenty left over to fuel your inner road warrior.  If this gas dispenser doesn't give up the goods for free, you may have to pay at a nearby terminal.", "symbol": "&", "color": "red", "move_cost": 0, "coverage": 65, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "REDUCE_SCENT", "PERMEABLE", "LIQUIDCONT"], "examine_action": "gaspump", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "clang!", "ter_set": "t_gas_pump_smashed", "items": [{"item": "scrap", "count": 1}]}}, {"type": "terrain", "id": "t_gas_pump_a", "name": "gasoline pump", "description": "Precious GASOLINE.  The former world bowed to their petroleum god as it led them to their ruin.  There's plenty left over to fuel your inner road warrior.  If this gas dispenser doesn't give up the goods for free, you may have to pay at a nearby terminal.", "//": "clone of t_gas_pump, but other color, must be clone every time", "looks_like": "t_gas_pump", "copy-from": "t_gas_pump", "symbol": "&", "color": "yellow_red"}, {"type": "terrain", "id": "t_gas_pump_smashed", "name": "smashed gas pump", "description": "The horror!  This gasoline pump has been destroyed, denying you access to the liquid gold.", "looks_like": "f_wreckage", "symbol": "&", "color": "light_red", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_pavement", "items": [{"item": "steel_lump", "prob": 50}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "pipe", "count": [4, 8]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 7]}, {"item": "hose", "count": 1}]}}, {"type": "terrain", "id": "t_diesel_pump", "name": "diesel pump", "description": "A diesel fuel pump.  This roadside attraction provides all the thick, gloopy liquid POWER you need to move your sensibly oversized APOCALYPTIC SUPERTRUCK from point A to points beyond.  If it doesn't dispense fuel immediately, try banging on it or grunt your way over the nearby payment terminal.", "symbol": "&", "color": "green", "looks_like": "t_gas_pump", "move_cost": 0, "coverage": 65, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "REDUCE_SCENT", "PERMEABLE", "LIQUIDCONT"], "examine_action": "gaspump", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "clang!", "ter_set": "t_diesel_pump_smashed", "items": [{"item": "scrap", "count": 1}]}}, {"type": "terrain", "id": "t_diesel_pump_a", "name": "diesel pump", "description": "A diesel fuel pump.  This roadside attraction provides all the thick, gloopy liquid POWER you need to move your sensibly oversized APOCALYPTIC SUPERTRUCK from point A to points beyond.  If it doesn't dispense fuel immediately, try banging on it or grunt your way over the nearby payment terminal.", "//": "clone of t_diesel_pump, but other color, must be clone every time", "symbol": "&", "color": "yellow_red", "looks_like": "t_gas_pump_a", "move_cost": 0, "coverage": 65, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "REDUCE_SCENT", "PERMEABLE", "LIQUIDCONT"], "examine_action": "gaspump", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "clang!", "ter_set": "t_diesel_pump_smashed", "items": [{"item": "scrap", "count": 1}]}}, {"type": "terrain", "id": "t_diesel_pump_smashed", "name": "smashed diesel pump", "description": "You're not getting any diesel out of this pump any time soon.  Some barbarian decided to take their frustration out on it.", "symbol": "&", "color": "light_green", "looks_like": "f_wreckage", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_pavement", "items": [{"item": "steel_lump", "prob": 50}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "pipe", "count": [4, 8]}, {"item": "scrap", "count": [3, 7]}, {"item": "hose", "count": 1}]}}, {"type": "terrain", "id": "t_jp8_pump", "name": "JP8 pump", "looks_like": "t_gas_pump", "description": "A pump that dispenses JP8.  If this gas dispenser doesn't give up the goods for free, you may have to pay at a nearby terminal.", "symbol": "&", "color": "red", "move_cost": 0, "coverage": 65, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "REDUCE_SCENT", "PERMEABLE", "LIQUIDCONT"], "examine_action": "gaspump", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "clang!", "ter_set": "t_jp8_pump_smashed", "items": [{"item": "scrap", "count": 1}]}}, {"type": "terrain", "id": "t_jp8_pump_smashed", "name": "smashed JP8 pump", "description": "You're not getting any JP8 out of this pump any time soon.  Some barbarian decided to take their frustration out on it.", "symbol": "&", "color": "light_green", "looks_like": "t_diesel_pump_smashed", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_pavement", "items": [{"item": "steel_lump", "prob": 50}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "pipe", "count": [4, 8]}, {"item": "scrap", "count": [3, 7]}, {"item": "hose", "count": 1}]}}, {"type": "terrain", "id": "t_avgas_pump", "name": "avgas pump", "looks_like": "t_gas_pump", "description": "A pump that dispenses avgas.  If this gas dispenser doesn't give up the goods for free, you may have to pay at a nearby terminal.", "symbol": "&", "color": "red", "move_cost": 0, "coverage": 65, "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "SEALED", "CONTAINER", "REDUCE_SCENT", "PERMEABLE", "LIQUIDCONT"], "examine_action": "gaspump", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "clang!", "ter_set": "t_avgas_pump_smashed", "items": [{"item": "scrap", "count": 1}]}}, {"type": "terrain", "id": "t_avgas_pump_smashed", "name": "smashed avgas pump", "description": "You're not getting any avgas out of this pump any time soon.  Some barbarian decided to take their frustration out on it.", "symbol": "&", "color": "light_green", "looks_like": "t_gas_pump_smashed", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 40, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_pavement", "items": [{"item": "steel_lump", "prob": 50}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "pipe", "count": [4, 8]}, {"item": "scrap", "count": [3, 7]}, {"item": "hose", "count": 1}]}}, {"type": "terrain", "id": "t_ticket_vendor", "name": "ticket vendor", "description": "A machine with a little screen and slots, intended to take your money and provide you with a ticket.", "symbol": "&", "looks_like": "f_atm", "color": "blue", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "ALARMED", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 40, "str_max": 210, "sound_fail": "clang!", "ter_set": "t_floor", "items": [{"item": "steel_chunk", "count": [1, 3]}, {"item": "scrap", "count": [4, 8]}]}}, {"type": "terrain", "id": "t_ticket_machine", "name": "ticket machine", "looks_like": "t_ticket_vendor", "description": "A machine that will provide you access to whatever lies beyond it - for the price of a ticket, of course.", "symbol": "&", "color": "blue", "move_cost": 0, "coverage": 55, "flags": ["TRANSPARENT", "NOITEM", "ALARMED", "REDUCE_SCENT", "PERMEABLE"], "bash": {"str_min": 40, "str_max": 210, "sound_fail": "clang!", "ter_set": "t_floor", "items": [{"item": "steel_chunk", "count": [1, 3]}, {"item": "scrap", "count": [4, 8]}]}}, {"type": "terrain", "id": "t_metal_ventilation_shutter", "name": "ventilation shutters", "description": "A large wall-mounted panel, covered with fragile metal sheets that regulate airflow.", "symbol": "=", "color": "light_gray", "looks_like": "t_chainfence", "move_cost": 0, "coverage": 90, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "WALL"], "bash": {"str_min": 6, "str_max": 20, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_metal_floor_no_roof", "items": [{"item": "scrap", "count": [8, 16]}]}, "deconstruct": {"ter_set": "t_metal_floor_no_roof", "items": [{"item": "scrap", "count": [8, 16]}]}}, {"type": "terrain", "id": "t_missile", "name": "missile", "description": "A section of an ICBM, an Intercontinental Ballistic Missile.  This isn't the kind of rocket that goes to the moon.", "symbol": "#", "color": "light_blue", "move_cost": 0, "flags": ["NOITEM"], "bash": {"str_min": 50, "str_max": 400, "explosive": 100, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_missile_exploded", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "plutonium", "count": [0, 3]}]}}, {"type": "terrain", "id": "t_missile_exploded", "name": "blown-out missile", "description": "A section of an ICBM, an Intercontinental Ballistic Missile.  This isn't the kind of rocket that's going anywhere.", "symbol": "#", "color": "light_gray", "move_cost": 0, "flags": ["NOITEM"], "bash": {"str_min": 20, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_metal_floor", "items": [{"item": "steel_lump", "count": [0, 2]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "scrap", "count": [4, 8]}]}}, {"type": "terrain", "id": "t_radio_tower", "name": "radio tower", "description": "The structure of a radio transmission tower.", "symbol": "&", "color": "light_gray", "move_cost": 0, "flags": ["TRANSPARENT", "NOITEM", "PERMEABLE", "MINEABLE"], "bash": {"str_min": 60, "str_max": 210, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_concrete", "items": [{"item": "steel_lump", "count": [1, 2]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 12]}]}}, {"type": "terrain", "id": "t_radio_controls", "name": "radio controls", "description": "A console that appears to control a nearby radio transmission tower.  It doesn't seem to be fully operational.", "symbol": "6", "color": "green", "looks_like": "f_console_broken", "move_cost": 0, "coverage": 50, "flags": ["TRANSPARENT", "NOITEM", "PERMEABLE"], "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "processor", "count": [1, 2]}, {"item": "RAM", "count": [4, 8]}, {"item": "cable", "charges": [4, 6]}, {"item": "large_lcd_screen", "count": 1}, {"item": "e_scrap", "count": [10, 16]}, {"item": "circuit", "count": [6, 10]}, {"item": "power_supply", "count": [2, 4]}, {"item": "amplifier", "count": [2, 4]}, {"item": "plastic_chunk", "count": [10, 12]}, {"item": "scrap", "count": [6, 8]}]}, "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_metal_floor", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "large_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "terrain", "id": "t_truss_pillar", "name": "truss pillar", "description": "A hollow metal pillar composed of interconnected beams that distribute loads efficiently while maintaining stability and strength.", "symbol": "H", "color": "light_gray", "looks_like": "t_radio_tower", "move_cost": 0, "flags": ["TRANSPARENT", "NOITEM", "PERMEABLE", "THIN_OBSTACLE"], "bash": {"str_min": 60, "str_max": 210, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_concrete", "items": [{"item": "steel_lump", "count": [1, 2]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 12]}]}}, {"type": "terrain", "id": "t_sewage_pipe", "name": "high gauge pipe", "description": "A section of high gauge pipe.", "symbol": "1", "color": "light_gray", "move_cost": 0, "coverage": 50, "flags": ["TRANSPARENT", "MOUNTABLE", "PERMEABLE", "THIN_OBSTACLE", "MINEABLE"], "bash": {"str_min": 30, "str_max": 210, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_sewage", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "steel_plate", "count": [0, 2]}]}}, {"type": "terrain", "id": "t_sewage_pump", "name": "high gauge pump", "description": "An unpowered pump that previously would have moved fluids around in a hurry.", "symbol": "&", "color": "light_gray", "looks_like": "f_machinery_heavy", "move_cost": 0, "coverage": 50, "flags": ["NOITEM", "REDUCE_SCENT", "MOUNTABLE"], "bash": {"str_min": 20, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_sewage", "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "pipe_fittings", "count": [0, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "terrain", "id": "t_drill_machinery", "name": "drilling machinery", "description": "All the machinery required to operate and control de drill string and the drill bit down below.  It is now inactive.", "symbol": "&", "color": "black", "looks_like": "f_machinery_heavy", "move_cost": 0, "coverage": 50, "flags": ["NOITEM", "WALL"], "bash": {"str_min": 20, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_open_air", "items": [{"item": "steel_plate", "count": [0, 4]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "terrain", "id": "t_drill_string", "name": "drill string", "description": "An extremely resistant length of mechanical tubing used to reach deep under water and drill into pockets of oil.", "symbol": "O", "color": "black", "looks_like": "t_little_column", "move_cost": 0, "coverage": 50, "flags": ["NOITEM", "THIN_OBSTACLE", "PERMEABLE"], "bash": {"str_min": 30, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_open_air", "items": [{"item": "steel_plate", "count": [0, 4]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "terrain", "id": "t_underwater_drill_string", "name": "drill string", "description": "An extremely resistant length of mechanical tubing used to reach deep under water and drill into pockets of oil.", "symbol": "O", "color": "blue", "looks_like": "t_little_column", "move_cost": 0, "coverage": 50, "flags": ["NOITEM", "THIN_OBSTACLE", "PERMEABLE"], "bash": {"str_min": 30, "str_max": 150, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_saltwater_cube", "items": [{"item": "steel_lump", "prob": 50}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "pipe_fittings", "count": [0, 4]}, {"item": "scrap", "count": [3, 7]}]}}, {"type": "terrain", "id": "t_cvdbody", "name": "CVD machine", "description": "The bulk of a highly technical-looking apparatus controlled by a nearby console.", "symbol": "%", "color": "dark_gray", "move_cost": 0, "coverage": 65, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 6, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_floor", "items": [{"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [1, 6], "prob": 50}, {"item": "scrap", "count": [2, 8], "prob": 50}]}}, {"type": "terrain", "id": "t_cvdmachine", "name": "CVD control panel", "description": "A VERY expensive-looking apparatus that's labeled 'Chemical Vapor Deposition Machine'.  With the input of certain exceptionally rare chemicals and elements, one could conceivably coat one's weapon with diamond.  While the process is extremely complicated, a previous user has helpfully sketched: Hydrogen + charcoal = smiley face.", "symbol": "&", "color": "cyan", "looks_like": "f_console", "move_cost": 0, "coverage": 50, "flags": ["TRANSPARENT", "NOITEM", "PERMEABLE"], "examine_action": "cvdmachine", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_metal_floor", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "small_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "terrain", "id": "t_nanofab_body", "name": "nanofabricator", "symbol": "%", "description": "A great column of advanced machinery.  Within this self-contained, miniaturized factory, several 3D printers work in tandem with a robotic assembler to manufacture nearly any inorganic object.", "color": "dark_gray", "move_cost": 0, "coverage": 65, "flags": ["PLACE_ITEM", "NANOFAB_TABLE"], "bash": {"str_min": 120, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_floor", "items": [{"item": "e_scrap", "count": [10, 14]}, {"item": "processor", "count": [10, 20]}, {"item": "RAM", "count": [14, 20]}, {"item": "nanomaterial", "count": 1, "prob": 10}, {"item": "bearing", "charges": [20, 60]}, {"item": "motor_small", "count": 4}, {"item": "power_supply", "count": [4, 16]}, {"item": "amplifier", "count": [4, 16]}, {"item": "cable", "charges": [250, 500]}, {"item": "circuit", "count": [1, 6]}, {"item": "scrap", "count": [12, 18]}]}}, {"type": "terrain", "id": "t_nanofab", "name": "nanofabricator control panel", "symbol": "&", "description": "A small computer panel attached to a nanofabricator.  It has a single slot for reading templates.", "allowed_template_ids": ["standard_template_construct", "standard_template_construct_combat_exoskeleton", "debug_template"], "color": "red", "looks_like": "f_console", "move_cost": 0, "coverage": 50, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["WALL", "NOITEM", "PERMEABLE"], "examine_action": "nanofab", "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_metal_floor", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "small_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "terrain", "id": "t_vat", "name": "cloning vat", "description": "A vat full of solution, probably intended to hold growing clones of people or other life forms.", "symbol": "0", "color": "light_cyan", "looks_like": "f_standing_tank", "move_cost": 0, "coverage": 40, "roof": "t_flat_roof", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "SEALED", "PLACE_ITEM", "WALL", "NO_SPOIL"], "bash": {"str_min": 2, "str_max": 80, "sound": "ker-rash!", "sound_fail": "plunk.", "sound_vol": 16, "sound_fail_vol": 12, "ter_set": "t_floor", "items": [{"item": "glass_shard", "count": [42, 84]}, {"item": "scrap", "count": [0, 2]}]}, "shoot": {"chance_to_hit": 50, "reduce_damage": [50, 70], "reduce_damage_laser": [2, 12], "destroy_damage": [5, 30]}}, {"type": "terrain", "id": "t_slot_machine", "name": "slot machine", "description": "A machine with a bright screen flashing hypnotic promises of wealth.  If gambling with your life on a daily basis isn't enough for you, you can also gamble with this.", "symbol": "6", "color": "green", "move_cost": 0, "coverage": 50, "roof": "t_flat_roof", "connect_groups": "INDOORFLOOR", "flags": ["NOITEM", "INDOORS"], "examine_action": {"type": "effect_on_condition", "effect_on_conditions": ["slot_machine_examine"]}, "bash": {"str_min": 8, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_metal_floor", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "small_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}, {"item": "coin_quarter", "count": [1, 1500]}]}}, {"type": "terrain", "id": "t_water_pump", "name": "water pump", "looks_like": "f_machinery_heavy", "description": "A deep well, collecting groundwater.  An installed pump allows you to draw water from it.", "symbol": "&", "color": "light_gray", "move_cost": 6, "coverage": 40, "flags": ["TRANSPARENT", "THIN_OBSTACLE"], "deconstruct": {"ter_set": "t_covered_well", "items": [{"item": "well_pump", "count": 1}, {"item": "pipe", "count": [1, 6]}]}, "bash": {"str_min": 15, "str_max": 80, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_covered_well", "items": [{"item": "steel_chunk", "count": [0, 2]}, {"item": "scrap", "count": [3, 6]}, {"item": "pipe_fittings", "count": [0, 1]}, {"item": "pipe", "count": [0, 2]}]}, "liquid_source": {"id": "water"}, "examine_action": "water_source"}, {"type": "terrain", "id": "t_wooden_well", "name": "wooden well", "looks_like": "t_covered_well", "description": "A deep well, collecting groundwater.  A wooden frame with a rope and bucket allows you to draw water from it, with some elbow grease.", "symbol": "#", "color": "c_brown", "move_cost": 6, "coverage": 40, "flags": ["TRANSPARENT", "THIN_OBSTACLE"], "deconstruct": {"ter_set": "t_covered_well", "items": [{"item": "rope_30", "count": 1}, {"item": "bucket_wood", "count": 1}, {"item": "stick", "count": 4}]}, "bash": {"str_min": 15, "str_max": 80, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_covered_well", "items": [{"item": "string_6", "count": [0, 18]}, {"item": "splinter", "count": [0, 24]}]}, "liquid_source": {"id": "water"}, "examine_action": "water_source"}, {"type": "terrain", "id": "t_salt_water_pump", "name": "salt water pump", "looks_like": "t_water_pump", "description": "A water pump connected to a pipe supplying it with salt water.  Not good to drink, but it has other uses.", "symbol": "&", "color": "light_gray", "move_cost": 6, "coverage": 40, "liquid_source": {"id": "salt_water", "min_temp": 2.2}, "flags": ["TRANSPARENT", "THIN_OBSTACLE"], "deconstruct": {"ter_set": "t_covered_well", "items": [{"item": "well_pump", "count": 1}, {"item": "pipe", "count": [1, 6]}]}, "bash": {"str_min": 15, "str_max": 80, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_dirt", "items": [{"item": "steel_chunk", "count": [0, 2]}, {"item": "scrap", "count": [3, 6]}, {"item": "pipe_fittings", "count": [0, 1]}, {"item": "pipe", "count": [0, 2]}]}, "examine_action": "water_source"}, {"type": "terrain", "id": "t_water_dispenser", "name": "water dispenser", "looks_like": "f_sink", "description": "A machine with several taps that dispenses clean water.", "symbol": "W", "color": "light_blue", "move_cost": 0, "coverage": 40, "flags": ["TRANSPARENT", "LIQUIDCONT"], "liquid_source": {"id": "water_clean", "count": [40, 240]}, "bash": {"str_min": 15, "str_max": 80, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_concrete", "items": [{"item": "steel_chunk", "count": [0, 2]}, {"item": "scrap", "count": [3, 6]}, {"item": "pipe_fittings", "count": [0, 1]}, {"item": "pipe", "count": [0, 2]}]}, "examine_action": "finite_water_source"}, {"type": "terrain", "id": "t_sai_box", "name": "telecom cabinet", "description": "A cabinet full of telecom equipment.  With the lines down, you might be able to take it apart for its useful electronics.", "symbol": "#", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 90, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "WALL"], "bash": {"str_min": 8, "str_max": 80, "sound": "whack!", "sound_fail": "clang!", "ter_set": "t_sai_box_damaged"}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "RAM", "count": [4, 8]}, {"item": "cable", "charges": [16, 40]}, {"item": "small_lcd_screen", "count": [2, 4]}, {"item": "e_scrap", "count": [12, 24]}, {"item": "circuit", "count": [6, 30]}, {"item": "power_supply", "count": [4, 8]}, {"item": "amplifier", "count": [3, 6]}, {"item": "plastic_chunk", "count": [4, 8]}, {"item": "scrap", "count": [8, 16]}]}}, {"type": "terrain", "id": "t_sai_box_damaged", "name": "damaged telecom cabinet", "description": "A damaged telecom cabinet.  You might still be able to salvage some useful electronic scrap from it.", "symbol": "#", "color": "light_gray", "looks_like": "f_wreckage", "move_cost": 0, "coverage": 90, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "WALL"], "bash": {"str_min": 6, "str_max": 80, "sound": "whack!", "sound_fail": "clang!", "ter_set": "t_concrete", "items": [{"item": "cable", "charges": [4, 8], "prob": 80}, {"item": "e_scrap", "count": [2, 8], "prob": 60}, {"item": "circuit", "count": [1, 6], "prob": 50}, {"item": "amplifier", "count": [1, 4], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "RAM", "count": [1, 2]}, {"item": "cable", "charges": [4, 24]}, {"item": "e_scrap", "count": [4, 12]}, {"item": "circuit", "count": [2, 12]}, {"item": "power_supply", "count": [1, 4]}, {"item": "amplifier", "count": [1, 3]}, {"item": "plastic_chunk", "count": [2, 6]}, {"item": "scrap", "count": [6, 12]}]}}, {"type": "terrain", "id": "t_oil_circ_brkr_l", "name": "HV oil circuit breaker", "description": "A circuit breaker that uses oil in its arc suppression chamber.", "symbol": "B", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 90, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 6, "sound": "pow!", "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 16]}, {"item": "steel_chunk", "count": [2, 6]}, {"item": "ceramic_shard", "count": [0, 4]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "cable", "charges": [8, 24]}, {"item": "power_supply", "count": [4, 8]}, {"item": "amplifier", "count": [8, 16]}, {"item": "steel_chunk", "count": [4, 16]}, {"item": "scrap", "count": [12, 24]}, {"item": "sheet_metal", "count": [6, 12]}, {"item": "ceramic_shard", "count": [2, 6]}]}}, {"type": "terrain", "id": "t_oil_circ_brkr_s", "name": "small HV oil circuit breaker", "description": "A small circuit breaker that uses oil in its arc suppression chamber.", "symbol": "b", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 65, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "FLAMMABLE", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 4, "sound": "pow!", "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [6, 12]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "ceramic_shard", "count": [0, 2]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "cable", "charges": [4, 12]}, {"item": "power_supply", "count": [3, 6]}, {"item": "amplifier", "count": [6, 12]}, {"item": "steel_chunk", "count": [2, 12]}, {"item": "scrap", "count": [8, 18]}, {"item": "sheet_metal", "count": [4, 8]}, {"item": "ceramic_shard", "count": [1, 4]}]}}, {"type": "terrain", "id": "t_switchgear_l", "name": "large switchgear", "description": "A switchgear panel.  It's covered in breaker switches, fuses, and gauges.", "symbol": "H", "color": "i_light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 90, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 3, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [5, 10]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "plastic_chunk", "count": [2, 4]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "RAM", "count": [2, 6]}, {"item": "cable", "charges": [4, 24]}, {"item": "small_lcd_screen", "count": [6, 12]}, {"item": "e_scrap", "count": [16, 24]}, {"item": "circuit", "count": [12, 30]}, {"item": "power_supply", "count": [6, 8]}, {"item": "amplifier", "count": [6, 8]}, {"item": "plastic_chunk", "count": [2, 4]}, {"item": "scrap", "count": [8, 16]}, {"item": "sheet_metal", "count": [2, 4]}]}}, {"type": "terrain", "id": "t_switchgear_s", "name": "small switchgear", "description": "A small switchgear panel.  It's covered in breaker switches, fuses, and gauges.", "symbol": "L", "color": "i_light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 65, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 2, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "steel_chunk", "count": [1, 2]}, {"item": "e_scrap", "count": [0, 2]}, {"item": "circuit", "count": [1, 4]}, {"item": "power_supply", "count": [0, 2]}, {"item": "amplifier", "prob": 50}, {"item": "plastic_chunk", "count": [1, 2]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "RAM", "count": [1, 2]}, {"item": "cable", "charges": [2, 8]}, {"item": "small_lcd_screen", "count": [2, 6]}, {"item": "e_scrap", "count": [6, 12]}, {"item": "circuit", "count": [8, 24]}, {"item": "power_supply", "count": [2, 6]}, {"item": "amplifier", "count": [1, 4]}, {"item": "plastic_chunk", "count": [1, 2]}, {"item": "scrap", "count": [4, 8]}, {"item": "sheet_metal", "count": [1, 2]}]}}, {"type": "terrain", "id": "t_lgtn_arrest", "name": "lightning arrester", "description": "A component designed to protect insulation and conductors in an electrical system by directing lightning through itself and into the ground.", "symbol": "}", "color": "i_light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 4, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "cable", "charges": [0, 4]}, {"item": "scrap", "count": [8, 12]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "ceramic_shard", "count": [8, 16]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "cable", "charges": [4, 8]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "scrap", "count": [12, 16]}, {"item": "plastic_chunk", "count": [1, 4]}, {"item": "ceramic_shard", "count": [12, 24]}]}}, {"type": "terrain", "id": "t_station_disc", "name": "disconnect switch", "description": "A switch used to make sure an electrical system doesn't have any current flowing through it, for maintenance periods.", "symbol": "h", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 3, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "steel_chunk", "count": [2, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "ceramic_shard", "count": [0, 2]}, {"item": "lead", "prob": 50}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "RAM", "count": [12, 24]}, {"item": "cable", "charges": [6, 12]}, {"item": "small_lcd_screen", "count": [8, 16]}, {"item": "e_scrap", "count": [8, 12]}, {"item": "circuit", "count": [6, 18]}, {"item": "power_supply", "count": [8, 12]}, {"item": "amplifier", "count": [2, 4]}, {"item": "plastic_chunk", "count": [4, 8]}, {"item": "scrap", "count": [2, 6]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "lead", "charges": [1, 2]}, {"item": "ceramic_shard", "count": [2, 6]}]}}, {"type": "terrain", "id": "t_current_trans", "name": "current transformer", "description": "An electronic component used to transform the voltage of a current.", "symbol": "{", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 50, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 5, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [10, 12]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "lead", "charges": [2, 8]}, {"item": "cable", "charges": [20, 60]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "ceramic_shard", "count": [2, 6]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "scrap", "count": [12, 16]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "lead", "charges": [4, 16]}, {"item": "cable", "charges": [60, 120]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "ceramic_shard", "count": [4, 12]}]}}, {"type": "terrain", "id": "t_potential_trans", "name": "potential transformer", "description": "A specialized type of electrical transformer.", "symbol": "8", "color": "i_light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 50, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 5, "ter_set": "t_concrete", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [10, 12]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "lead", "charges": [8, 16]}, {"item": "cable", "charges": [1, 20]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "ceramic_shard", "count": [2, 6]}]}, "deconstruct": {"ter_set": "t_concrete", "items": [{"item": "scrap", "count": [12, 16]}, {"item": "steel_chunk", "count": [4, 6]}, {"item": "lead", "charges": [12, 32]}, {"item": "cable", "charges": [20, 40]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "ceramic_shard", "count": [4, 12]}]}}, {"type": "terrain", "id": "t_conveyor", "name": "conveyor belt", "description": "A conveyor belt.  Used to transport things.", "symbol": "=", "color": "brown", "move_cost": 6, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "PLACE_ITEM", "INDOORS"], "coverage": 30, "deconstruct": {"ter_set": "t_rock_floor", "items": [{"item": "lc_wire", "count": 1}, {"item": "pipe", "count": [1, 4]}, {"item": "chain", "prob": 10}, {"item": "scrap", "count": [1, 5]}, {"item": "xlframe", "prob": 5}]}, "bash": {"str_min": 12, "str_max": 80, "sound": "clang!", "sound_fail": "ting.", "ter_set": "t_rock_floor", "items": [{"item": "pipe", "count": [1, 3]}, {"item": "chain", "prob": 10}, {"item": "scrap", "count": [1, 5]}]}}, {"id": "t_milking_machine", "type": "terrain", "name": "milking machine", "description": "A machine used in the dairy industry to milk cows.", "symbol": "%", "color": "light_gray", "move_cost": 2, "bash": {"str_min": 10, "str_max": 50, "ter_set": "t_floor", "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "scrap", "count": [1, 6]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "hose", "count": [1, 3]}]}, "deconstruct": {"ter_set": "t_floor", "items": [{"item": "scrap", "count": [2, 9]}, {"item": "steel_chunk", "count": [2, 5]}, {"item": "pipe_fittings", "count": [1, 2]}, {"item": "hose", "count": [2, 6]}]}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR"]}, {"id": "t_bulk_tank", "type": "terrain", "name": "bulk tank", "description": "A heavy, high capacity tank.", "symbol": "O", "color": "light_gray", "looks_like": "f_standing_tank", "move_cost": 0, "coverage": 85, "bash": {"str_min": 20, "str_max": 80, "ter_set": "t_floor", "sound": "crunch!", "sound_fail": "whump!", "items": [{"item": "pipe", "count": [1, 2]}, {"item": "steel_lump", "count": [1, 5]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "steel_chunk", "count": [0, 2]}]}, "deconstruct": {"ter_set": "t_floor", "items": [{"item": "55gal_drum"}, {"item": "water_faucet"}]}, "connect_groups": "WALL", "connects_to": "WALL", "flags": ["CONTAINER", "LIQUIDCONT", "NOITEM", "WALL"]}, {"id": "t_rockyobstacle", "type": "terrain", "name": "rocky obstacle", "description": "Some larger rocks arranged and crudely rammed into the ground to slow down who or whatever is trying to cross.", "symbol": "^", "color": "light_gray", "looks_like": "t_railroad_rubble", "move_cost": 4, "bash": {"str_min": 20, "str_max": 30, "ter_set": "t_dirt", "sound_fail": "whump!", "items": [{"item": "rock_large", "count": [0, 20]}, {"item": "sharp_rock", "count": [0, 16]}]}, "deconstruct": {"ter_set": "t_dirt", "items": [{"item": "rock_large", "count": 22}]}, "flags": ["TRANSPARENT", "THIN_OBSTACLE", "SHORT", "ROUGH", "UNSTABLE", "PERMEABLE", "EASY_DECONSTRUCT"]}, {"type": "terrain", "id": "t_rootcellar", "name": "root cellar", "symbol": "=", "description": "A cellar dug into the earth for storing food in a cool environment.", "color": "green", "looks_like": "t_pit", "move_cost": 0, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "INDOORS"], "deconstruct": {"ter_set": "t_pit", "items": [{"item": "rock", "count": 40}, {"item": "stick", "count": [16, 16]}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "whump!", "ter_set": "t_pit_shallow", "items": [{"item": "rock", "count": 8}, {"item": "stick", "count": [8, 12]}]}}, {"type": "terrain", "id": "t_covered_well", "name": "covered well", "description": "A deep well, collecting groundwater.  You won't be able to get any water unless you add some mechanism for drawing it up to the surface.", "symbol": "#", "color": "dark_gray", "move_cost": 2, "coverage": 40, "flags": ["TRANSPARENT", "FLAT", "MINEABLE"], "bash": {"str_min": 80, "str_max": 300, "sound": "crash!", "sound_fail": "whump!", "ter_set": "t_pit", "items": [{"item": "rock", "count": [8, 18]}, {"item": "2x4", "count": [0, 2]}, {"item": "nail", "charges": [1, 4]}, {"item": "splinter", "count": [1, 2]}]}}, {"type": "terrain", "id": "t_covered_salt_water_well", "name": "covered salt water well", "description": "A deep well, collecting salt water from a surrounding swamp.  You won't be able to get any water unless you add some mechanism for drawing it up to the surface.", "symbol": "#", "color": "dark_gray", "looks_like": "t_covered_well", "move_cost": 2, "coverage": 40, "flags": ["TRANSPARENT", "FLAT", "MINEABLE"], "bash": {"str_min": 80, "str_max": 300, "sound": "crash!", "sound_fail": "whump!", "ter_set": "t_pit", "items": [{"item": "rock", "count": [8, 18]}, {"item": "2x4", "count": [0, 2]}, {"item": "nail", "charges": [1, 4]}, {"item": "splinter", "count": [1, 2]}]}}, {"type": "terrain", "id": "t_pit_straw", "name": "pit filled with straw", "symbol": "#", "description": "A shallow pit filled with some straw.  Very crude.  Itchy and uncomfortable, but better than the bare ground.", "color": "yellow", "move_cost": 2, "coverage": 35, "comfort": 1, "floor_bedding_warmth": 100, "deconstruct": {"items": [{"item": "straw_pile", "count": [55, 60]}], "ter_set": "t_pit_shallow"}, "flags": ["TRANSPARENT", "FLAMMABLE", "ORGANIC", "MOUNTABLE", "SHORT", "EASY_DECONSTRUCT"], "bash": {"str_min": 6, "str_max": 20, "sound": "crunch!", "sound_fail": "brush.", "ter_set": "t_pit_shallow", "items": [{"item": "straw_pile", "count": [30, 40]}]}}, {"type": "terrain", "id": "t_improvised_shelter", "name": "improvised shelter", "description": "An improvised shelter providing a little bit of protection, that can be used to take refuge from the elements or to protect a campfire from the rain.", "symbol": "#", "color": "brown_green", "looks_like": "t_leanto", "move_cost": 2, "coverage": 30, "floor_bedding_warmth": 1000, "comfort": 3, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE_ASH", "THIN_OBSTACLE", "REDUCE_SCENT", "INDOORS", "MOUNTABLE", "HIDE_PLACE", "EASY_DECONSTRUCT"], "deconstruct": {"ter_set": "t_pit_shallow", "items": [{"item": "stick", "count": 12}, {"item": "pine_bough", "count": 24}]}, "bash": {"str_min": 4, "str_max": 60, "sound": "crunch.", "sound_fail": "brush.", "ter_set": "t_pit_shallow", "items": [{"item": "stick", "count": [3, 6]}, {"item": "pine_bough", "count": [6, 18]}]}}, {"type": "terrain", "id": "t_improvised_shelter_filled", "name": "improvised shelter", "description": "An improvised shelter which has been made a bit more comfortable by filling it with dry plant matter, which should provide some more insulation.  Trying to contain a fire here would be a bad idea.", "symbol": "#", "color": "brown_green", "looks_like": "t_leanto", "floor_bedding_warmth": 1200, "comfort": 4, "move_cost": 2, "coverage": 30, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "CONTAINER", "THIN_OBSTACLE", "REDUCE_SCENT", "INDOORS", "MOUNTABLE", "HIDE_PLACE", "EASY_DECONSTRUCT"], "deconstruct": {"ter_set": "t_pit_shallow", "items": [{"item": "stick", "count": 12}, {"item": "pine_bough", "count": 24}, {"item": "withered", "count": 80}]}, "bash": {"str_min": 4, "str_max": 60, "sound": "crunch.", "sound_fail": "brush.", "ter_set": "t_pit_shallow", "items": [{"item": "stick", "count": [3, 6]}, {"item": "pine_bough", "count": [6, 18]}, {"item": "withered", "count": [40, 60]}]}}, {"id": "t_leanto", "type": "terrain", "name": "pine lean-to", "description": "A small shelter, roofed with pine branches, that can be used to take refuge from the elements or to protect a campfire from the rain.", "symbol": ";", "color": "brown", "move_cost": 2, "bash": {"str_min": 4, "str_max": 60, "ter_set": "t_tree_pine", "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick", "count": [2, 7]}, {"item": "splinter", "count": [8, 20]}, {"item": "pine_bough", "count": [0, 2]}]}, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "CONTAINER", "FLAMMABLE_ASH", "THIN_OBSTACLE", "REDUCE_SCENT", "INDOORS", "MOUNTABLE"]}, {"id": "t_tarptent", "type": "terrain", "name": "tarp lean-to", "description": "A small shelter, covered by a waterproof tarp, that can be used to take refuge from the elements or to protect a campfire from the rain.", "symbol": ";", "color": "light_blue", "looks_like": "t_leanto", "move_cost": 2, "bash": {"str_min": 6, "str_max": 12, "ter_set": "t_dirt", "sound": "crash!", "sound_fail": "whack!", "items": [{"item": "stick", "count": [1, 2]}, {"item": "splinter", "count": [1, 4]}, {"item": "plastic_chunk", "count": [4, 8]}]}, "deconstruct": {"ter_set": "t_dirt", "items": [{"item": "pointy_stick", "count": 4}, {"item": "string_6", "count": 4}, {"item": "tarp", "count": 1}]}, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "FLAMMABLE", "THIN_OBSTACLE", "INDOORS", "MOUNTABLE", "EASY_DECONSTRUCT"]}, {"type": "terrain", "id": "t_brick_oven_base", "name": "brick oven base", "symbol": "=", "description": "A flat assembly of carefully stacked bricks that is not quite an oven.", "color": "yellow", "move_cost": 4, "deconstruct": {"ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": 30}]}, "bash": {"str_min": 16, "str_max": 25, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": [15, 20]}]}}, {"type": "terrain", "id": "t_brick_oven_struct", "looks_like": "f_fireplace", "name": "exposed brick oven", "symbol": "#", "description": "A large brick oven that could make several pizzas at once.  This one seems to be missing its door, and cannot be used in such a state.", "color": "yellow", "move_cost": 0, "flags": ["REDUCE_SCENT", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PERMEABLE"], "deconstruct": {"ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": 90}]}, "bash": {"str_min": 25, "str_max": 50, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": [45, 75]}]}}, {"type": "terrain", "id": "t_brick_oven", "looks_like": "f_fireplace", "name": "brick oven", "symbol": "#", "description": "A large brick oven that could make several pizzas at once.", "color": "red", "move_cost": 0, "flags": ["REDUCE_SCENT", "NOITEM", "PERMEABLE", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE"], "deconstruct": {"ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": 90}]}, "bash": {"str_min": 25, "str_max": 50, "sound": "whack!", "sound_fail": "thunk.", "ter_set": "t_dirt", "items": [{"item": "fire_brick", "count": [45, 75]}]}}]