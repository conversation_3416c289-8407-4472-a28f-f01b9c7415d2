name: "Experimental Release"
concurrency: release
on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/release.yml'
      - 'android/**'
      - 'build-data/**'
      - 'build-scripts/generate-release-notes.js'
      - 'cataclysm-launcher'
      - 'data/**'
      - 'doc/**'
      - 'gfx/**'
      - 'lang/po/*.po'
      - 'LICENSE*'
      - 'Makefile'
      - 'README*'
      - 'src/**'

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  REPOSITORY_NAME: ${{ github.event.repository.name }}

jobs:
  release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions: write-all
    outputs:
      timestamp: ${{ steps.get-timestamp.outputs.time }}
    steps:
      - name: Get build timestamp
        id: get-timestamp
        run: |
          echo "time=$(/bin/date -u "+%Y-%m-%d-%H%M")" >> $GITHUB_OUTPUT
      - name: Generate environmental variables
        id: generate_env_vars
        run: |
          echo "tag_name=cdda-experimental-${{ steps.get-timestamp.outputs.time }}" >> $GITHUB_OUTPUT
          echo "release_name=Cataclysm-DDA experimental build ${{ steps.get-timestamp.outputs.time }}" >> $GITHUB_OUTPUT
      - uses: actions/checkout@v4
      - name: Generate Release Notes
        id: generate-release-notes
        run: |
          npm install @actions/github
          node build-scripts/generate-release-notes.js '${{ secrets.GITHUB_TOKEN }}' '${{ steps.generate_env_vars.outputs.tag_name }}' "$(git log -1 --format='%H')" > notes.txt
      - run: |
          gh release create ${{ steps.generate_env_vars.outputs.tag_name }} --notes-file notes.txt --prerelease --title "${{ steps.generate_env_vars.outputs.release_name }}" --target "$(git log -1 --format='%H')"

  builds:
    needs: release
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Windows Tiles x64 MSVC
            artifact: windows-tiles-x64-msvc
            arch: x64
            os: windows-2019
            mxe: none
            ext: zip
            content: application/zip
            sound: 0
          - name: Windows Tiles Sounds x64 MSVC
            artifact: windows-tiles-sounds-x64-msvc
            arch: x64
            os: windows-2019
            mxe: none
            ext: zip
            content: application/zip
            sound: 1
          - name: Windows Tiles x64
            mxe: x86_64
            libbacktrace: 1
            artifact: windows-tiles-x64
            os: ubuntu-latest
            ext: zip
            content: application/zip
            sound: 0
          - name: Windows Tiles Sounds x64
            mxe: x86_64
            libbacktrace: 1
            artifact: windows-tiles-sounds-x64
            os: ubuntu-latest
            ext: zip
            content: application/zip
            sound: 1
          - name: Linux Tiles x64
            os: ubuntu-20.04
            mxe: none
            android: none
            libbacktrace: 1
            tiles: 1
            sound: 0
            artifact: linux-tiles-x64
            ext: tar.gz
            content: application/gzip
          - name: Linux Tiles Sounds x64
            os: ubuntu-20.04
            mxe: none
            android: none
            libbacktrace: 1
            tiles: 1
            sound: 1
            artifact: linux-tiles-sounds-x64
            ext: tar.gz
            content: application/gzip
          - name: Linux Curses x64
            os: ubuntu-20.04
            mxe: none
            android: none
            libbacktrace: 1
            tiles: 0
            sound: 0
            artifact: linux-curses-x64
            ext: tar.gz
            content: application/gzip
          - name: macOS Curses Universal Binary (x64 and arm64)
            os: macos-12
            mxe: none
            tiles: 0
            sound: 0
            artifact: osx-curses-universal
            ext: dmg
            content: application/x-apple-diskimage
          - name: macOS Tiles Universal Binary (x64 and arm64)
            os: macos-12
            mxe: none
            tiles: 1
            sound: 0
            artifact: osx-tiles-universal
            ext: dmg
            content: application/x-apple-diskimage
          - name: Android x64
            os: ubuntu-latest
            mxe: none
            android: arm64
            artifact: android-x64
            ext: apk
            content: application/apk
          - name: Android x32
            os: ubuntu-latest
            mxe: none
            android: arm32
            artifact: android-x32
            ext: apk
            content: application/apk 
          - name: Android Bundle
            os: ubuntu-latest
            mxe: none
            android: bundle
            artifact: android-bundle
            ext: aab
            content: application/aap
          - name: WebAssembly Bundle
            os: ubuntu-latest
            mxe: none
            artifact: wasm
            ext: zip
            content: application/zip
            sound: 0
            wasm: true
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    permissions: write-all
    env:
        ZSTD_CLEVEL: 17
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-tags: true
      - name: Get soundpack
        if: matrix.sound == 1
        run: |
          git clone --depth=1 --shallow-submodules --recurse-submodules https://github.com/Fris0uman/CDDA-Soundpacks '${{ github.workspace }}/CDDA-Soundpacks'
          mv '${{ github.workspace }}/CDDA-Soundpacks/sound/CC-Sounds' '${{ github.workspace }}/data/sound'
      - name: Install dependencies (windows msvc) (0/4)
        if: runner.os == 'Windows'
        uses: lukka/get-cmake@latest
      - name: Install dependencies (windows msvc) (1/4)
        if: runner.os == 'Windows'
        uses: microsoft/setup-msbuild@v2
      - name: Install dependencies (windows msvc) (2/4)
        if: runner.os == 'Windows'
        uses: lukka/run-vcpkg@v11
        id: runvcpkg
        with:
          vcpkgDirectory: '${{ runner.workspace }}/b/vcpkg'
          vcpkgGitCommitId: '66444e13a86da7087ee24c342f91801cc6eb9877'
      - name: Install dependencies (windows msvc) (3/4)
        if: runner.os == 'Windows'
        run: |
          vcpkg integrate install --vcpkg-root '${{ runner.workspace }}\b\vcpkg'
      - name: Install dependencies (windows msvc) (4/4)
        if: runner.os == 'Windows'
        uses: msys2/setup-msys2@v2
        with:
          msystem: mingw64
          install: >-
            gettext
            make
      - name: Install dependencies (windows mxe)
        if: matrix.mxe != 'none'
        run: |
          sudo apt install gettext
      - name: Install MXE
        if: matrix.mxe != 'none'
        run: |
          curl -L -o mxe-${{ matrix.mxe }}.tar.xz https://github.com/BrettDong/MXE-GCC/releases/download/mxe-sdl-2-0-20/mxe-${{ matrix.mxe }}.tar.xz
          curl -L -o mxe-${{ matrix.mxe }}.tar.xz.sha256 https://github.com/BrettDong/MXE-GCC/releases/download/mxe-sdl-2-0-20/mxe-${{ matrix.mxe }}.tar.xz.sha256
          shasum -a 256 -c ./mxe-${{ matrix.mxe }}.tar.xz.sha256
          sudo tar xJf mxe-${{ matrix.mxe }}.tar.xz -C /opt
          curl -L -o SDL2-devel-2.26.2-mingw.tar.gz https://github.com/libsdl-org/SDL/releases/download/release-2.26.2/SDL2-devel-2.26.2-mingw.tar.gz
          shasum -a 256 -c ./build-scripts/SDL2-devel-2.26.2-mingw.tar.gz.sha256
          sudo tar -xzf SDL2-devel-2.26.2-mingw.tar.gz -C /opt/mxe/usr/${{ matrix.mxe }}-w64-mingw32.static.gcc12 --strip-components=2 SDL2-2.26.2/${{ matrix.mxe }}-w64-mingw32
      - name: Install dependencies (Linux)
        if: runner.os == 'Linux' && matrix.mxe == 'none' && matrix.android == 'none' && !matrix.wasm
        run: |
          sudo apt-get update
          sudo apt-get install libsdl2-dev
          git clone https://github.com/libsdl-org/SDL.git --branch release-2.0.20 --depth 1
          cd SDL
          mkdir build
          cd build
          ../configure
          make -j$((`nproc`+0))
          sudo make install
          cp ../LICENSE.txt ${{ github.workspace }}/LICENSE-SDL.txt
          sudo apt-get install libncursesw5-dev libsdl2-ttf-dev libsdl2-image-dev \
            libsdl2-mixer-dev libpulse-dev ccache gettext parallel
      - name: Install Emscripten (WebAssembly)
        if: matrix.wasm
        uses: mymindstorm/setup-emsdk@v13
      - name: Install runtime dependencies (mac)
        if: runner.os == 'macOS'
        uses: BrettDong/setup-sdl2-frameworks@v2
        with:
          sdl2: latest
          sdl2-ttf: latest
          sdl2-image: latest
          sdl2-mixer: latest
      - name: Install build dependencies (mac)
        if: runner.os == 'macOS'
        run: |
          HOMEBREW_NO_AUTO_UPDATE=yes HOMEBREW_NO_INSTALL_CLEANUP=yes brew install gettext ccache parallel dylibbundler
          pip3 install dmgbuild biplist
      - name: Create VERSION.TXT
        shell: bash
        run: |
          cat >VERSION.txt <<EOL
          build type: ${{ matrix.artifact }}
          build number: ${{ needs.release.outputs.timestamp }}
          commit sha: ${{ github.sha }}
          commit url: https://github.com/${{ github.repository }}/commit/${{ github.sha }}
          EOL
      - name: Compile translations (windows)
        if: runner.os == 'Windows'
        shell: msys2 {0}
        run: |
          lang/compile_mo.sh all
      - name: Build libbacktrace
        if: matrix.libbacktrace == 1
        run: |
          git clone https://github.com/ianlancetaylor/libbacktrace.git
          cd libbacktrace
          git checkout 14818b7783eeb9a56c3f0fca78cefd3143f8c5f6
          ./configure
          make -j$((`nproc`+0))
          cp LICENSE ${{ github.workspace }}/LICENSE-libbacktrace.txt
          sudo make install
      - name: Build CDDA (linux)
        if: runner.os == 'Linux' && matrix.mxe == 'none' && matrix.android == 'none' && !matrix.wasm
        run: |
          make -j$((`nproc`+0)) TILES=${{ matrix.tiles }} SOUND=${{ matrix.tiles }} RELEASE=1 LOCALIZE=1 LANGUAGES=all BACKTRACE=1 LIBBACKTRACE=${{ matrix.libbacktrace }} PCH=0 bindist
          mv cataclysmdda-0.I.tar.gz cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.tar.gz
      - name: Build CDDA (WebAssembly)
        if: matrix.wasm && success()
        run: |
          ./build-scripts/build-emscripten.sh
          ./build-scripts/prepare-web.sh
          (cd build && zip ../cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.${{ matrix.ext }} *)
      - name: Build CDDA (windows mxe)
        if: matrix.mxe != 'none'
        env:
          PLATFORM: /opt/mxe/usr/bin/${{ matrix.mxe }}-w64-mingw32.static.gcc12-
        run: |
          make -j$((`nproc`+0)) CROSS="${PLATFORM}" TILES=1 SOUND=1 RELEASE=1 LOCALIZE=1 LANGUAGES=all BACKTRACE=1 LIBBACKTRACE=${{ matrix.libbacktrace }} PCH=0 bindist
          mv cataclysmdda-0.I.zip cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.zip
      - name: Build CDDA (windows msvc)
        if: runner.os == 'Windows'
        env:
          # Enable pretty backtraces
          BACKTRACE: 1
          CDDA_RELEASE_BUILD: 1
          VCPKG_OVERLAY_TRIPLETS: ${{ github.workspace }}\.github\vcpkg_triplets
        run: |
          msbuild -m -p:Configuration=Release -p:Platform=${{ matrix.arch }} "-target:Cataclysm-vcpkg-static;JsonFormatter-vcpkg-static" msvc-full-features\Cataclysm-vcpkg-static.sln
          .\build-scripts\windist.ps1
          mv cataclysmdda-0.I.zip cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.zip
      - name: Build CDDA (osx)
        if: runner.os == 'macOS'
        run: |
          make -j3 TILES=${{ matrix.tiles }} SOUND=${{ matrix.tiles }} RELEASE=1 LOCALIZE=1 LANGUAGES=all BACKTRACE=0 PCH=0 USE_HOME_DIR=1 FRAMEWORK=1 UNIVERSAL_BINARY=1 dmgdist
          mv Cataclysm.dmg cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.dmg
      - name: Set up JDK 8 (android)
        if: runner.os == 'Linux' && matrix.android != 'none' && matrix.mxe == 'none'
        uses: actions/setup-java@v4
        with:
          java-version: '11'
          distribution: 'adopt'   
      - name: Setup Build and Dependencies (android)
        if: runner.os == 'Linux' && matrix.android != 'none' && matrix.mxe == 'none'
        run: |
          sudo apt-get update
          sudo apt-get install gettext          
      - name: Build CDDA (android)
        if: runner.os == 'Linux' && matrix.android != 'none' && matrix.mxe == 'none'
        working-directory: ./android
        run: |
          echo "${{ secrets.KEYSTORE }}" > release.keystore.asc   
          gpg -d --passphrase "${{ secrets.KEYSTORE_PASSWORD }}" --batch release.keystore.asc > app/release.keystore 
          echo "${{ secrets.KEYSTORE_PROPERTIES }}" > keystore.properties.asc   
          gpg -d --passphrase "${{ secrets.KEYSTORE_PASSWORD }}" --batch keystore.properties.asc > keystore.properties
          export UPSTREAM_BUILD_NUMBER="$((11581 + ${{ github.run_number }}))"
          chmod +x gradlew
          if [ ${{ matrix.android }} = arm64 ]
          then
               ./gradlew -Pj=$((`nproc`+0)) -Pabi_arm_32=false assembleExperimentalRelease
               mv ./app/build/outputs/apk/experimental/release/*.apk ../cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.apk
          elif [ ${{ matrix.android }} = arm32 ]
          then
               ./gradlew -Pj=$((`nproc`+0)) -Pabi_arm_64=false assembleExperimentalRelease
               mv ./app/build/outputs/apk/experimental/release/*.apk ../cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.apk 
          elif [ ${{ matrix.android }} = bundle ]
          then
               ./gradlew -Pj=$((`nproc`+0)) bundleExperimentalRelease
               mv ./app/build/outputs/bundle/experimentalRelease/*.aab ../cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.aab     
          fi
      - run: |
          gh release upload cdda-experimental-${{ needs.release.outputs.timestamp }} cdda-${{ matrix.artifact }}-${{ needs.release.outputs.timestamp }}.${{ matrix.ext }}
