# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# This property controls the number of jobs sent by -j switch to ndk-build
# You can override this from the command line by passing "-Pj=#"
j=2

# This property controls whether build should be run with localization
# You can override this from the command line by passing "-Plocalize=#"
localize=true

# This property controls whether to build arm 32 bit ABI
# You can override this from the command line by passing "-Pa<PERSON>_arm_32=#"
abi_arm_32=true

# This property controls whether to build arm 64 bit ABI
# You can override this from the command line by passing "-Pabi_arm_64=#"
abi_arm_64=true

# This property controls whether to build x86 32 bit ABI
# You can override this from the command line by passing "-Pabi_x86_32=#"
abi_x86_32=false

# This property controls whether to build x86 64 bit ABI
# You can override this from the command line by passing "-Pabi_x86_64=#"
abi_x86_64=false

# This property controls which dependencies file to use
# You can override this from the command line by passing "-Pdeps=#"
deps=./deps.zip

# This property controls which override version number to use
# You can override this from the command line by passing "-Poverride_version=#"
override_version=

# This property controls path where overridden version number header should be generated
# You can override this from the command line by passing "-Pversion_header_path=#"
version_header_path=./../src/version.h

# This property controls which compileSdkVersion should be used
# You can override this from the command line by passing "-Poverride_compileSdkVersion=#"
override_compileSdkVersion=34

# This property controls which minSdkVersion should be used
# You can override this from the command line by passing "-Poverride_minSdkVersion=#"
override_minSdkVersion=21

# This property controls which targetSdkVersion should be used
# You can override this from the command line by passing "-Poverride_targetSdkVersion=#"
override_targetSdkVersion=34

# This property controls which ndkBuildAppPlatform should be used
# You can override this from the command line by passing "-Poverride_ndkBuildAppPlatform=#"
override_ndkBuildAppPlatform=android-21

# This property controls which ndkVersion should be used
# You can override this from the command line by passing "-Poverride_ndkVersion=#"
override_ndkVersion=25.2.9519653

# This property controls which versionCode should be used
# You can override this from the command line by passing "-Poverride_versionCode=#"
override_versionCode=1
