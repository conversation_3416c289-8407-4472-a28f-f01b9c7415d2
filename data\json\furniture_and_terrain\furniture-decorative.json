[{"type": "furniture", "id": "f_bigmirror", "name": "standing mirror", "symbol": "{", "description": "A full-length mirror mounted in a sleek aluminum frame.  You can easily see all of the dirt and blood on your clothes, and the weariness in your eyes.", "color": "white", "move_cost_mod": 2, "coverage": 80, "required_str": 5, "crafting_pseudo_item": "mirror", "flags": ["NOITEM", "BLOCKSDOOR", "EASY_DECONSTRUCT"], "examine_action": "change_appearance", "deconstruct": {"items": [{"item": "<PERSON><PERSON><PERSON><PERSON>", "count": 1}]}, "bash": {"str_min": 5, "str_max": 16, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "furn_set": "f_bigmirror_b", "items": [{"item": "glass_shard", "count": [121, 131]}]}}, {"type": "furniture", "id": "f_bigmirror_b", "name": "broken standing mirror", "description": "An aluminum frame for a full-length mirror, with most of the mirror missing.  What remains in the frame are large, dangerous-looking shards of fractured glass.", "symbol": "{", "color": "light_gray", "move_cost_mod": 2, "coverage": 80, "required_str": 5, "flags": ["NOITEM", "BLOCKSDOOR"], "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap_aluminum", "count": [7, 10]}, {"item": "glass_shard", "count": [26, 46]}]}}, {"id": "f_bitts", "type": "furniture", "name": "bitts", "description": "A pair of vertical iron posts mounted on a wharf, pier, or other form of dock.  They are used to secure mooring lines, ropes, and similar.", "symbol": "B", "color": "light_gray", "move_cost_mod": 2, "coverage": 30, "required_str": 0, "bash": {"str_min": 80, "str_max": 200, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "steel_chunk", "count": [5, 10]}]}, "flags": ["TRANSPARENT", "MOUNTABLE", "SHORT"]}, {"id": "f_cleat", "type": "furniture", "name": "cleat", "description": "A small horn cleat with two \"horns\" extending parallel to the deck or the axis of the spar, and resembling a small anvil.  They are used to secure mooring lines, ropes, and similar.", "symbol": "B", "color": "light_gray", "looks_like": "f_bitts", "move_cost_mod": 2, "coverage": 30, "required_str": 0, "bash": {"str_min": 80, "str_max": 200, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "steel_chunk", "count": [1, 3]}]}, "flags": ["TRANSPARENT", "SHORT"]}, {"type": "furniture", "id": "f_shackle", "name": "manacles", "description": "A pair of metal shackles with heavy chains mounted to a wall or floor.", "symbol": "8", "color": "light_gray", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "ALLOW_FIELD_EFFECT", "MOUNTABLE", "SHORT"], "bash": {"str_min": 18, "str_max": 40, "sound": "smash!", "sound_fail": "crack.", "items": [{"item": "chain", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_statue", "name": "statue", "description": "A massive block of stone that has been carefully carved into a work of timeless art.", "symbol": "S", "color": "dark_gray", "move_cost_mod": -1, "coverage": 50, "required_str": 10, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "SIGN"], "bash": {"str_min": 16, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [1, 6]}]}}, {"type": "furniture", "id": "f_mannequin", "name": "mannequin", "description": "A life-size wooden figure of a person, most commonly used to display clothing in stores, or for tailors to design outfits on.  Considering all that's happened, something about it is somewhat unnerving.", "symbol": "@", "color": "brown", "move_cost_mod": 2, "coverage": 40, "required_str": 5, "flags": ["PLACE_ITEM", "TRANSPARENT", "FLAMMABLE", "EASY_DECONSTRUCT"], "deployed_item": "mannequin", "deconstruct": {"items": [{"item": "mannequin", "count": 1}]}, "examine_action": "deployed_furniture", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "splinter", "count": [9, 12]}]}}, {"type": "furniture", "id": "f_birdbath", "name": "birdbath", "description": "A wide stone bowl mounted to a pedestal, usually filled with rainwater, meant for birds to play or bathe in.", "symbol": "o", "color": "light_gray", "move_cost_mod": -1, "required_str": 10, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "LIQUIDCONT", "TRANSPARENT"], "bash": {"str_min": 16, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [1, 6]}, {"item": "rebar", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_gazing_ball", "name": "gazing ball", "description": "A mirrored glass globe mounted on a cement pedestal.  It warps your reflection when you look into it.", "symbol": "o", "color": "white", "move_cost_mod": -1, "required_str": 10, "flags": ["BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "rock", "count": [2, 4]}, {"item": "rebar", "count": [0, 1]}, {"item": "glass_shard", "count": [10, 15]}]}}, {"type": "furniture", "id": "f_rotary_clothesline", "name": "rotary clothes dryer line", "description": "A central metal pole holding up a wide rotating frame, this would be used to hang up clothes to dry in the sunlight.", "symbol": "X", "color": "white", "move_cost_mod": -2, "required_str": 10, "flags": ["TRANSPARENT", "BLOCKSDOOR", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "pipe", "count": 6}, {"item": "steel_chunk", "count": [2, 6]}, {"item": "pipe_fittings", "count": [1, 3]}, {"item": "lc_wire", "count": 10}]}, "bash": {"str_min": 6, "str_max": 10, "sound": "smash!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 6]}, {"item": "lc_wire", "count": [1, 2]}, {"item": "scrap", "count": [1, 6]}]}}, {"type": "furniture", "id": "f_floor_lamp", "name": "standing lamp", "symbol": "T", "looks_like": "f_rack_coat", "description": "A light mounted on the top of a metal pole, this would be plugged into a wall socket to illuminate a room.", "color": "light_gray", "move_cost_mod": 2, "required_str": 1, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "standing_lamp"}, "flags": ["TRANSPARENT", "BLOCKSDOOR", "PLACE_ITEM", "EASY_DECONSTRUCT"], "deconstruct": {"items": [{"item": "standing_lamp", "count": 1}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "metal screeching!", "sound_fail": "bonk!", "items": [{"item": "scrap", "count": [1, 3]}, {"item": "cable", "charges": [0, 1]}, {"item": "e_scrap", "count": [0, 1]}, {"item": "glass_shard", "count": [0, 8]}, {"item": "pipe", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_lighthouse_light", "name": "lighthouse light", "description": "Very high power, modern lighthouse LED light source with attached heatsinks.", "symbol": "%", "color": "light_gray", "required_str": -1, "move_cost_mod": -1, "deconstruct": {"items": [{"item": "lightstrip_inactive"}, {"item": "material_aluminium_ingot", "count": 4}]}, "bash": {"str_min": 18, "str_max": 80, "sound": "clang!", "sound_fail": "ting.", "items": [{"item": "material_aluminium_ingot", "count": [1, 4]}, {"item": "e_scrap", "count": [1, 4]}]}}, {"type": "furniture", "id": "f_winter_wreath", "name": "pine wreath", "description": "A decorative wreath for the winter holidays.", "symbol": "o", "color": "light_green", "move_cost_mod": 2, "required_str": 5, "bash": {"str_min": 20, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "pine_bough", "count": [4, 6]}, {"item": "nail", "charges": [5, 14]}, {"item": "stick", "count": [1, 3]}]}, "flags": ["PERMEABLE", "THIN_OBSTACLE", "PLACE_ITEM", "EASY_DECONSTRUCT"]}, {"type": "furniture", "id": "f_sand_castle", "name": "sand castle", "description": "A glorious castle made of sand.  This mighty fortress will stand tall for the ages to come, a true testament to the skills of its builder.", "symbol": "m", "color": "yellow", "move_cost_mod": 2, "required_str": -1, "bash": {"str_min": 1, "str_max": 1, "sound": "crunch.", "sound_fail": "thump.", "items": [{"item": "material_sand", "count": 8}]}}, {"type": "furniture", "id": "f_decorative_tree", "name": "decorative tree", "description": "A decorative pine tree littered with ornaments for the winter holidays.", "symbol": "+", "color": "light_green", "looks_like": "t_tree_pine", "move_cost_mod": -1, "required_str": 10, "flags": ["BLOCKSDOOR", "PLACE_ITEM", "EASY_DECONSTRUCT"], "bash": {"str_min": 20, "str_max": 40, "sound": "smash!", "sound_fail": "thump.", "items": [{"item": "pine_bough", "count": [4, 6]}, {"item": "nail", "charges": [5, 14]}, {"item": "stick", "count": [1, 3]}]}}, {"type": "furniture", "id": "f_styling_station", "name": "styling station", "symbol": "}", "description": "A styling station, is a countertop with a mirror mounted above it and just enough room and cubbies to store a stylist's equipment on it.", "color": "white", "move_cost_mod": 5, "coverage": 80, "required_str": -1, "crafting_pseudo_item": "mirror", "flags": ["TRANSPARENT", "FLAMMABLE_ASH"], "examine_action": "change_appearance", "bash": {"str_min": 7, "str_max": 16, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "items": [{"item": "glass_shard", "count": [25, 50]}, {"item": "splinter", "count": [5, 15]}]}}, {"type": "furniture", "id": "f_indoor_flagpole", "name": "indoor flagpole", "description": "A 2.5-meter tall flagpole on a weighted stand.  You could hoist up a flag here.", "symbol": "F", "color": "light_gray", "move_cost_mod": 2, "coverage": 20, "required_str": 1, "flags": ["TRANSPARENT", "PLACE_ITEM"], "max_volume": "2 L", "bash": {"str_min": 10, "str_max": 30, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "material_aluminium_ingot", "count": [4, 8]}, {"item": "scrap", "count": [2, 6]}]}}]