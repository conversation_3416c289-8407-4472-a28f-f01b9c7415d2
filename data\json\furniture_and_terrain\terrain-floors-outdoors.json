[{"type": "terrain", "id": "t_dirt", "name": "dirt", "description": "It's dirt.  Looks like some fine soil for tillage.  Could also be dug out for construction projects.", "symbol": ".", "color": "brown", "connect_groups": "DIRT", "connects_to": ["DIRT", "CLAY", "SAND", "MUD", "DIRTMOUND", "CLAYMOUND", "SANDMOUND", "SANDGLASS", "CONCRETE"], "move_cost": 2, "flags": ["TRANSPARENT", "DIGGABLE", "FLAT", "PLOWABLE"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_forestfloor", "name": "forest floor", "description": "Covered in twigs and lots of brown, decaying leaves.", "symbol": ".", "color": "brown", "looks_like": "t_moss", "move_cost": 2, "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_sand", "name": "sand", "description": "A large area of fine sand that could be useful in a number of ways, if it was extracted properly.", "symbol": ".", "color": "yellow", "move_cost": 3, "connect_groups": "SAND", "connects_to": ["SAND", "SANDMOUND", "SANDGLASS"], "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_mud", "name": "mud", "description": "An area of wet, slick mud.", "symbol": ".", "color": "brown", "looks_like": "t_dirt", "move_cost": 3, "connect_groups": "MUD", "connects_to": "MUD", "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_clay", "name": "clay", "description": "A large area of malleable clay, suitable for kiln firing if it was extracted properly.", "symbol": ".", "color": "light_red", "connect_groups": "CLAY", "connects_to": ["CLAY", "CLAYMOUND"], "move_cost": 2, "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_claymound", "name": "mound of clay", "description": "A mound of clay soil.", "looks_like": "t_dirtmound", "symbol": "#", "color": "brown", "connect_groups": "CLAYMOUND", "connects_to": "CLAYMOUND", "move_cost": 5, "flags": ["TRANSPARENT", "BURROWABLE"], "bash": {"str_min": 2, "str_max": 4, "sound": "thump!", "sound_fail": "thud!", "ter_set": "t_clay", "items": [{"item": "clay_lump", "count": [6, 12]}]}}, {"type": "terrain", "id": "t_sandmound", "name": "mound of sand", "description": "A mound of sand.", "looks_like": "t_dirtmound", "symbol": "#", "color": "brown", "connect_groups": "SANDMOUND", "connects_to": "SANDMOUND", "move_cost": 5, "flags": ["TRANSPARENT", "BURROWABLE"], "bash": {"str_min": 2, "str_max": 4, "sound": "thump!", "sound_fail": "thud!", "ter_set": "t_sand", "items": [{"item": "material_sand", "charges": [300, 600]}]}}, {"type": "terrain", "id": "t_glassed_sand", "name": "glassed sand", "description": "A patch of sand with a crust of natural glass atop it from exposure to intense heat.", "looks_like": "t_sand", "symbol": "~", "color": "brown", "connect_groups": "SANDGLASS", "connects_to": "SANDGLASS", "move_cost": 3, "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "crunch", "ter_set": "t_sand", "str_min": 25, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_dirtmound", "name": "upturned dirt", "description": "An area of loose dirt, not easily traversable.  If examined more closely, it's quite favorable for planting seeds and the like.", "symbol": "#", "color": "brown", "connect_groups": "DIRTMOUND", "connects_to": "DIRTMOUND", "move_cost": 3, "flags": ["TRANSPARENT", "DIGGABLE", "MOUNTABLE", "NOCOLLIDE", "PLANTABLE"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100}, "examine_action": "dirtmound"}, {"type": "terrain", "id": "t_dirtmoundfloor", "name": "mound of upturned dirt", "description": "A giant hill of dirt that looks like you could crawl inside for shelter.", "looks_like": "t_dirtmound", "symbol": "#", "color": "brown", "connect_groups": ["DIRT", "INDOORFLOOR"], "connects_to": "DIRT", "move_cost": 3, "coverage": 40, "flags": ["TRANSPARENT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "items": [{"item": "material_soil", "count": [100, 200]}]}}, {"type": "terrain", "id": "t_fault", "name": "odd fault", "description": "An unnaturally humanoid-shaped hole, it seems oddly familiar.  You feel a strange urge to examine it closer, as if it belongs to you somehow.", "symbol": "#", "color": "magenta", "looks_like": "t_pit", "move_cost": 0, "roof": "t_rock_floor", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL"], "examine_action": {"type": "effect_on_condition", "effect_on_conditions": [{"id": "EOC_TERRAIN_FAULT", "effect": [{"u_message": "This wall is perfectly vertical.  Odd, twisted holes are set in it, leading as far back into the solid rock as you can see.  The holes are humanoid in shape, but with long, twisted, distended limbs.", "popup": true}]}]}}, {"type": "terrain", "id": "t_grave", "name": "grave", "looks_like": "t_dirtmound", "description": "A dirt grave, with some grass growing on it.  At least some of the dead do actually rest in peace.", "symbol": "#", "color": "green", "move_cost": 3, "flags": ["TRANSPARENT", "DIGGABLE", "MOUNTABLE", "NOCOLLIDE"], "bash": {"sound": "thump", "ter_set": "t_dirt", "str_min": 50, "str_max": 100, "str_min_supported": 100}}, {"type": "terrain", "id": "t_grave_new", "name": "grave", "looks_like": "t_dirtmound", "description": "A fresh grave, covered with stones, either to keep something from digging it up or to keep one inside from digging out of it.  Or both.  Two planks mark this place of someone's eternal rest.", "symbol": "#", "color": "brown", "move_cost": 3, "flags": ["TRANSPARENT", "DIGGABLE", "MOUNTABLE", "NOCOLLIDE", "CONTAINER", "SEALED"], "bash": {"sound": "thump", "ter_set": "t_dirt", "str_min": 50, "str_max": 100, "str_min_supported": 100}}, {"type": "terrain", "id": "t_rock_floor_no_roof", "name": "rock floor", "description": "A relatively flat area of rock and stone.  Looks stable enough to be mined with the proper mining gear.", "symbol": ".", "color": "light_gray", "looks_like": "t_rock_floor", "connect_groups": "ROCKFLOOR", "connects_to": "ROCKFLOOR", "move_cost": 2, "roof": "t_open_air", "flags": ["TRANSPARENT", "FLAT", "ROAD"], "bash": {"ter_set": "t_null", "str_min": 75, "str_max": 400, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_rubber_mulch", "name": "rubber mulch", "description": "A large area mulched with shredded rubber.  It's a softer place to land than regular dirt.", "symbol": ".", "color": "dark_gray", "move_cost": 3, "connect_groups": "MULCHFLOOR", "connects_to": "MULCHFLOOR", "flags": ["TRANSPARENT", "DIGGABLE", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_pavement", "name": "pavement", "connect_groups": "PAVEMENT", "connects_to": "PAVEMENT", "description": "A segment of asphalt, slowly degrading from cracks, frost heaves, and lack of maintenance.", "symbol": ".", "color": "dark_gray", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 10]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_pavement_y", "name": "yellow pavement", "connect_groups": ["PAVEMENT", "PAVEMENT_MARKING"], "connects_to": "PAVEMENT_MARKING", "description": "Streaks of carefully aligned yellow paint mark the road to inform drivers not to cross.  No one is enforcing these rules anymore.", "symbol": ".", "color": "yellow", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 10]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_zebra", "name": "pedestrian crossing", "connect_groups": ["PAVEMENT", "PAVEMENT_ZEBRA"], "connects_to": "PAVEMENT_ZEBRA", "description": "White polymer lines painted on the pavement which indicate places where pedestrians should cross the road.", "symbol": ".", "color": "white", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 10]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_sidewalk", "name": "sidewalk", "description": "An area of common poured concrete, damaged by frost heaves and large cracks due to lack of maintenance.", "symbol": ".", "color": "light_gray", "looks_like": "t_concrete", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "connect_groups": "CONCRETE", "connects_to": "CONCRETE", "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 10]}]}}, {"type": "terrain", "id": "t_thconc_floor_no_roof", "name": "concrete floor", "description": "A bare and cold concrete floor.", "looks_like": "t_thconc_floor", "symbol": ".", "color": "cyan", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD"], "connect_groups": "CONCRETE", "connects_to": "CONCRETE", "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 100, "str_max": 400, "str_min_supported": 150, "items": [{"item": "rock", "count": [5, 10]}, {"item": "scrap", "count": [5, 8]}, {"item": "rebar", "count": [0, 2]}]}}, {"type": "terrain", "id": "t_strconc_floor_no_roof", "name": "reinforced concrete floor", "description": "An extremely resilient floor made from carefully placed rebar and poured concrete, capable of providing protection from the elements.  As for the matching roof, it still requires supporting walls, otherwise it may very well cave in.", "looks_like": "t_strconc_floor", "symbol": ".", "color": "cyan", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD"], "connect_groups": "CONCRETE", "connects_to": "CONCRETE", "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 150, "str_max": 400, "str_min_supported": 200, "items": [{"item": "rock", "count": [10, 22]}, {"item": "scrap", "count": [10, 12]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_concrete", "name": "concrete", "description": "A newer segment of poured concrete with surface finishes for aesthetics and resistance to freeze-thaw cycles.", "symbol": ".", "color": "light_gray", "looks_like": "t_pavement", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "connect_groups": "CONCRETE", "connects_to": "CONCRETE", "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 15]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_concrete_y", "name": "concrete", "description": "A newer segment of poured concrete with surface finishes for aesthetics and resistance to freeze-thaw cycles.  Covered with a streak of yellow paint.", "symbol": ".", "color": "yellow", "looks_like": "t_pavement_y", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD", "MINEABLE"], "connect_groups": "CONCRETE", "connects_to": "CONCRETE", "bash": {"ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "rock", "count": [2, 15]}, {"item": "rebar", "count": [0, 4]}]}}, {"type": "terrain", "id": "t_floor_noroof", "name": "wooden floor", "description": "A wooden floor created from boards, packed tightly together and nailed down.  Common on patios.", "symbol": ".", "color": "brown", "looks_like": "t_floor", "move_cost": 2, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "FLAT", "ROAD"], "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "splinter", "count": [2, 32]}, {"item": "2x4", "count": [0, 8]}, {"item": "nail", "charges": [5, 10]}]}}, {"type": "terrain", "id": "t_floor_wooden_gangway", "name": "wooden floor gangway", "description": "A wooden gangway bridging a short gap.  Sturdy enough to act as a floor, but not sufficient to support additional flooring.", "symbol": ".", "color": "brown", "looks_like": "t_floor", "move_cost": 2, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "FLAT", "ROAD"], "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "splinter", "count": [2, 32]}, {"item": "2x4", "count": [0, 8]}, {"item": "nail", "charges": [5, 10]}]}}, {"type": "terrain", "id": "t_metal_floor_no_roof", "name": "metal floor", "looks_like": "t_metal_floor", "description": "High-quality and tough checkered metal flooring to reduce the risk of slips and falls.", "symbol": ".", "color": "light_cyan", "connect_groups": "METALFLOOR", "connects_to": "METALFLOOR", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 200, "str_max": 500, "str_min_supported": 200, "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [3, 12]}, {"item": "scrap", "count": [9, 36]}]}}, {"type": "terrain", "id": "t_floor_metal_gangway", "name": "metal floor gangway", "looks_like": "t_metal_floor", "description": "A metal gangway bridging a short gap.  Sturdy enough to act as a floor, but not sufficient to support additional flooring.", "symbol": ".", "color": "light_cyan", "connect_groups": "METALFLOOR", "connects_to": "METALFLOOR", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 200, "str_max": 500, "str_min_supported": 200, "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [3, 12]}, {"item": "scrap", "count": [9, 36]}]}}, {"type": "terrain", "id": "t_linoleum_white_no_roof", "name": "linoleum tile", "looks_like": "t_linoleum_white", "description": "A section of flooring made out of a tough, rubbery material.  Colored a simple white.", "symbol": ".", "color": "white", "connect_groups": "LINOLEUM", "connects_to": "LINOLEUM", "move_cost": 2, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "FLAT", "ROAD"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "splinter", "count": [2, 8]}, {"item": "nail", "charges": [5, 10]}]}}, {"type": "terrain", "id": "t_linoleum_gray_no_roof", "name": "linoleum tile", "looks_like": "t_linoleum_gray", "description": "A section of flooring made out of a tough, gray, rubbery material.", "symbol": ".", "color": "light_gray", "connect_groups": "LINOLEUM", "connects_to": "LINOLEUM", "move_cost": 2, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "FLAT", "ROAD"], "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "items": [{"item": "splinter", "count": [2, 8]}, {"item": "nail", "charges": [5, 10]}]}}, {"type": "terrain", "id": "t_dirtfloor_no_roof", "name": "dirt floor", "description": "A floor consisting of finely mixed earth that has been tamped down.", "symbol": ".", "color": "brown", "connect_groups": "DIRT", "connects_to": "DIRT", "move_cost": 2, "looks_like": "t_dirtfloor", "flags": ["TRANSPARENT", "FLAT", "DIGGABLE"], "bash": {"sound": "SMASH!", "ter_set": "t_null", "str_min": 50, "str_max": 400, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_gelatinous_groundcover", "name": "gelatinous ground", "description": "An incursion of a large area of otherworldly substrate, it feels like walking on hardened gelatin.", "symbol": ".", "color": "pink", "move_cost": 5, "flags": ["TRANSPARENT", "FLAT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 50, "str_max": 100, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_orifice", "name": "orifice", "description": "A large round orifice, with trails of fresh and dried-up strange green liquid with a strongly acidic odor covering its inner sides.", "symbol": "O", "color": "green", "looks_like": "t_pit", "move_cost": 0, "roof": "t_rock_floor", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL"]}, {"type": "terrain", "id": "t_elevator_no_roof", "name": "elevator", "description": "An elevator section of the flight deck.", "symbol": ".", "color": "dark_gray", "looks_like": "t_elevator", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ELEVATOR"]}, {"type": "terrain", "id": "t_deck_coating_no_roof", "name": "deck coating", "description": "A thick metal hull coating, covered with a military-grade epoxy mix.  Used for runways, and can't be melted by jet engine nozzles.", "symbol": ".", "looks_like": "t_pavement", "color": "dark_gray", "connect_groups": "METALFLOOR", "connects_to": "METALFLOOR", "move_cost": 2, "flags": ["FLAT", "ROAD", "TRANSPARENT"], "bash": {"sound": "thump", "ter_set": "t_null", "str_min": 500, "str_max": 1000, "str_min_supported": 200, "items": [{"item": "steel_lump", "count": [1, 4]}, {"item": "steel_chunk", "count": [3, 12]}, {"item": "scrap", "count": [9, 36]}]}}, {"type": "terrain", "id": "t_deck_coating_no_roof_y", "name": "yellow deck coating", "description": "A thick metal hull covering, covered with a military-grade epoxy mix and painted yellow.", "copy-from": "t_deck_coating_no_roof", "looks_like": "t_pavement_y", "flags": ["FLAT", "ROAD", "TRANSPARENT"], "symbol": ".", "color": "yellow"}, {"type": "terrain", "id": "t_deck_coating_no_roof_r", "name": "red deck coating", "description": "A thick metal hull covering, covered with a military-grade epoxy mix, and painted red.", "copy-from": "t_deck_coating_no_roof", "looks_like": " ", "flags": ["FLAT", "ROAD", "TRANSPARENT"], "symbol": ".", "color": "red"}, {"type": "terrain", "id": "t_deck_coating_no_roof_w", "name": "white deck coating", "description": "A thick metal hull covering, covered with a military-grade epoxy mix and painted white.", "copy-from": "t_deck_coating_no_roof", "looks_like": " ", "flags": ["FLAT", "ROAD", "TRANSPARENT"], "symbol": ".", "color": "white"}, {"type": "terrain", "id": "t_stone_masonry_floor", "name": "stone masonry floor", "description": "A floor of carefully cut and laid stones, joined with mortar.", "symbol": ".", "color": "light_gray", "looks_like": "t_rock_floor", "connect_groups": "ROCKFLOOR", "connects_to": "ROCKFLOOR", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT", "ROAD"], "bash": {"ter_set": "t_null", "str_min": 75, "str_max": 400, "str_min_supported": 100, "bash_below": true}}, {"type": "terrain", "id": "t_grate", "name": "metal grate", "description": "A walkway that can be used as a protective covering over drains, or even as a filter.", "symbol": "#", "color": "dark_gray", "move_cost": 2, "flags": ["TRANSPARENT"]}, {"type": "terrain", "id": "t_floor_red", "name": "red floor", "description": "A red section of flooring.", "symbol": ".", "color": "red", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT"]}, {"type": "terrain", "id": "t_floor_green", "name": "green floor", "description": "A green section of flooring.", "symbol": ".", "color": "green", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT"]}, {"type": "terrain", "id": "t_floor_blue", "name": "blue floor", "description": "A blue section of flooring.", "symbol": ".", "color": "blue", "move_cost": 2, "flags": ["TRANSPARENT", "FLAT"]}]