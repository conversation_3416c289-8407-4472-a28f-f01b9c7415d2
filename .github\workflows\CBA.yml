name: Clang Build Analyzer

on:
  push:
    branches:
    - master
  workflow_dispatch:

# Mixing in event_name so that a master push and a workflow dispatch can run at the same time.
concurrency: clang_build_analyzer-${{ github.event_name }}

jobs:
  build:
    runs-on: ubuntu-latest    
    steps:
    - uses: actions/checkout@v4
    - name: install dependencies
      run: sudo apt-get install libncursesw5-dev gettext
    - name: Prepare Build Analyzer
      run: |
        cd
        git clone --recursive https://github.com/aras-p/ClangBuildAnalyzer.git buildAnalyzer
        cd buildAnalyzer
        mkdir build
        cd build
        cmake -DCMAKE_BUILD_TYPE=RELEASE ..
        cmake --build . -- -j3
        echo "${PWD}" >> $GITHUB_PATH
    - uses: ammaraskar/gcc-problem-matcher@master
    - name: make
      run: |
        ClangBuildAnalyzer --start .
        CLANG=clang++-14 CXXFLAGS=-ftime-trace make
        ClangBuildAnalyzer --stop . buildAnalysis
    - name: Analyze
      run: Clang<PERSON><PERSON>Analyzer --analyze buildAnalysis
    - uses: actions/upload-artifact@v4
      with:
        name: Clang<PERSON>uildAnalyzer-traces
        path: "**/obj/*.json"
