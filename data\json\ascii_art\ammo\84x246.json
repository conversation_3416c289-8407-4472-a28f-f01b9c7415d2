[{"type": "ascii_art", "id": "84x246mm_he", "picture": ["  <color_light_gray>,.", " /  \\", "|┴┴┴┴|", "<color_green>│<color_yellow>≈≈≈≈</color>│", "│    │", "<color_dark_gray>║    ║", "║<color_white>≈≈≈≈</color>║", "║    ║", "║    ║", "║    ║", "║    ║", "║    ║", "║    ║", "╩════╩"]}, {"type": "ascii_art", "id": "84x246mm_hedp", "picture": ["<color_dark_gray>  /\\", " /  \\", "/____\\", "│    │", "│<color_yellow>≈≈≈≈</color>│", "│<color_white>_<color_yellow>][</color>_</color>│", "║    ║", "║<color_white>≈≈≈≈</color>║", "║    ║", "║ <color_white>][</color> ║", "║ <color_white>][</color> ║", "║ <color_white>][</color> ║", "║ <color_white>][</color> ║", "║    ║", "╩════╩"]}, {"type": "ascii_art", "id": "84x246mm_smoke", "picture": ["  <color_light_gray>,.", " /  \\", "<color_light_green>|    |", "│    │", "│<color_dark_gray>≈≈≈≈</color>│", "│    │", "│    │", "<color_dark_gray>║    ║", "║<color_white>≈≈≈≈</color>║", "║    ║", "║    ║", "║    ║", "║    ║", "║    ║", "║    ║", "╩════╩"]}]