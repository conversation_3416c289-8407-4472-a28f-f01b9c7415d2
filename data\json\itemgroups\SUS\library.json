[{"id": "SUS_tailoring_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of tailoring manuals and references.", "subtype": "collection", "entries": [{"item": "mag_tailor", "count": [1, 10], "prob": 80}, {"item": "manual_tailor", "count": [1, 4], "prob": 80}, {"item": "textbook_tailor", "count": [1, 4], "prob": 80}, {"item": "tailor_portfolio", "prob": 20}, {"item": "tailor_japanese", "prob": 10}, {"item": "scots_tailor", "prob": 10}]}, {"id": "SUS_tailoring_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of tailoring manuals and references.", "subtype": "collection", "entries": [{"item": "mag_tailor", "count": [0, 2], "prob": 40}, {"item": "manual_tailor", "count": [0, 2], "prob": 30}, {"item": "textbook_tailor", "count": [0, 2], "prob": 30}, {"item": "tailor_portfolio", "prob": 5}, {"item": "tailor_japanese", "prob": 2}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "scots_tailor", "prob": 3}]}, {"id": "SUS_engineering_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of mechanical engineering textbooks and references.", "subtype": "collection", "entries": [{"item": "mag_mechanics", "count": [1, 10], "prob": 80}, {"item": "manual_mechanics", "count": [1, 4], "prob": 70}, {"item": "textbook_mechanics", "count": [1, 4], "prob": 80}, {"item": "book_icef", "count": [1, 3], "prob": 60}, {"item": "welding_book", "count": [1, 3], "prob": 80}, {"item": "textbook_carpentry", "count": [1, 2], "prob": 50}, {"item": "concrete_book", "count": [1, 2], "prob": 40}]}, {"id": "SUS_engineering_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of mechanical engineering textbooks and references.", "subtype": "collection", "entries": [{"item": "mag_mechanics", "count": [0, 4], "prob": 40}, {"item": "manual_mechanics", "count": [0, 1], "prob": 35}, {"item": "textbook_mechanics", "count": [0, 2], "prob": 30}, {"item": "book_icef", "count": [0, 1], "prob": 20}, {"item": "welding_book", "count": [0, 3], "prob": 30}, {"item": "textbook_carpentry", "count": [0, 2], "prob": 25}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "concrete_book", "count": [0, 2], "prob": 10}]}, {"id": "SUS_crafts_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of crafts manuals and references.", "subtype": "collection", "entries": [{"item": "mag_fabrication", "count": [1, 10], "prob": 80}, {"item": "manual_fabrication", "count": [1, 4], "prob": 70}, {"item": "textbook_fabrication", "count": [1, 2], "prob": 60}, {"item": "bronze_mag", "count": [1, 2], "prob": 50}, {"item": "mag_carpentry", "count": [1, 4], "prob": 80}, {"item": "glassblowing_book", "count": [1, 2], "prob": 60}, {"item": "theater_props", "count": [1, 2], "prob": 40}, {"item": "jewelry_book", "count": [1, 3], "prob": 80}]}, {"id": "SUS_crafts_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of crafts manuals and references.", "subtype": "collection", "entries": [{"item": "mag_fabrication", "count": [0, 3], "prob": 20}, {"item": "manual_fabrication", "count": [0, 2], "prob": 30}, {"item": "textbook_fabrication", "count": [0, 1], "prob": 20}, {"item": "mag_carpentry", "count": [0, 1], "prob": 20}, {"item": "bronze_mag", "count": [0, 1], "prob": 20}, {"item": "glassblowing_book", "count": [0, 1], "prob": 20}, {"item": "theater_props", "count": [0, 1], "prob": 20}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "jewelry_book", "count": [0, 1], "prob": 20}]}, {"id": "SUS_historical_manufacturing_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of manuals and references for historical manufacturing techniques.", "subtype": "collection", "entries": [{"item": "jewelry_book", "count": [1, 2], "prob": 70}, {"item": "textbook_armeast", "count": [1, 2], "prob": 60}, {"item": "textbook_armwest", "count": [1, 4], "prob": 80}, {"item": "textbook_armschina", "count": [1, 2], "prob": 50}, {"item": "textbook_weapeast", "count": [1, 2], "prob": 60}, {"item": "textbook_weapwest", "count": [1, 4], "prob": 80}, {"item": "textbook_weparabic", "count": [1, 2], "prob": 60}, {"item": "textbook_mesoam", "count": [1, 2], "prob": 50}, {"item": "bronze_book", "count": [1, 3], "prob": 50}, {"item": "bronze_mag", "count": [1, 2], "prob": 30}, {"item": "recipe_arrows", "count": [1, 3], "prob": 60}, {"item": "recipe_bows", "count": [1, 3], "prob": 80}, {"item": "ballista_book", "count": [1, 2], "prob": 40}]}, {"id": "SUS_historical_manufacturing_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of manuals and references for historical manufacturing techniques.", "subtype": "collection", "entries": [{"item": "jewelry_book", "count": [0, 1], "prob": 20}, {"item": "textbook_armeast", "count": [0, 1], "prob": 10}, {"item": "textbook_armwest", "count": [0, 1], "prob": 30}, {"item": "textbook_armschina", "count": [0, 1], "prob": 25}, {"item": "textbook_weapeast", "count": [0, 1], "prob": 20}, {"item": "textbook_weapwest", "count": [0, 2], "prob": 30}, {"item": "textbook_weparabic", "count": [0, 1], "prob": 20}, {"item": "textbook_mesoam", "count": [0, 1], "prob": 20}, {"item": "bronze_book", "count": [0, 1], "prob": 20}, {"item": "bronze_mag", "count": [0, 1], "prob": 10}, {"item": "recipe_arrows", "count": [0, 1], "prob": 30}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "recipe_bows", "count": [0, 2], "prob": 20}, {"item": "ballista_book", "count": [0, 1], "prob": 15}]}, {"id": "SUS_electronics_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of electronics textbooks and references.", "subtype": "collection", "entries": [{"item": "mag_electronics", "count": [1, 10], "prob": 80}, {"item": "manual_electronics", "count": [1, 4], "prob": 80}, {"item": "textbook_electronics", "count": [1, 3], "prob": 80}, {"item": "advanced_electronics", "count": [1, 3], "prob": 60}, {"item": "ic_reference_electronics", "count": [1, 2], "prob": 60}, {"item": "radio_book", "count": [1, 3], "prob": 70}, {"collection": [{"item": "textbook_robots", "count": [1, 2], "prob": 70}, {"item": "manual_computer", "count": [1, 3], "prob": 60}, {"item": "computer_science", "count": [1, 3], "prob": 70}], "prob": 30}, {"item": "repeater_mod_guide", "prob": 20}]}, {"id": "SUS_electronics_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of electronics textbooks and references.", "subtype": "collection", "entries": [{"item": "mag_electronics", "count": [0, 2], "prob": 30}, {"item": "manual_electronics", "count": [0, 3], "prob": 30}, {"item": "textbook_electronics", "count": [0, 2], "prob": 30}, {"item": "advanced_electronics", "count": [0, 2], "prob": 20}, {"item": "ic_reference_electronics", "count": [0, 1], "prob": 60}, {"item": "radio_book", "count": [0, 3], "prob": 25}, {"collection": [{"item": "textbook_robots", "count": [0, 1], "prob": 20}, {"item": "manual_computer", "count": [0, 2], "prob": 15}, {"item": "computer_science", "count": [0, 2], "prob": 20}], "prob": 10}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "repeater_mod_guide", "prob": 10}]}, {"id": "SUS_computers_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of computer science and programming textbooks and references.", "subtype": "collection", "entries": [{"item": "manual_computer", "count": [1, 4], "prob": 70}, {"item": "hackerman_computer", "count": [1, 4], "prob": 50}, {"item": "webbasics_computer", "count": [1, 4], "prob": 70}, {"item": "textbook_computer", "count": [1, 4], "prob": 80}, {"item": "computer_science", "count": [1, 4], "prob": 80}, {"item": "SICP", "prob": 70}]}, {"id": "SUS_computers_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of computer science and programming textbooks and references.", "subtype": "collection", "entries": [{"item": "manual_computer", "count": [0, 2], "prob": 20}, {"item": "hackerman_computer", "count": [1, 4], "prob": 10}, {"item": "webbasics_computer", "count": [1, 4], "prob": 30}, {"item": "textbook_computer", "count": [0, 2], "prob": 30}, {"item": "computer_science", "count": [0, 2], "prob": 30}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "SICP", "prob": 20}]}, {"id": "SUS_chemistry_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of chemistry textbooks and references.", "subtype": "collection", "entries": [{"item": "textbook_biodiesel", "count": [1, 2], "prob": 70}, {"item": "textbook_gaswarfare", "count": [1, 2], "prob": 40}, {"item": "textbook_chemistry", "count": [1, 4], "prob": 90}, {"item": "textbook_extraction", "count": [1, 2], "prob": 60}, {"item": "adv_chemistry", "count": [1, 4], "prob": 70}, {"item": "basic_chemistry", "count": [1, 3], "prob": 70}]}, {"id": "SUS_chemistry_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of chemistry textbooks and references.", "subtype": "collection", "entries": [{"item": "textbook_biodiesel", "count": [0, 1], "prob": 20}, {"item": "textbook_gaswarfare", "count": [0, 1], "prob": 10}, {"item": "textbook_chemistry", "count": [0, 2], "prob": 40}, {"item": "textbook_extraction", "count": [0, 1], "prob": 10}, {"item": "adv_chemistry", "count": [0, 2], "prob": 20}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "basic_chemistry", "count": [0, 1], "prob": 20}]}, {"id": "SUS_cooking_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of cooking manuals and references.", "subtype": "collection", "entries": [{"item": "mag_cooking", "count": [1, 10], "prob": 80}, {"item": "cookbook", "count": [1, 4], "prob": 80}, {"item": "cookbook_daintydishes", "count": [1, 2], "prob": 70}, {"item": "cookbook_foodfashions", "count": [1, 2], "prob": 70}, {"item": "cookbook_eatyrway", "prob": 66}, {"item": "cookbook_liverforkids", "prob": 60}, {"item": "cookbook_sushi", "count": [1, 2], "prob": 60}, {"item": "cookbook_italian", "count": [1, 2], "prob": 60}, {"item": "cookbook_mexican", "count": [1, 2], "prob": 60}, {"item": "family_cookbook", "prob": 20}, {"item": "scots_cookbook", "prob": 10}]}, {"id": "SUS_cooking_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of cooking manuals and references.", "subtype": "collection", "entries": [{"item": "mag_cooking", "count": [0, 3], "prob": 30}, {"item": "cookbook", "count": [0, 2], "prob": 30}, {"item": "cookbook_daintydishes", "count": [0, 1], "prob": 20}, {"item": "cookbook_foodfashions", "count": [0, 1], "prob": 20}, {"item": "cookbook_eatyrway", "prob": 16}, {"item": "cookbook_liverforkids", "prob": 20}, {"item": "cookbook_sushi", "count": [0, 1], "prob": 20}, {"item": "cookbook_italian", "count": [0, 1], "prob": 20}, {"item": "cookbook_mexican", "count": [0, 1], "prob": 20}, {"item": "family_cookbook", "prob": 5}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "scots_cookbook", "prob": 1}]}, {"id": "SUS_medicine_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of medical manuals and references.", "subtype": "collection", "entries": [{"item": "manual_first_aid", "count": [1, 2], "prob": 60}, {"item": "textbook_firstaid", "count": [1, 3], "prob": 70}, {"item": "emergency_book", "count": [1, 4], "prob": 70}]}, {"id": "SUS_medicine_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of medical manuals and references.", "subtype": "collection", "entries": [{"item": "manual_first_aid", "count": [0, 1], "prob": 20}, {"item": "textbook_firstaid", "count": [0, 1], "prob": 30}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "emergency_book", "count": [0, 2], "prob": 30}]}, {"id": "SUS_business_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of business theory textbooks.", "subtype": "collection", "entries": [{"item": "manual_business", "count": [1, 3], "prob": 80}, {"item": "textbook_business", "count": [1, 3], "prob": 80}, {"item": "manual_speech", "count": [1, 3], "prob": 80}, {"item": "textbook_speech", "count": [1, 3], "prob": 80}]}, {"id": "SUS_business_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of business theory textbooks.", "subtype": "collection", "entries": [{"item": "manual_business", "count": [0, 1], "prob": 20}, {"item": "textbook_business", "count": [0, 1], "prob": 20}, {"item": "manual_speech", "count": [0, 1], "prob": 20}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "textbook_speech", "count": [0, 1], "prob": 20}]}, {"id": "SUS_bushcraft_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of bushcraft and wilderness survival manuals and references.", "subtype": "collection", "entries": [{"item": "mag_survival", "count": [1, 10], "prob": 80}, {"item": "manual_survival", "count": [1, 3], "prob": 80}, {"item": "fun_survival", "count": [1, 3], "prob": 80}, {"item": "atomic_survival", "count": [1, 2], "prob": 30}, {"item": "textbook_survival", "count": [1, 3], "prob": 80}, {"item": "survival_book", "count": [1, 2], "prob": 60}, {"item": "manual_swimming", "count": [1, 2], "prob": 40}]}, {"id": "SUS_bushcraft_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of bushcraft and wilderness survival manuals and references.", "subtype": "collection", "entries": [{"item": "mag_survival", "count": [0, 3], "prob": 10}, {"item": "manual_survival", "count": [0, 1], "prob": 30}, {"item": "fun_survival", "count": [0, 1], "prob": 30}, {"item": "atomic_survival", "count": [0, 2], "prob": 10}, {"item": "textbook_survival", "count": [0, 2], "prob": 30}, {"item": "survival_book", "count": [0, 1], "prob": 20}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}, {"item": "manual_swimming", "count": [0, 1], "prob": 10}]}, {"id": "SUS_field_manual_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of military field and technical manuals.", "subtype": "collection", "entries": [{"item": "manual_stabbing", "count": [1, 3], "prob": 80}, {"item": "manual_rifle", "count": [1, 3], "prob": 80}, {"item": "manual_traps_mil", "count": [1, 3], "prob": 80}, {"item": "manual_traps_mil_basic", "count": [1, 3], "prob": 80}]}, {"id": "SUS_martial_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of martial arts manuals.", "subtype": "collection", "entries": [{"group": "book_martial_rare", "prob": 65}, {"group": "book_martial_common", "count": [1, 2], "prob": 100}, {"group": "book_mag_combat", "count": [1, 4], "prob": 100}, {"group": "book_manual_combat", "count": [1, 2], "prob": 50}]}, {"id": "SUS_martial_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of martial arts manuals.", "subtype": "collection", "entries": [{"group": "SUS_martial_bookcase", "prob": 100}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}]}, {"id": "SUS_fiction_bookcase", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of novels.", "subtype": "collection", "entries": [{"group": "SUS_book_fict", "count": [4, 22], "prob": 400}, {"item": "novel_war", "count": [1, 5], "prob": 80}, {"item": "novel_adventure", "count": [1, 5], "prob": 80}, {"item": "novel_experimental", "count": [1, 5], "prob": 80}, {"item": "novel_road", "count": [1, 5], "prob": 80}, {"item": "novel_buddy", "count": [1, 5], "prob": 80}, {"item": "novel_drama", "count": [1, 5], "prob": 80}, {"item": "novel_mystery", "count": [1, 5], "prob": 80}, {"item": "novel_fantasy", "count": [1, 5], "prob": 80}, {"item": "novel_erotic", "count": [1, 5], "prob": 80}, {"item": "novel_thriller", "count": [1, 5], "prob": 80}, {"item": "novel_crime", "count": [1, 5], "prob": 80}, {"item": "novel_horror", "count": [1, 5], "prob": 80}, {"item": "novel_tragedy", "count": [1, 5], "prob": 80}]}, {"id": "SUS_fiction_bookcase_vandalized", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a set of novels.", "subtype": "collection", "entries": [{"group": "SUS_fiction_bookcase", "count": [4, 22], "prob": 400}, {"item": "book_ruined", "count": [0, 12], "prob": 100}, {"item": "book_pieces", "count": [0, 26], "prob": 100}]}, {"id": "SUS_book", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single book of any type.", "subtype": "distribution", "entries": [{"group": "SUS_book_fict", "prob": 1}, {"group": "SUS_book_nonf", "prob": 1}]}, {"id": "SUS_book_sports", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns one book about sports.  It may be hard or soft cover, fiction or nonfiction", "subtype": "distribution", "entries": [{"group": "SUS_book_fict_soft_sports", "prob": 1}, {"group": "SUS_book_fict_hard_sports", "prob": 1}, {"group": "SUS_book_nonf_soft_sports", "prob": 1}, {"group": "SUS_book_nonf_hard_sports", "prob": 1}, {"group": "SUS_book_nonf_zine_sport", "prob": 1}]}, {"id": "SUS_book_fict", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single book of any fiction.", "subtype": "distribution", "entries": [{"group": "SUS_book_fict_soft", "prob": 8}, {"group": "SUS_book_fict_hard", "prob": 1}]}, {"id": "SUS_book_fict_soft", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single paperback book of any fiction.", "subtype": "distribution", "entries": [{"group": "SUS_book_fict_soft_pulp", "prob": 1}, {"group": "SUS_book_fict_soft_romc", "prob": 1}, {"group": "SUS_book_fict_soft_ya", "prob": 1}, {"group": "SUS_book_fict_soft_west", "prob": 1}, {"group": "SUS_book_fict_soft_satire", "prob": 1}, {"group": "SUS_book_fict_soft_swash", "prob": 1}, {"group": "SUS_book_fict_soft_sports", "prob": 1}, {"group": "SUS_book_fict_soft_scifi", "prob": 1}, {"group": "SUS_book_fict_soft_spy", "prob": 1}]}, {"id": "SUS_book_fict_hard", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single hardback novel.", "subtype": "distribution", "entries": [{"group": "SUS_book_fict_hard_scifi", "prob": 4}, {"group": "SUS_book_fict_hard_sports", "prob": 4}, {"group": "SUS_book_fict_hard_ya", "prob": 4}, {"item": "novel_samurai", "prob": 1}]}, {"id": "SUS_book_fict_soft_pulp", "type": "item_group", "//": "Returns a single pulp fiction genre dimestore novel.", "subtype": "distribution", "entries": [{"item": "book_fict_soft_pulp_venus", "prob": 2}, {"item": "book_fict_soft_pulp_wtmrw", "prob": 2}, {"item": "book_fict_soft_pulp_nogod", "prob": 2}, {"item": "book_fict_soft_pulp_ddive", "prob": 2}, {"item": "book_fict_soft_pulp_squids", "prob": 2}, {"item": "book_fict_soft_pulp_flashcc", "prob": 2}, {"item": "book_fict_soft_pulp_gcapes", "prob": 2}, {"item": "book_fict_soft_pulp_yesmurd", "prob": 2}, {"item": "book_anthology_sst", "prob": 2}, {"item": "book_anthology_bk", "prob": 2}, {"item": "book_anthology_cw", "prob": 2}, {"item": "novel_pulp", "prob": 1}, {"item": "novel_crime_midnight", "prob": 1}, {"item": "novel_crime_murdergrape", "prob": 1}]}, {"id": "SUS_book_fict_soft_west", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single paperback book of the western genre.", "subtype": "distribution", "entries": [{"item": "novel_western", "prob": 1}, {"item": "book_fict_soft_west_atwixt", "prob": 1}, {"item": "book_fict_soft_west_bart", "prob": 1}, {"item": "book_fict_soft_west_beans", "prob": 1}, {"item": "book_fict_soft_west_calico", "prob": 1}, {"item": "book_fict_soft_west_riot", "prob": 1}, {"item": "book_fict_soft_west_vaq", "prob": 1}, {"item": "book_fict_soft_west_vride", "prob": 1}, {"item": "book_fict_soft_west_gore", "prob": 1}]}, {"id": "SUS_book_fict_soft_scifi", "type": "item_group", "//2": "This returns a single paperback fictional scifi book.", "subtype": "distribution", "//3": "satire_cats and pulp_ddive are both dual genre scifi", "entries": [{"item": "novel_scifi", "prob": 4}, {"item": "book_fict_soft_scifi_brave", "prob": 1}, {"item": "book_fict_soft_scifi_f451", "prob": 1}, {"item": "book_fict_soft_scifi_roadp", "prob": 1}, {"item": "book_fict_soft_scifi_cybrd", "prob": 1}, {"item": "book_fict_soft_scifi_dune", "prob": 1}, {"item": "book_fict_soft_scifi_zamwe", "prob": 1}, {"item": "book_fict_soft_satire_cats", "prob": 1}, {"item": "book_anthology_htloop", "prob": 1}, {"item": "book_anthology_an", "prob": 1}, {"item": "book_anthology_sunvault", "prob": 1}, {"item": "book_anthology_moataatgb", "prob": 1}, {"item": "book_fict_soft_pulp_ddive", "prob": 1}]}, {"id": "SUS_book_fict_soft_sports", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This returns a single paperback fictional sports book.", "subtype": "distribution", "entries": [{"item": "novel_sports", "prob": 1}, {"item": "book_fict_soft_sports_bunt", "prob": 1}, {"item": "book_fict_soft_sports_tdsp", "prob": 1}, {"item": "book_fict_soft_sports_envy", "prob": 1}]}, {"id": "SUS_book_fict_soft_swash", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This returns a single paperback swashbuckling book.", "subtype": "distribution", "entries": [{"item": "novel_swash", "prob": 1}, {"item": "book_fict_soft_swash_blkfreight", "prob": 1}, {"item": "book_fict_soft_swash_searovers", "prob": 1}, {"item": "book_fict_soft_swash_buccaneer", "prob": 1}]}, {"id": "SUS_book_fict_soft_romc", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single paperback book of the romance genre.", "subtype": "distribution", "entries": [{"item": "novel_romance", "prob": 1}, {"item": "paperback_romance_circuses", "prob": 1}, {"item": "paperback_romance_cloven", "prob": 1}, {"item": "paperback_romance_conq", "prob": 1}, {"item": "paperback_romance_dd", "prob": 1}, {"item": "paperback_romance_diodes", "prob": 1}, {"item": "paperback_romance_envyh", "prob": 1}, {"item": "paperback_romance_grue", "prob": 1}, {"item": "paperback_romance_rider", "prob": 1}, {"item": "paperback_romance_rogue", "prob": 1}, {"item": "paperback_romance_secret", "prob": 1}]}, {"id": "SUS_book_fict_soft_ya", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group returns a single paperback book for young adults and teens.", "subtype": "distribution", "entries": [{"item": "book_fict_soft_ya_adrk", "prob": 1}, {"item": "book_fict_soft_ya_bjak", "prob": 1}, {"item": "book_fict_soft_ya_burn", "prob": 1}, {"item": "book_fict_soft_ya_hilo", "prob": 1}, {"item": "book_fict_soft_ya_myeyes", "prob": 1}, {"item": "book_fict_soft_ya_pbbr", "prob": 1}, {"item": "book_fict_soft_ya_rwya", "prob": 1}, {"item": "book_fict_soft_ya_sboy", "prob": 1}, {"item": "book_fict_soft_ya_sumv", "prob": 1}, {"item": "novel_coa", "prob": 1}]}, {"id": "SUS_book_fict_soft_satire", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single paperback satire novel", "subtype": "distribution", "entries": [{"item": "novel_satire", "prob": 2}, {"item": "book_fict_soft_satire_hgod", "prob": 1}, {"item": "book_fict_soft_satire_mandm", "prob": 1}, {"item": "book_fict_soft_satire_catch", "prob": 1}, {"item": "book_fict_soft_satire_dust", "prob": 1}, {"item": "book_fict_soft_satire_cats", "prob": 1}]}, {"id": "SUS_book_fict_soft_spy", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single paperback spy novel", "subtype": "distribution", "entries": [{"item": "novel_spy", "prob": 2}, {"item": "novel_spy_comefly", "prob": 1}, {"item": "novel_spy_rocketsci", "prob": 1}]}, {"id": "SUS_book_fict_hard_scifi", "type": "item_group", "//2": "This returns a single hardback fictional scifi book.", "subtype": "distribution", "entries": [{"item": "book_fict_hard_scifi_dune", "prob": 2}, {"item": "book_fict_hard_scifi_talnt", "prob": 2}, {"item": "book_fict_hard_scifi_zamwe", "prob": 2}, {"item": "book_fict_hard_scifi_fifth", "prob": 1}]}, {"id": "SUS_book_fict_hard_sports", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This returns a single hardback fictional sports book.", "subtype": "distribution", "entries": [{"item": "book_fict_hard_sports_semi", "prob": 1}, {"item": "book_fict_hard_sports_omni", "prob": 1}, {"item": "book_fict_hard_sports_uni", "prob": 1}]}, {"id": "SUS_book_fict_hard_ya", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group returns a single hardback book for young adults and teens.", "subtype": "distribution", "entries": [{"item": "book_fict_hard_ya_dark", "prob": 3}, {"item": "book_fict_hard_ya_btwo", "prob": 3}, {"item": "novel_coa2", "prob": 3}, {"item": "book_fict_hard_sports_uni", "prob": 2}]}, {"id": "SUS_book_nonf", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a single nonfiction printed material.", "subtype": "distribution", "entries": [{"group": "SUS_book_nonf_hard", "prob": 3}, {"group": "SUS_book_nonf_soft", "prob": 3}, {"group": "SUS_book_nonf_zine", "prob": 1}]}, {"id": "SUS_book_nonf_hard", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a single nonfiction book that is hard bound.  Note that this may or may not be a skill training book.", "subtype": "distribution", "entries": [{"group": "SUS_book_nonf_hard_dodge", "prob": 10}, {"group": "SUS_book_nonf_hard_spirit", "prob": 10}, {"group": "SUS_book_nonf_hard_homemk", "prob": 10}, {"group": "SUS_book_nonf_hard_psych", "prob": 10}, {"group": "SUS_book_nonf_hard_phil", "prob": 7}, {"group": "SUS_book_nonf_hard_sports", "prob": 3}, {"group": "SUS_book_nonf_hard_arch", "prob": 10}]}, {"id": "SUS_book_nonf_soft", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a single nonfiction paperback.", "subtype": "distribution", "entries": [{"group": "SUS_book_nonf_soft_occult", "prob": 2}, {"group": "SUS_book_nonf_soft_psych", "prob": 6}, {"group": "SUS_book_nonf_pulp_newage", "prob": 3}, {"group": "SUS_book_nonf_soft_sports", "prob": 3}, {"item": "book_nonf_soft_wedding_coolring", "prob": 1}, {"item": "book_nonf_soft_parent_howtogen", "prob": 1}, {"item": "book_nonf_soft_safety_radiaea", "prob": 1}, {"item": "book_nonf_soft_mechnic_hotrod", "prob": 1}, {"item": "book_nonf_soft_speech_naillaw", "prob": 1}, {"item": "book_nonf_soft_phil_benoth", "prob": 1}]}, {"id": "SUS_book_nonf_zine", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "a single magazine, but not comics", "subtype": "distribution", "entries": [{"group": "SUS_book_nonf_zine_news", "prob": 6}, {"group": "SUS_book_nonf_zine_spint", "prob": 5}, {"group": "SUS_book_nonf_zine_sport", "prob": 3}]}, {"id": "SUS_book_nonf_zine_news", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a single news magazine.", "subtype": "distribution", "entries": [{"item": "mag_news", "prob": 1}, {"item": "news_regional", "prob": 1}, {"item": "mag_glam", "prob": 1}, {"item": "book_nonf_zine_news_anlyst", "prob": 1}]}, {"id": "SUS_book_nonf_zine_spint", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a single special interest magazine.", "subtype": "distribution", "entries": [{"item": "fun_survival", "prob": 10}, {"item": "mag_survival", "prob": 10}, {"item": "mag_dude", "prob": 10}, {"item": "mag_traps", "prob": 10}, {"item": "mag_rifle", "prob": 10}, {"item": "mag_stabbing", "prob": 2}, {"item": "mag_swimming", "prob": 10}, {"item": "mag_beauty", "prob": 10}, {"item": "mag_tailor", "prob": 10}, {"item": "mag_computer", "prob": 10}, {"item": "mag_electronics", "prob": 10}, {"item": "mag_fabrication", "prob": 10}, {"item": "mag_mechanics", "prob": 10}, {"item": "mag_tailor", "prob": 6}, {"item": "news_regional", "prob": 10}, {"item": "mag_cooking", "prob": 10}, {"item": "mag_cutting", "prob": 6}, {"item": "mag_cars", "prob": 10}, {"item": "mag_guns", "prob": 10}, {"item": "mag_glam", "prob": 10}, {"item": "mag_gaming", "prob": 10}]}, {"id": "SUS_book_nonf_zine_sport", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a single sports or games magazine.", "//TODO": "rename this SUS_book_nonf_zine_sports", "subtype": "distribution", "entries": [{"item": "mag_unarmed", "prob": 1}, {"item": "mag_throwing", "prob": 1}, {"item": "mag_archery", "prob": 1}, {"item": "mag_bashing", "prob": 1}]}, {"id": "SUS_book_nonf_hard_homemk", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a book about homemaking.", "subtype": "distribution", "entries": [{"item": "book_nonf_hard_homemk_anythingcan", "prob": 10}, {"item": "book_nonf_hard_homemk_grtrms", "prob": 10}, {"item": "book_nonf_hard_homemk_handshome", "prob": 10}, {"item": "book_nonf_hard_homemk_loverms", "prob": 10}, {"item": "book_nonf_hard_homemk_nyparti", "prob": 10}, {"item": "book_nonf_hard_homemk_outkitchn", "prob": 10}, {"item": "book_nonf_hard_homemk_plantshome", "prob": 10}, {"item": "book_nonf_hard_homemk_wmencolor", "prob": 10}, {"item": "book_nonf_hard_psych_thergar", "prob": 3}]}, {"id": "SUS_book_nonf_hard_psych", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns a book about psychology.", "subtype": "distribution", "entries": [{"item": "book_nonf_hard_psych_forpsych", "prob": 1}, {"item": "book_nonf_hard_psych_grconres", "prob": 1}, {"item": "book_nonf_hard_psych_moodalm", "prob": 1}, {"item": "book_nonf_hard_psych_phonodis", "prob": 1}, {"item": "book_nonf_hard_psych_thergar", "prob": 1}]}, {"id": "SUS_book_nonf_hard_phil", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "Returns one hardback book about philosophy.", "subtype": "distribution", "entries": [{"item": "book_nonf_hard_phil_mdlogic", "prob": 1}, {"item": "book_nonf_hard_phil_aesth", "prob": 1}, {"item": "book_nonf_hard_sports_ergo", "prob": 1}, {"item": "book_nonf_hard_phil_phinfo", "prob": 1}]}, {"id": "SUS_book_nonf_hard_spirit", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single religious book that is hard bound.", "subtype": "distribution", "entries": [{"item": "holybook_bible1", "prob": 2}, {"item": "holybook_bible2", "prob": 2}, {"item": "holybook_bible3", "prob": 2}, {"item": "holybook_granth", "prob": 2}, {"item": "holybook_hadith", "prob": 2}, {"item": "holybook_havamal", "prob": 2}, {"item": "holybook_ka<PERSON><PERSON>", "prob": 1}, {"item": "holybook_kojiki", "prob": 2}, {"item": "holybook_mormon", "prob": 2}, {"item": "holybook_nag_hammadi", "prob": 1}, {"item": "holybook_pastafarian", "prob": 1}, {"item": "holybook_quran", "prob": 2}, {"item": "holybook_slack", "prob": 1}, {"item": "holybook_sutras", "prob": 2}, {"item": "holybook_talmud", "prob": 2}, {"item": "holybook_tanakh", "prob": 2}, {"item": "holybook_tripitaka", "prob": 2}, {"item": "holybook_upanishads", "prob": 2}, {"item": "holybook_vedas", "prob": 2}, {"item": "mycenacean_hymns", "prob": 1}]}, {"id": "SUS_book_nonf_soft_occult", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single paperback book of occult studies", "subtype": "distribution", "entries": [{"item": "occult_economicon", "prob": 1}, {"item": "occult_bobliographon", "prob": 1}, {"item": "occult_glimpses", "prob": 1}]}, {"id": "SUS_book_nonf_soft_psych", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single psychology paperback", "subtype": "distribution", "entries": [{"item": "book_nonf_soft_psych_paranoia", "prob": 1}, {"item": "book_nonf_soft_psych_psycolo", "prob": 1}, {"item": "book_nonf_soft_psych_psycstlk", "prob": 1}, {"item": "book_nonf_soft_psych_adrugds", "prob": 1}, {"item": "book_nonf_soft_psych_arteatds", "prob": 1}, {"item": "book_nonf_soft_psych_cggamers", "prob": 1}]}, {"id": "SUS_book_nonf_pulp_newage", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single new-age pulp book", "subtype": "distribution", "entries": [{"item": "book_newage_generic", "prob": 1}, {"item": "book_newage_inpsychic", "prob": 1}, {"item": "book_newage_healcrystals", "prob": 1}, {"item": "book_newage_oilsecrets", "prob": 1}, {"item": "book_newage_witchcraftbeg", "prob": 1}, {"item": "book_newage_witchcraftadv", "prob": 1}, {"item": "book_newage_beyondveil", "prob": 1}, {"item": "book_newage_weapcrys", "prob": 1}, {"item": "book_newage_aliens", "prob": 1}, {"item": "poetic_edda", "prob": 1}, {"item": "book_newage_horoscopes", "prob": 1}]}, {"id": "SUS_book_nonf_soft_sports", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns one nonfiction paperback about sports", "subtype": "distribution", "entries": [{"item": "book_nonf_soft_sports_bdgt", "prob": 1}, {"item": "book_nonf_soft_sports_lads", "prob": 1}, {"item": "book_nonf_soft_sports_vlly", "prob": 1}]}, {"id": "SUS_book_nonf_hard_sports", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns a single hardback book of nonfiction sports", "subtype": "distribution", "entries": [{"item": "book_nonf_hard_sports_hoop", "prob": 1}, {"item": "book_nonf_hard_sports_bike", "prob": 1}, {"item": "book_nonf_hard_sports_ergo", "prob": 1}, {"item": "book_nonf_hard_sports_morg", "prob": 1}]}, {"id": "SUS_book_nonf_hard_arch", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "returns an archery skill training book that is hard bound.", "subtype": "distribution", "entries": [{"item": "manual_archery", "prob": 1}, {"item": "book_archery", "prob": 1}]}, {"id": "SUS_book_nonf_hard_dodge", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a dodge skill training book that is hard bound.", "subtype": "distribution", "entries": [{"item": "manual_dodge", "prob": 1}, {"item": "manual_dodge_kid", "prob": 1}, {"item": "book_nonf_hard_dodge_tlwd", "prob": 1}]}]