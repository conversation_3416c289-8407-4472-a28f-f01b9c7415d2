[{"type": "terrain", "id": "t_carpet_concrete_green_olight", "name": "industrial green carpet", "description": "Firm, low-pile, high-durability carpet in a green color, for laying down on bare concrete, with a still-functioning light attached to the ceiling above.", "symbol": ".", "color": "green", "move_cost": 2, "light_emitted": 120, "looks_like": "t_carpet_green", "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "SUPPORTS_ROOF", "COLLAPSES", "INDOORS", "FLAT", "RUG"], "bash": {"str_min": 4, "str_max": 12, "sound": "glass breaking!", "sound_fail": "whack!", "sound_vol": 16, "sound_fail_vol": 10, "ter_set": "t_carpet_concrete_green", "items": [{"item": "glass_shard", "count": [8, 16]}]}, "shoot": {"chance_to_hit": 50, "reduce_damage": [0, 0], "reduce_damage_laser": [0, 0], "destroy_damage": [2, 8]}}, {"type": "terrain", "id": "t_lixa_wall", "name": "reinforced concrete wall", "looks_like": "t_concrete_wall", "description": "An extremely resilient wall, filled with concrete and rebar.  Best suited for supporting multi-level buildings, only serious explosives and high-speed impacts would be capable of damaging this wall.", "symbol": "#", "color": "light_gray", "move_cost": 0, "coverage": 100, "roof": "t_concrete_roof", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL", "NO_SCENT", "AUTO_WALL_SYMBOL", "MINEABLE", "BLOCK_WIND"], "bash": {"str_min": 120, "str_max": 460, "sound": "scrrrash!", "sound_fail": "whump!", "ter_set": "t_folded_wall"}}, {"type": "terrain", "id": "t_folded_wall", "name": "folded wall", "description": "When looked at out of the side of the eye, this wall seems to bend and curve in on itself and when looked at head on it seems to be a repeating pattern of solid concrete hexagons.", "symbol": "#", "color": "yellow_cyan", "move_cost": 0, "coverage": 100, "roof": "t_concrete_roof", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL", "NO_SCENT", "AUTO_WALL_SYMBOL", "MINEABLE", "BLOCK_WIND"], "bash": {"str_min": 120, "str_max": 460, "sound": "scrrrash!", "sound_fail": "whump!", "ter_set": "t_unfolded_wall"}}, {"type": "terrain", "id": "t_unfolded_wall", "name": "unfolded wall", "description": "This wall tessellates as you view it.", "symbol": "#", "color": "cyan_red", "move_cost": 0, "coverage": 100, "roof": "t_concrete_roof", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL", "NO_SCENT", "AUTO_WALL_SYMBOL", "MINEABLE", "BLOCK_WIND"], "bash": {"str_min": 120, "str_max": 460, "sound": "scrrrash!", "sound_fail": "whump!", "ter_set": "t_lixa_wall"}}, {"type": "terrain", "id": "t_LIXA_tube", "name": "laser chamber", "looks_like": "t_sewage_pipe", "description": "A large section of metal tubing, balanced on spring supports to prevent any vibration from affecting the laser inside.", "symbol": "1", "color": "light_gray", "move_cost": 3, "coverage": 50, "connect_groups": ["INDOORFLOOR", "LIXATUBE"], "connects_to": "LIXATUBE", "flags": ["TRANSPARENT", "MOUNTABLE", "PERMEABLE", "THIN_OBSTACLE", "MINEABLE", "UNSTABLE", "INDOORS"], "bash": {"str_min": 30, "str_max": 210, "sound": "metal screeching!", "sound_fail": "clang!", "ter_set": "t_thconc_floor", "items": [{"item": "scrap", "count": [6, 10]}, {"item": "spring", "count": [10, 20]}]}, "shoot": {"chance_to_hit": 100, "reduce_damage": [0, 0], "reduce_damage_laser": [0, 0], "destroy_damage": [10, 15]}}, {"type": "terrain", "id": "t_LIXA_portal_pulse", "name": "spatial compressor", "description": "A dizzyingly complex series of devices and readouts is on the outside of this machine's casing.  You imagine the inside would be incomprehensible.", "symbol": "{", "color": "light_gray", "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 50, "connect_groups": ["WALL", "INDOORFLOOR"], "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "INDOORS", "PERMEABLE"], "bash": {"str_min": 20, "str_max": 150, "explosive": 7, "ter_set": "t_thconc_floor", "sound_fail": "clang!", "items": [{"item": "processor", "prob": 25}, {"item": "RAM", "count": [4, 6], "prob": 50}, {"item": "cable", "charges": [1, 2], "prob": 50}, {"item": "small_lcd_screen", "prob": 25}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "plastic_chunk", "count": [4, 10], "prob": 50}, {"item": "scrap", "count": [2, 6], "prob": 50}]}, "shoot": {"chance_to_hit": 100, "reduce_damage": [0, 0], "reduce_damage_laser": [0, 0], "destroy_damage": [20, 30]}}, {"type": "terrain", "id": "t_LIXA_unfolded_portal_pulse", "name": "fractal compressor", "description": "This space is infinite, and has no beginning or end.  But you can see both right here, in this churning mass of angles.", "symbol": "{", "color": "light_gray", "light_emitted": 120, "looks_like": "f_machinery_electronic", "move_cost": 0, "coverage": 50, "connect_groups": ["WALL", "INDOORFLOOR"], "connects_to": "WALL", "flags": ["TRANSPARENT", "NOITEM", "WALL", "INDOORS", "PERMEABLE"], "bash": {"str_min": 5, "str_max": 15, "explosive": 10, "ter_set": "t_LIXA_thconc_floor", "sound_fail": "clang!"}, "shoot": {"chance_to_hit": 100, "reduce_damage": [0, 0], "reduce_damage_laser": [0, 0], "destroy_damage": [20, 30]}}, {"type": "terrain", "id": "t_LIXA_unfolded_tube", "name": "unfolded laser chamber", "looks_like": "t_sewage_pipe", "description": "You can see the inside surface of this tube, and what it contains.  Thick, slimy red light oozes within it.", "symbol": "1", "color": "light_gray", "move_cost": 3, "coverage": 50, "connect_groups": ["INDOORFLOOR", "LIXATUBE"], "connects_to": "LIXATUBE", "flags": ["TRANSPARENT", "MOUNTABLE", "PERMEABLE", "INDOORS", "THIN_OBSTACLE", "UNSTABLE", "MINEABLE"], "bash": {"str_min": 7, "str_max": 17, "sound": "a hollow pop, followed by a sickly wet squelching as congealed light slowly leaks out of the tube.", "sound_fail": "nothing.", "ter_set": "t_LIXA_unfolded_tube_broken"}, "shoot": {"chance_to_hit": 100, "reduce_damage": [0, 0], "reduce_damage_laser": [0, 0], "destroy_damage": [10, 15]}}, {"type": "terrain", "id": "t_LIXA_unfolded_tube_broken", "name": "leaking laser chamber", "looks_like": "t_sewage_pipe", "description": "Gobbets of congealed light drip from the gash in the side of this chamber like hot marmalade.", "symbol": "1", "color": "light_gray", "light_emitted": 120, "move_cost": 2, "coverage": 50, "connect_groups": ["INDOORFLOOR", "LIXATUBE"], "connects_to": "LIXATUBE", "flags": ["TRANSPARENT", "MOUNTABLE", "PERMEABLE", "INDOORS", "THIN_OBSTACLE", "UNSTABLE", "MINEABLE"], "emissions": ["emit_congealed_light"]}, {"type": "terrain", "id": "t_LIXA_thconc_floor", "name": "concrete floor", "looks_like": "t_thconc_floor", "description": "You can see every side of every crack in this worn concrete, all the way down.  You can't look at it for too long.", "symbol": ".", "color": "cyan", "move_cost": 2, "roof": "t_concrete_roof", "flags": ["TRANSPARENT", "SUPPORTS_ROOF", "INDOORS", "FLAT", "ROAD"], "connect_groups": ["CONCRETE", "INDOORFLOOR"], "connects_to": "CONCRETE"}, {"type": "terrain", "id": "t_LIXA_strconc_wall", "name": "reinforced concrete wall", "looks_like": "t_concrete_wall", "description": "You can see every side of every crack in this worn concrete, all the way down.  You can't look at it for too long.", "symbol": "#", "color": "light_gray", "move_cost": 0, "coverage": 100, "roof": "t_concrete_roof", "connect_groups": "WALL", "connects_to": "WALL", "flags": ["NOITEM", "SUPPORTS_ROOF", "WALL", "NO_SCENT", "AUTO_WALL_SYMBOL", "BLOCK_WIND"]}, {"type": "terrain", "id": "t_card_LIXA", "name": "card reader", "description": "A smartcard reader.  It bears the letters LIXA in proud silver.", "//": "Science", "symbol": "6", "color": "pink", "looks_like": "t_card_science", "move_cost": 0, "roof": "t_metal_roof", "connect_groups": ["INDOORFLOOR", "WALL"], "connects_to": "WALL", "flags": ["NOITEM", "INDOORS"], "examine_action": {"type": "cardreader", "flags": ["LIXA_SCIENCE_CARD"], "consume_card": false, "allow_hacking": true, "terrain_changes": {"t_door_metal_locked": "t_door_metal_c"}, "query_msg": "Swipe your ID card?", "success_msg": "You insert your ID card.\n<color_light_green>The nearby doors unlock.</color>", "redundant_msg": "The nearby doors are already opened."}, "bash": {"str_min": 18, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_card_reader_broken", "items": [{"item": "plastic_chunk", "count": [0, 2]}, {"item": "scrap", "prob": 50}]}}, {"type": "terrain", "id": "t_card_LIXA_2", "name": "rewired card reader", "description": "A smartcard reader.  The casing is damaged, and repaired with tape.", "//": "Science", "symbol": "6", "color": "pink", "looks_like": "t_card_science", "move_cost": 0, "roof": "t_metal_roof", "connect_groups": ["INDOORFLOOR", "WALL"], "connects_to": "WALL", "flags": ["NOITEM", "INDOORS"], "examine_action": {"type": "cardreader", "flags": ["LIXA_SCIENCE_CARD_2"], "consume_card": false, "allow_hacking": false, "terrain_changes": {"t_door_metal_locked": "t_door_metal_c"}, "query_msg": "Swipe your ID card?", "success_msg": "You insert your ID card.\n<color_light_green>The nearby doors unlock.</color>", "redundant_msg": "The nearby doors are already opened."}, "bash": {"str_min": 14, "str_max": 140, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_card_reader_broken", "items": [{"item": "plastic_chunk", "count": [0, 2]}, {"item": "scrap", "prob": 50}]}}, {"type": "terrain", "id": "t_card_LIXA_3", "name": "PPE lockup card reader", "description": "A smartcard reader.  The label above it reads 'SECURE PPE LOCKER'.", "//": "Science", "symbol": "6", "color": "pink", "looks_like": "t_card_science", "move_cost": 0, "roof": "t_metal_roof", "connect_groups": ["INDOORFLOOR", "WALL"], "connects_to": "WALL", "flags": ["NOITEM", "INDOORS"], "examine_action": {"type": "cardreader", "flags": ["LIXA_SCIENCE_CARD_3"], "consume_card": false, "allow_hacking": false, "terrain_changes": {"t_door_metal_locked": "t_door_metal_c"}, "query_msg": "Swipe your ID card?", "success_msg": "You insert your ID card.\n<color_light_green>The nearby doors unlock.</color>", "redundant_msg": "The nearby doors are already opened."}, "bash": {"str_min": 14, "str_max": 140, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_card_reader_broken", "items": [{"item": "plastic_chunk", "count": [0, 2]}, {"item": "scrap", "prob": 50}]}}, {"type": "terrain", "id": "t_card_LIXA_mil", "name": "card reader", "description": "A smartcard reader.  It is a forbidding black, with no symbols of any sort to identify it.", "//": "Science", "symbol": "6", "color": "dark_gray", "looks_like": "t_card_military", "move_cost": 0, "roof": "t_metal_roof", "connect_groups": ["INDOORFLOOR", "WALL"], "connects_to": "WALL", "flags": ["NOITEM", "INDOORS"], "examine_action": {"type": "cardreader", "flags": ["LIXA_MILITARY_CARD"], "consume_card": false, "allow_hacking": false, "terrain_changes": {"t_door_metal_locked": "t_door_metal_c"}, "query_msg": "Swipe your ID card?", "success_msg": "You insert your ID card.\n<color_light_green>The nearby doors unlock.</color>", "redundant_msg": "The nearby doors are already opened."}, "bash": {"str_min": 18, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "ter_set": "t_card_reader_broken", "items": [{"item": "plastic_chunk", "count": [0, 2]}, {"item": "scrap", "prob": 50}]}}, {"type": "terrain", "id": "t_LIXA_laser", "name": "precision laser emitter", "description": "A huge casing for a device that generates and focuses a laser of an incredibly precise wavelength.", "symbol": "$", "color": "yellow", "looks_like": "t_machinery_electronic", "move_cost": 3, "roof": "t_metal_roof", "flags": ["TRANSPARENT", "FLAMMABLE", "FLAT"], "connect_groups": ["INDOORFLOOR", "LIXATUBE"], "connects_to": "LIXATUBE", "deconstruct": {"ter_set": "t_metal_floor", "items": [{"item": "lc_wire", "count": [1, 3]}, {"item": "pipe", "count": [1, 2]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "processor", "count": 1}, {"item": "RAM", "count": [1, 4]}, {"item": "cable", "charges": [1, 4]}, {"item": "lens", "count": 8}, {"item": "e_scrap", "count": [5, 10]}, {"item": "circuit", "count": [3, 8]}, {"item": "power_supply", "count": [1, 3]}, {"item": "amplifier", "count": [1, 3]}, {"item": "plastic_chunk", "count": [2, 8]}, {"item": "scrap", "count": [1, 5]}]}}, {"type": "furniture", "id": "f_LIXA_unfold_barrier", "name": "wall of flat space", "looks_like": "fd_fatigue", "description": "From a distance, this looks like where the chamber ends; a flat wall, with a very detailed painting of a tunnel behind it.  But as you get closer, you can see how it ripples, like lazy water, and notice angles of the space beyond that a flat image could never give.", "symbol": "@", "color": "light_gray", "move_cost_mod": 1, "coverage": 0, "required_str": -1, "flags": ["TRANSPARENT"]}, {"type": "furniture", "id": "f_LIXA_machinery_electronic", "name": "unfolded machinery", "looks_like": "t_machinery_electronic", "description": "This inscrutable device is filled with wires, boards, and connections.  You can see every one of them, and it gives you a headache.", "symbol": "$", "color": "yellow", "move_cost_mod": 8, "coverage": 55, "required_str": -1, "flags": ["TRANSPARENT", "CONTAINER", "SEALED", "FLAMMABLE", "PLACE_ITEM"]}, {"type": "furniture", "id": "f_LIXA_electrical_conduit", "name": "photonic conduit", "looks_like": "f_electrical_conduit", "description": "You can see through the metal casing of this tube, into the blazing light coursing through it.", "symbol": "|", "color": "light_gray", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "NOCOLLIDE"]}, {"type": "furniture", "id": "f_lixa_whiteboard", "name": "whiteboard", "description": "A wide space of whiteboard, for writing on with washable markers.  No one has washed it in a long time, though; it is covered in dense scrawls of equations and waveform diagrams.", "symbol": "6", "color": "blue", "looks_like": "f_bulletin", "move_cost_mod": -1, "coverage": 75, "required_str": -1, "flags": ["FLAMMABLE", "ORGANIC", "TRANSPARENT"], "max_volume": "120 ml", "deconstruct": {"items": [{"item": "2x4", "count": 4}, {"item": "nail", "charges": [4, 8]}]}, "bash": {"str_min": 3, "str_max": 40, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "2x4", "count": [0, 3]}, {"item": "nail", "charges": [4, 6]}, {"item": "splinter", "count": [1, 4]}]}}]