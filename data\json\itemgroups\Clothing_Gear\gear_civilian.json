[{"type": "item_group", "id": "everyday_gear", "ammo": 75, "magazine": 100, "//": "Small common items carried in a person's pockets, purse, etc. Wallets at roughly 50%.", "items": [{"group": "wallets", "prob": 1600}, {"item": "cell_phone", "prob": 26, "charges": [0, 300]}, {"item": "smart_phone", "prob": 204, "charges": [0, -1]}, {"prob": 250, "group": "cig_box_cigarette_1_20"}, {"item": "lighter", "prob": 50, "charges": [0, -1]}, {"item": "small_lighter", "prob": 25, "charges": [0, -1]}, ["joint", 10], {"item": "ref_lighter", "prob": 50, "charges": [0, -1]}, {"item": "matches", "prob": 50, "charges": [0, 20]}, {"item": "ref_matches", "prob": 50, "charges": [0, 32]}, {"group": "tobacco_products", "prob": 60}, {"item": "permanent_marker", "prob": 10, "charges": [0, -1]}, ["dog_whistle", 5], ["multitool", 1], ["cane", 25], ["sinister_cane", 1], ["wizard_cane_cheap", 1], ["wizard_cane", 1], ["pom_poms", 5], ["pocketwatch", 50], ["pockknife", 50], ["knife_folding", 30], ["knife_swissarmy", 10], ["machete_gimmick", 5], ["teleumbrella", 20], ["umbrella", 20], {"prob": 100, "group": "bottle_otc_painkiller_1_20"}, {"prob": 100, "group": "vitamins_bottle_plastic_pill_supplement_1_20"}, ["eyedrops", 50], ["steroid_eyedrops", 5], {"item": "inhaler", "prob": 50, "charges": [10, 100]}, {"item": "flashlight", "prob": 50, "charges": [0, 300]}, {"item": "heavy_flashlight", "prob": 15, "charges": [0, 300]}, {"item": "camera", "prob": 20, "charges": [0, 150]}, {"item": "camera", "container-item": "camera_bag", "prob": 10, "charges": [0, 150]}, {"item": "camera_pro", "prob": 1, "charges": [0, 150]}, {"item": "camera_pro", "container-item": "camera_bag", "prob": 4, "charges": [0, 150]}, ["camera_bag", 10], {"item": "eink_tablet_pc", "prob": 30, "charges": [0, 100]}, {"item": "laptop", "prob": 15, "charges": [0, 500]}, ["memory_card_unread", 50], ["usb_drive", 50], {"item": "mp3", "prob": 20, "charges": [0, 100]}, {"item": "portable_game", "prob": 50, "charges": [0, 100]}, {"item": "vibrator", "prob": 5, "charges": [0, 100]}, {"item": "gum", "prob": 50, "count": [0, 10]}, {"group": "ammo_pocket_batteries", "prob": 50}, ["wrapper", 50], ["wrapper_foil", 5], ["string_6", 50], ["pocket_firstaid", 5], ["pocket_survival", 5], ["pocket_firearms", 5], ["newest_newspaper", 75], ["flyer", 10], ["thermos", 5], ["hairbrush", 25], ["comb_pocket", 50], ["ankle_wallet_pouch", 1], {"group": "lunchbox_with_contents", "prob": 50}, {"item": "teargas_sprayer", "prob": 10, "charges": [1, 10]}]}, {"id": "gear_homeless", "type": "item_group", "//": "Regular items for the homeless.", "items": [{"group": "trash", "prob": 30}, {"group": "donated_clothes", "prob": 25}, {"group": "gear_survival", "prob": 5}, {"group": "contraband", "prob": 20}, {"group": "prison_textile", "prob": 7, "damage": [1, 2]}, {"group": "novels", "prob": 10}, {"group": "misc_smoking", "prob": 10}, {"group": "stoner", "prob": 5}, {"group": "cannedfood", "prob": 10, "count": [1, 3]}, {"group": "wallet_duct_tape_full", "prob": 1}, {"item": "ankle_wallet_pouch", "prob": 1}, {"item": "drink_hobo", "prob": 1}]}, {"type": "item_group", "id": "child_items_pockets", "//": "Stuff that kids would have in their pockets or otherwise carry on them when they turned. NOT for items that they could have been carrying in their hands - those would have fallen out of their grasp once they died.", "items": [{"group": "candy_chocolate", "prob": 200}, {"group": "toastems", "prob": 25}, {"prob": 80, "group": "cookies_box_snack_1_4"}, {"prob": 10, "group": "licorice_bag_plastic_1_4"}, {"item": "mp3", "prob": 20, "charges": [0, 100]}, {"item": "portable_game", "prob": 30, "charges": [0, 100]}, {"group": "softdrinks_canned", "prob": 55}, {"group": "softdrinks_bottled", "prob": 30}, ["backpack", 48], ["backpack_denim", 8], ["purse", 20], ["slingpack", 19], ["travelpack", 10], ["petpack", 1], ["armrig", 3], ["leg_small_bag", 3], ["leg_bag", 1], ["bookstrap", 1], ["pockknife", 14], ["knife_swissarmy", 10], ["teleumbrella", 2], ["usb_drive", 10], {"group": "writing_utensils", "prob": 40}, {"group": "child_schoolbag_1", "prob": 500}, {"group": "child_schoolbag_2", "prob": 150}, {"group": "child_schoolbag_3", "prob": 150}, {"group": "child_bugout_bag_1", "prob": 100}, {"group": "child_bugout_bag_2", "prob": 25}, {"group": "child_bugout_bag_3", "prob": 5}, ["novel_pulp", 16], ["elbow_pads", 20], ["knee_pads", 20], {"item": "radiocontrol", "prob": 5, "charges": [0, 100]}, ["RPG_die", 1], ["metal_RPG_die", 1], ["deck_of_cards", 1]]}, {"type": "item_group", "id": "child_items", "items": [{"group": "candy_chocolate", "prob": 200}, {"group": "toastems", "prob": 25}, {"prob": 80, "group": "cookies_box_snack_1_4"}, {"prob": 10, "group": "licorice_bag_plastic_1_4"}, ["bat", 40], ["bat_nerf", 20], ["bat_metal", 20], ["hockey_stick", 10], {"item": "mp3", "prob": 20, "charges": [0, 100]}, {"item": "portable_game", "prob": 30, "charges": [0, 100]}, {"group": "softdrinks_canned", "prob": 55}, {"group": "softdrinks_bottled", "prob": 30}, ["backpack", 48], ["backpack_denim", 8], ["purse", 20], ["slingpack", 19], ["travelpack", 10], ["petpack", 1], ["armrig", 3], ["leg_small_bag", 3], ["leg_bag", 1], ["bookstrap", 1], ["pockknife", 14], ["knife_swissarmy", 10], ["umbrella", 5], ["teleumbrella", 2], ["usb_drive", 10], {"group": "writing_utensils", "prob": 40}, {"group": "child_schoolbag_1", "prob": 500}, {"group": "child_schoolbag_2", "prob": 150}, ["novel_pulp", 16], {"group": "toy_radio_car", "prob": 1}, {"group": "toy_makerspace_kit", "prob": 1}, {"group": "toy_robot_kit", "prob": 1}, {"group": "toy_engine_kit", "prob": 1}, {"item": "air_launcher", "prob": 1}, ["mouthpiece", 3], ["basketball", 8], ["beach_volleyball", 8], ["indoor_volleyball", 8], ["football", 8], ["elbow_pads", 20], ["knee_pads", 20], {"item": "radiocontrol", "prob": 5, "charges": [0, 100]}, ["slingshot", 10], ["frisbee", 10], ["dnd_handbook", 1], ["RPG_die", 1], ["metal_RPG_die", 1], ["balloon", 5], ["deck_of_cards", 1], ["milkshake_fastfood", 3], {"group": "butterfly_net_any", "prob": 5}]}, {"id": "child_hats", "type": "item_group", "//": "Hats for zombie children.", "items": [{"item": "helmet_football", "prob": 30}, {"item": "helmet_ball", "prob": 30}, {"item": "hat_ball", "prob": 10}, {"item": "hat_golf", "prob": 5}, {"item": "helmet_bike", "prob": 70}, {"item": "helmet_riding", "prob": 5}, {"group": "helmet_bike_modified", "prob": 10}]}, {"id": "child_shoes", "type": "item_group", "//": "Shoes for zombie children.", "items": [{"item": "roller_shoes_off", "prob": 30}, {"item": "roller_shoes_on", "prob": 25}, {"item": "rollerskates", "prob": 40}, {"item": "roller_blades", "prob": 35}, {"item": "sneakers", "prob": 50}, {"item": "lowtops", "prob": 40}, {"item": "dance_shoes", "prob": 25}, {"item": "slippers", "prob": 30}, {"item": "flip_flops", "prob": 35}, {"item": "flip_flops_exp", "prob": 5}, {"item": "leathersandals", "prob": 40}, {"item": "espadrilles", "prob": 20}, {"item": "bastsandals", "prob": 20}, {"item": "sneakers_steel", "prob": 20}, {"item": "knee_high_boots", "prob": 20}, {"item": "thigh_high_boots", "prob": 15}, {"item": "boots", "prob": 25}]}, {"id": "child_schoolbag_1", "type": "item_group", "container-item": "backpack", "on_overflow": "spill", "items": [{"group": "child_schoolbag_contents"}]}, {"id": "child_schoolbag_2", "type": "item_group", "container-item": "backpack_leather", "on_overflow": "spill", "items": [{"group": "child_schoolbag_contents"}]}, {"id": "child_schoolbag_3", "type": "item_group", "container-item": "backpack_denim", "on_overflow": "spill", "items": [{"group": "child_schoolbag_contents"}]}, {"id": "child_schoolbag_contents", "type": "item_group", "subtype": "collection", "//": "A list of items that would have had a decent chance to appear in a pre-apocalypse child schoolbag.", "items": [{"group": "pencil_case_with_contents", "prob": 70}, {"group": "book_school", "prob": 65}, {"group": "book_school", "prob": 55}, {"group": "kids_books", "prob": 15}, {"group": "lunchbox_with_contents", "prob": 45}, {"group": "candy_chocolate", "prob": 12}, {"group": "softdrinks_canned", "prob": 5}, {"group": "softdrinks_bottled", "prob": 7}, {"group": "bugout_toys", "prob": 2}]}, {"id": "lunchbox_with_contents", "type": "item_group", "container-item": "lunchbox", "on_overflow": "spill", "items": [{"group": "lunchbox_contents", "prob": 7}]}, {"id": "lunchbox_contents", "type": "item_group", "subtype": "collection", "//": "A list of items that would have had a decent chance to appear in a pre-apocalypse lunchbox, be it child or adult (but I did a pretty poor job at variety, I am not sure what could actually go there).", "items": [{"group": "lunchbox_food", "prob": 90}, {"group": "lunchbox_fruit", "prob": 35}, {"item": "plastic_fork", "prob": 30}, {"item": "plastic_knife", "prob": 30}, {"item": "plastic_spoon", "prob": 15}, {"item": "napkin", "prob": 15, "count": [1, 5]}]}, {"type": "item_group", "id": "npc_hacker", "items": [["energy_drink", 55], ["energy_drink_atomic", 12], ["mag_computer", 45], ["mag_electronics", 25], ["colamdew", 50], ["adderall", 10], ["fries", 10], ["cheese_fries", 10], ["onion_rings", 10], ["mintpatties", 20], {"item": "electrohack", "prob": 3, "charges": [0, 100]}, ["usb_drive", 5], {"group": "ammo_pocket_batteries_full", "prob": 50}, ["file", 10], ["manual_computer", 20], ["hackerman_computer", 10], ["textbook_computer", 8], ["textbook_anarch", 4], ["computer_science", 7], ["mask_guy_fawkes", 5], ["solar_cell", 5], ["SICP", 3], ["recipe_serum", 2], ["recipe_chimera", 1], ["recipe_labchem", 1], {"item": "soldering_iron_portable", "prob": 70, "charges": [0, 50]}, ["solder_wire", 70], ["textbook_weapwest", 2], ["textbook_weparabic", 2], ["textbook_weapeast", 2], ["textbook_armwest", 2], ["textbook_armeast", 2], ["textbook_armschina", 2], ["textbook_mesoam", 2], ["ballista_book", 2], ["bronze_book", 2], ["bronze_mag", 2], ["radio_book", 22], {"group": "toy_radio_car", "prob": 1}, {"group": "toy_makerspace_kit", "prob": 1}, {"group": "toy_robot_kit", "prob": 1}, {"group": "toy_engine_kit", "prob": 1}, {"item": "radiocontrol", "prob": 5, "charges": [0, 100]}]}, {"type": "item_group", "id": "traveler", "items": [["daypack", 20], ["roadmap", 6], ["trailmap", 6], ["touristmap", 4], {"item": "lighter", "prob": 60, "charges": [0, -1]}, {"prob": 40, "group": "cig_box_cigarette_1_20"}, ["blazer", 15], ["jeans", 90], ["under_armor", 15], ["under_armor_shorts", 15], ["shorts", 80], ["tshirt", 80], ["bandana", 15], ["towel", 90], {"group": "wallets", "prob": 90}, ["ankle_wallet_pouch", 40], ["longshirt", 80], ["shorts_cargo", 50], ["pants_cargo", 70], ["hoodie", 65], ["skirt", 75], ["skirt_long", 60], ["skirt_denim", 10], ["jacket_light", 50], ["jacket_windbreaker", 25], ["jacket_varsity", 20], ["jacket_jean", 30], ["vest_jean", 15], ["jacket_leather", 30], ["leather_police_jacket", 10], ["gosling_jacket", 15], ["bra", 30], ["undershirt", 30], ["boxer_shorts", 30], ["briefs", 15], ["boxer_briefs", 20], ["swim_briefs", 10], ["speedo", 5], {"group": "bikini", "prob": 15}, ["panties", 30], ["boy_shorts", 25], ["sweatshirt", 75], ["backpack", 38], ["backpack_denim", 8], ["travelpack", 10], ["slingpack", 19], ["suitcase_m", 99], ["suitcase_l", 55], ["duffelbag", 20], ["pockknife", 14], ["knife_folding", 6], ["knife_swissarmy", 6], ["multitool", 8], {"distribution": [{"group": "full_survival_kit"}, {"group": "used_survival_kit"}], "prob": 3}, ["touring_suit", 20], ["helmet_motor", 14], ["motorbike_armor", 10], ["motorbike_pants", 10], ["motorbike_boots", 10], ["leg_bag", 10], {"item": "shavingkit", "prob": 30, "charges": [0, -1]}, {"item": "elec_hairtrimmer", "prob": 15, "charges": [0, 50]}, ["razor_blade", 5], ["razor_shaving", 5], ["toothbrush_plain", 10], ["string_floss", 10], ["kilt", 4], ["hobo_stove", 1], ["tinder", 1], {"item": "tinderbox", "prob": 1, "charges": [0, 100]}, ["flint_steel", 5], ["bottle_metal", 5], ["wallet_travel", 10], {"item": "teargas_sprayer", "prob": 5, "charges": 10}]}, {"type": "item_group", "id": "licorice_bag_plastic_1_4", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic_small", "entries": [{"item": "licorice", "container-item": "null", "count": [1, 4]}]}]