[{"type": "furniture", "id": "f_mutpoppy", "name": "mutated poppy flower", "description": "These strange flowers have appeared in the wake of the Cataclysm, and their buds can be used for medicinal purposes, like the seeds of the mundane poppy they're named after.  The dirt around it gently churns as its roots writhe beneath the soil, and it's surrounded by an overwhelming floral smell that makes you feel sleepy.", "symbol": "f", "color": "light_red", "looks_like": "f_dandelion", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "flower_poppy", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_dandelion", "name": "dandelion", "description": "A common weed with a yellow flower.  Produces seeds that get carried on the wind by thin, gray filaments.", "symbol": "f", "color": "yellow", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "dandelion_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_burdock", "name": "burdock", "description": "A common weed with a purple thistle-like flower.  Its seeds tend to stick to clothing and fur.", "symbol": "f", "color": "magenta", "looks_like": "f_dandelion", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "autumn"], "id": "burdock_harv"}, {"seasons": ["summer"], "id": "burdock_summer_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_carrot_wild", "name": "wild carrot", "description": "A common white wildflower which could be harvested for its root.", "symbol": "f", "color": "red", "looks_like": "f_lily", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer"], "id": "wild_carrot_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_maianthemum_stellatum", "name": "starry false Solomon's seal", "description": "The blossoms of this flower look like stars in the night sky.  Its crimson berries are edible.", "symbol": "f", "color": "white", "looks_like": "f_lily", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["autumn", "winter"], "id": "m_stellatum_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_salsify", "name": "salsify", "description": "Tragopogon porrifolius, a ragged-looking purple flower with a nutritious root.", "symbol": "f", "color": "light_red", "looks_like": "f_dahlia", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "//": "Root can always be harvested, but there is no flower to harvest seeds from in winter.", "harvest_by_season": [{"seasons": ["summer", "autumn"], "id": "salsify_seed_harv"}, {"seasons": ["autumn", "winter"], "id": "salsify_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_chamomile", "name": "chamomile", "description": "A cute white flower with a yellow center, used to brew a soothing tea.", "symbol": "f", "color": "white", "looks_like": "f_dandelion", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn", "harvest_by_season": [{"seasons": ["summer", "autumn"], "id": "chamomille_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_flower_tulip", "name": "tulip", "description": "A bright, colorful flower with petals forming a small cup at its top.  It can be poisonous to livestock.", "symbol": "f", "looks_like": "f_mutpoppy", "color": "light_blue", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "generic_flower_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_flower_spurge", "name": "spurge flower", "description": "A yellow-green flower that grows in densely-packed bushes.", "symbol": "f", "looks_like": "f_dandelion", "color": "light_green", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "spurge_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_cattails", "name": "cattails", "description": "This useful plant is available all year round.  Many parts of the plant are edible.", "symbol": "i", "color": "brown", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC", "SMALL_HIDE"], "examine_action": "harvest_furn", "harvest_by_season": [{"seasons": ["winter"], "id": "cattails_winter_harv"}, {"seasons": ["spring", "summer", "autumn"], "id": "cattails_summer_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_black_eyed_susan", "name": "black eyed Susan", "description": "A mildly poisonous yellow flower with a dark ball in the middle.  Sometimes known as an ox-eye daisy.", "looks_like": "f_dandelion", "symbol": "f", "color": "light_green", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "generic_flower_harv"}], "//": "Add flower and bud spawns once useful.", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_lily", "name": "lily", "description": "A pretty flower that comes in a variety of colors.", "looks_like": "f_flower_tulip", "symbol": "f", "color": "pink", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "generic_flower_harv"}], "//": "Add flower and bud spawns once useful.", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_lotus", "name": "lotus", "description": "A lovely flower that grows on the surface of bodies of fresh water.  A traditional symbol of many Eastern cultures.", "looks_like": "f_lily", "symbol": "f", "color": "yellow", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "lotus_harv"}, {"seasons": ["summer"], "id": "lotus_summer_harv"}, {"seasons": ["autumn"], "id": "lotus_autumn_harv"}], "//": "Add flower and bud spawns once useful.", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_sunflower", "name": "sunflower", "description": "A tall, wide-headed flower with a large dark center.  Produces many nutritious seeds.", "looks_like": "f_dandelion", "symbol": "f", "color": "yellow", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["summer"], "id": "sunflower_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_jerusalem_artichoke", "name": "Jerusalem artichoke plant", "description": "Looking similar to a sunflower, this tall plant has a nutritious tuber.", "looks_like": "f_sunflower", "symbol": "f", "color": "yellow", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["autumn", "winter"], "id": "jerusalem_artichoke_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_lilypad", "name": "lilypad", "description": "This lilypad doesn't look like it would support the weight of the things you've heard croaking in the swamp.", "symbol": "f", "color": "green", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "generic_flower_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_bluebell", "name": "bluebell", "description": "A common bluebell flower.  Pretty.", "looks_like": "f_dandelion", "symbol": "f", "color": "blue", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "bluebell_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_dahlia", "name": "dahlia", "description": "A puffy flower with many tightly-layered petals.", "symbol": "f", "color": "magenta", "looks_like": "f_dandelion", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "flower_dahlia", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_chicory", "name": "chicory", "description": "A blue flower imported from Europe, also known as a cornflower.", "symbol": "f", "color": "cyan", "looks_like": "f_bluebell", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "chicory_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_datura", "name": "datura", "description": "A pretty moonflower.", "symbol": "*", "color": "light_green", "looks_like": "f_dandelion", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC", "GRAZER_INEDIBLE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "datura_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_leaves_pile", "name": "pile of leaves", "symbol": "#", "description": "A sizable pile of leaves.  You could sleep on it if you don't care about comfort or warmth.", "color": "brown", "looks_like": "t_dirtpile", "move_cost_mod": 3, "coverage": 35, "comfort": 1, "floor_bedding_warmth": 50, "required_str": -1, "deconstruct": {"items": [{"item": "leaves", "count": 100}]}, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "ORGANIC", "MOUNTABLE", "SHORT", "EASY_DECONSTRUCT", "SMALL_HIDE"], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch!", "sound_fail": "whump.", "items": [{"item": "leaves", "count": [62, 75]}]}}, {"type": "furniture", "id": "f_mustard", "name": "mustard", "description": "A yellow flower known as Brassica Nigra.  Known for its seeds that can be used for making table mustard.", "symbol": "f", "color": "yellow", "looks_like": "f_dandelion", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn", "harvest_by_season": [{"seasons": ["summer", "autumn"], "id": "mustard_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish."}}, {"type": "furniture", "id": "f_wild_sarsaparilla", "name": "wild sarsaparilla", "description": "A small, flowering plant known as '<PERSON>lia nudicaulis'.  It is colored in vibrant shades of orange and red in autumn, making it easy to spot.", "symbol": ",", "looks_like": "f_lily", "color": ["brown_green", "green", "red", "brown"], "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["autumn"], "id": "wild_sarsaparilla_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "twig", "prob": 50}]}}, {"type": "furniture", "id": "f_wintergreen", "name": "wintergreen", "description": "A small shrub native to North America.  The leaves, which remain green even in winter, are valued for medicinal purposes.  It produces edible and sweet berries, too!", "symbol": ",", "looks_like": "f_mutpoppy", "//": "green all year long", "color": "green", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLAMMABLE_ASH", "NOCOLLIDE", "FLOWER"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["autumn", "winter"], "id": "wintergreen_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}]}}, {"type": "furniture", "id": "f_japanese_knotweed", "name": "Japanese knotweed", "description": "An invasive species originating from Japan.  The stem looks like it might contain some water.", "symbol": ",", "looks_like": "f_cattails", "color": ["brown_green", "green", "green", "brown"], "move_cost_mod": 3, "coverage": 70, "required_str": -1, "flags": ["TRANSPARENT", "SHORT", "SHRUB", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring"], "id": "japanese_knotweed_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "leaves", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_fireweed", "name": "fireweed", "description": "Vibrant purple, it tends to be the first to appear after fires, hence its name.", "symbol": ",", "looks_like": "f_dahlia", "move_cost_mod": 0, "color": ["brown_green", "magenta", "magenta", "brown"], "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLOWER", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["summer", "autumn"], "id": "generic_edible_flower_spring_harv"}, {"seasons": ["summer", "autumn"], "id": "generic_edible_flower_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "leaves", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_selfheal", "name": "common selfheal", "description": "A small purple flower.  It got its name due to its usage in traditional medicine.", "symbol": ",", "looks_like": "f_dahlia", "color": ["brown_green", "magenta", "magenta", "brown"], "required_str": -1, "move_cost_mod": 0, "flags": ["TRANSPARENT", "TINY", "FLOWER", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["summer", "autumn"], "id": "generic_edible_flower_spring_harv"}, {"seasons": ["summer", "autumn"], "id": "generic_edible_flower_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "leaves", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_dogbane", "name": "spreading dogbane", "description": "It is not only toxic to dogs when eaten by them.  Humans and many other animals are affected, too.  Its fibers sure are worth salvaging though.  Despite its name, it is not actually spreading fast.", "symbol": ",", "looks_like": "f_chamomile", "color": ["brown_green", "green", "green", "brown"], "required_str": -1, "move_cost_mod": 0, "flags": ["TRANSPARENT", "SHORT", "SHRUB", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "dogbane_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "leaves", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_thistle", "name": "thistle", "looks_like": "f_burdock", "description": "A prickly and invasive small plant.", "symbol": ",", "color": ["brown_green", "green", "green", "brown"], "required_str": -1, "move_cost_mod": 0, "flags": ["TRANSPARENT", "TINY", "FLOWER", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["spring", "summer", "autumn"], "id": "thistle_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}]}}, {"type": "furniture", "id": "f_purple_loosestrife", "name": "purple loosestrife", "looks_like": "f_dahlia", "description": "A pretty violet flower that likes water.  Unfortunately it is invasive and does not benefit the environment.", "symbol": ",", "color": ["brown_green", "magenta", "magenta", "brown"], "required_str": -1, "move_cost_mod": 0, "flags": ["TRANSPARENT", "SHORT", "FLOWER", "FLAMMABLE_ASH", "NOCOLLIDE", "ORGANIC"], "examine_action": "harvest_furn_nectar", "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}]}}, {"type": "furniture", "id": "f_wild_rice", "name": "wild rice", "description": "Also called Indian rice, Northern wild rice, or manoomin in Ojibwe, is a species of rice that has been harvested and used by Native American tribes of the northeast for hundreds of years.  It's similar to other types of wild rice, offering a fiber-rich and healthy basis of a meal.", "symbol": "f", "looks_like": "f_cattails", "color": ["brown_green", "green", "green", "brown"], "move_cost_mod": 3, "coverage": 70, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "DIGGABLE", "FLAT", "THIN_OBSTACLE", "SHRUB", "SMALL_HIDE"], "examine_action": "harvest_furn_nectar", "harvest_by_season": [{"seasons": ["autumn"], "id": "wild_rice_harv"}], "bash": {"str_min": 2, "str_max": 6, "sound": "crunch.", "sound_fail": "whish.", "items": [{"item": "withered", "prob": 50, "count": [1, 2]}, {"item": "leaves", "count": [2, 4]}]}}, {"type": "furniture", "id": "f_hedge_short", "name": "short hedge", "description": "A shrub with small green leaves, trimmed into a squared shape.", "symbol": "#", "color": "green", "looks_like": "t_shrub", "move_cost_mod": 3, "coverage": 50, "required_str": -1, "connect_groups": "WOODFENCE", "connects_to": "WOODFENCE", "flags": ["TRANSPARENT", "FLAMMABLE_ASH", "ORGANIC", "PERMEABLE", "SMALL_HIDE"], "bash": {"str_min": 10, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [2, 3]}, {"item": "splinter", "count": [5, 10]}, {"item": "leaves", "count": [5, 8]}]}}, {"type": "furniture", "id": "f_hedge_tall", "name": "tall hedge", "description": "A tall shrub with small green leaves, trimmed into a squared shape.  You can't see past this thick hedgerow.", "symbol": "#", "color": "green", "looks_like": "t_shrub", "move_cost_mod": -1, "coverage": 90, "required_str": 14, "connect_groups": "WOODFENCE", "connects_to": "WOODFENCE", "flags": ["FLAMMABLE_ASH", "ORGANIC", "PERMEABLE", "BLOCK_WIND", "NOITEM", "SMALL_HIDE"], "bash": {"str_min": 16, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 6]}, {"item": "splinter", "count": [8, 16]}, {"item": "leaves", "count": [7, 10]}]}}, {"type": "furniture", "id": "f_topiary_abstract", "name": "abstract topiary", "description": "A tall shrub with small green leaves, trimmed into a fanciful abstract shape.", "symbol": "^", "color": "green", "looks_like": "t_shrub_hydrangea", "move_cost_mod": 4, "coverage": 60, "required_str": -1, "flags": ["FLAMMABLE_ASH", "ORGANIC", "PERMEABLE"], "bash": {"str_min": 10, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 6]}, {"item": "splinter", "count": [8, 16]}, {"item": "leaves", "count": [7, 10]}]}}, {"type": "furniture", "id": "f_topiary_animal", "name": "animal topiary", "description": "A tall shrub with small green leaves, trimmed into an animal shape.", "symbol": "^", "color": "green", "looks_like": "t_shrub_hydrangea", "move_cost_mod": 4, "coverage": 60, "required_str": -1, "flags": ["FLAMMABLE_ASH", "ORGANIC", "PERMEABLE"], "bash": {"str_min": 10, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 6]}, {"item": "splinter", "count": [8, 16]}, {"item": "leaves", "count": [7, 10]}]}}, {"type": "furniture", "id": "f_topiary_dragon", "name": "animal topiary", "description": "A tall shrub with small green leaves, trimmed into a dragon shape.", "symbol": "^", "color": "green", "looks_like": "t_shrub_hydrangea", "move_cost_mod": 4, "coverage": 60, "required_str": -1, "flags": ["FLAMMABLE_ASH", "ORGANIC", "PERMEABLE"], "bash": {"str_min": 10, "str_max": 180, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "stick_long", "count": [3, 6]}, {"item": "splinter", "count": [8, 16]}, {"item": "leaves", "count": [7, 10]}]}}, {"type": "furniture", "id": "f_beaver_dam", "name": "dam", "description": "A house that beavers built.  The entrance is underwater making it harder for predators to access.", "symbol": "#", "color": "brown", "looks_like": "t_wall_log", "move_cost_mod": 2, "required_str": -1, "flags": ["FLAMMABLE_ASH", "TRANSPARENT"], "bash": {"str_min": 6, "str_max": 12, "sound": "crunch.", "sound_fail": "bonk.", "items": [{"item": "twig", "count": [5, 12]}, {"item": "stick", "count": [5, 12]}, {"item": "stick_long", "count": [5, 12]}, {"item": "splinter", "count": [12, 38]}]}}]