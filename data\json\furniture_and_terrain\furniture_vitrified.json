[{"type": "furniture", "id": "f_glassedbody_1", "name": "black glass figure", "description": "A black glass figure of a someone frozen halfway through falling backwards, and suspended on a single leg.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -1, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_2", "name": "black glass figure", "description": "A black glass figure of a person covering their face with their hands.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_3", "name": "black glass figure", "description": "A black glass figure of a child.  It is difficult to make out their facial features, but it seems as if they are smiling.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_4", "name": "black glass figure", "description": "A black glass figure of someone kneeling in prayer.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_5", "name": "black glass figure", "description": "A black glass figure of someone in a firing posture.  Their jagged fingers are frozen squeezing a trigger that isn't there.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_6", "name": "black glass figure", "description": "A black glass figure of someone leaning into the space to their right, their fingers bent as if interlaced with someone else's.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_7", "name": "black glass figure", "description": "A black glass figure of someone clutching at their neck.  A delicate spray of connected glass droplets emerges between their fingers.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_8", "name": "black glass figure", "description": "Most of a black glass human figure.  The face and much of the head is missing, as if scooped out, and the inner surface is perfectly smooth.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_9", "name": "black glass figure", "description": "A black glass figure of someone doubled over in laughter.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_10", "name": "black glass figure", "description": "A black glass figure.  Their features are too jagged to make out clearly, but they remind you of someone you knew.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_glassedbody_11", "name": "black glass figure", "description": "A black glass figure of someone in a fighting stance, their face twisted with howls of rage and anger.  They're gripping some sort of weapon that isn't there.", "symbol": "@", "color": "dark_gray", "looks_like": "f_statue", "move_cost_mod": -3, "coverage": 50, "required_str": 20, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE"], "bash": {"str_min": 16, "str_max": 40, "sound": "crunch!", "sound_fail": "tink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_bed", "name": "black glass bed", "symbol": "#", "description": "A black glass sculpture of a bed.  The rumpled covers are jagged, and splinter at the touch.", "color": "dark_gray", "move_cost_mod": 3, "coverage": 40, "comfort": 0, "floor_bedding_warmth": 0, "required_str": -1, "deconstruct": {"items": [{"item": "black_glass_shard", "count": [10, 20]}]}, "flags": ["TRANSPARENT", "PLACE_ITEM", "MOUNTABLE", "CAN_SIT", "EASY_DECONSTRUCT"], "bash": {"str_min": 12, "str_max": 40, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_bathtub", "name": "black glass bathtub", "symbol": "~", "description": "A tub made from black glass, with a frozen column of onyx pouring from the tap.", "color": "dark_gray", "move_cost_mod": 2, "coverage": 30, "required_str": -1, "flags": ["TRANSPARENT", "FLAMMABLE_HARD", "CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE", "LIQUIDCONT"], "max_volume": "200 L", "examine_action": "keg", "keg_capacity": 600, "bash": {"str_min": 12, "str_max": 50, "sound": "glass shattering!", "sound_fail": "clink!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_dresser", "name": "black glass dresser", "symbol": "{", "description": "A black glass cabinet with its doors slightly ajar.  Inside is a row of hangers bearing neatly folded and entirely rigid clothing.", "color": "dark_gray", "move_cost_mod": -1, "coverage": 70, "required_str": 8, "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "max_volume": "2000 L", "bash": {"str_min": 12, "str_max": 40, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_fridge", "name": "black glass refrigerator", "symbol": "{", "description": "A black glass refrigerator, its door hanging open.  Inside is a seamless surface with protrusions resembling fresh fruit and plastic bottles.", "color": "dark_gray", "move_cost_mod": -1, "coverage": 90, "required_str": 10, "flags": ["BLOCKSDOOR", "MINEABLE"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "max_volume": "2000 L", "bash": {"str_min": 12, "str_max": 40, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_armchair", "name": "black glass armchair", "symbol": "H", "description": "An armchair that was presumably once very plush and comfortable.  Now the overstuffed cushions are hard, cold, and slippery.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 45, "comfort": 1, "floor_bedding_warmth": 0, "required_str": 7, "flags": ["TRANSPARENT", "MOUNTABLE", "CAN_SIT", "ALIGN_WORKBENCH"], "bash": {"str_min": 10, "str_max": 40, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_chair", "name": "black glass chair", "symbol": "h", "description": "A simple glass chair.  The seat is very thin, and crunches ominously under any weight.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 40, "comfort": 0, "floor_bedding_warmth": -1500, "bonus_fire_warmth_feet": 1000, "required_str": 4, "flags": ["TRANSPARENT", "MOUNTABLE", "CAN_SIT", "ALIGN_WORKBENCH"], "bash": {"str_min": 6, "str_max": 12, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_desk", "name": "black glass desk", "symbol": "#", "description": "A glass desk.  There are a handful of shapes atop it that suggest pens, paper, and miscellaneous knickknacks that are too jagged to identify.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 45, "required_str": 5, "crafting_pseudo_item": "medium_surface_pseudo", "flags": ["TRANSPARENT", "PLACE_ITEM", "MOUNTABLE", "FLAT_SURF", "SHARP", "PAINFUL"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "bash": {"str_min": 12, "str_max": 40, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "examine_action": "workbench", "workbench": {"multiplier": 0.7, "mass": 2000, "volume": "75 L"}}, {"type": "furniture", "id": "f_vitrified_locker", "name": "black glass locker", "symbol": "{", "description": "A tall glass cabinet.  Whatever was inside will stay that way; the door is seamless with the frame.", "color": "dark_gray", "move_cost_mod": -1, "coverage": 90, "required_str": 9, "flags": ["BLOCKSDOOR"], "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "max_volume": "500 L", "bash": {"str_min": 12, "str_max": 40, "sound": "glass crunching!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_oven", "name": "black glass oven", "symbol": "#", "description": "A glass sculpture of a gas oven.  Bristling from the burner is a crown of jagged shards in the rough shape of an uncontrolled fire.", "color": "dark_gray", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["PLACE_ITEM", "TRANSPARENT", "BLOCKSDOOR"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "max_volume": "120 L", "bash": {"str_min": 8, "str_max": 30, "sound": "glass crunching!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_mailbox", "name": "black glass mailbox", "symbol": "P", "description": "A glass sculpture of a mailbox.  The door is open; inside is a pile of envelopes, all razor-edged.", "color": "light_gray", "move_cost_mod": 1, "required_str": -1, "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "MOUNTABLE"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "max_volume": "10 L", "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_rack", "name": "black glass display rack", "description": "A glass sculpture of a shelving unit.  The angled shelves are covered in jagged miscellany, most of which is firmly fused in place.", "symbol": "{", "color": "light_gray", "move_cost_mod": -1, "coverage": 70, "required_str": 8, "flags": ["TRANSPARENT", "PLACE_ITEM", "BLOCKSDOOR", "MOUNTABLE"], "rotates_to": "INDOORFLOOR", "max_volume": "500 L", "bash": {"str_min": 10, "str_max": 30, "sound": "glass crunching!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_sink", "name": "black glass sink", "symbol": "&", "description": "A glass basin and tap.  The inside stops halfway down in sheet of concentric ripples, with the tiny spire of a splashing droplet at the center ", "color": "dark_gray", "move_cost_mod": 2, "coverage": 60, "required_str": -1, "flags": ["TRANSPARENT", "PLACE_ITEM", "MOUNTABLE"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "rotates_to": "INDOORFLOOR", "bash": {"str_min": 8, "str_max": 30, "sound": "glass crunching!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_toilet", "name": "black glass toilet", "symbol": "o", "color": "dark_gray", "description": "A toilet made of black glass.  The lid, perhaps mercifully, is fused shut.", "move_cost_mod": 2, "coverage": 30, "required_str": -1, "flags": ["TRANSPARENT", "MOUNTABLE"], "bash": {"str_min": 8, "str_max": 30, "sound": "glass crunching!", "sound_fail": "clink!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_cupboard", "name": "black glass cupboard", "symbol": "#", "description": "A black glass cupboard.  The doors are fused shut, but it presumably contains glasses.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 55, "required_str": -1, "flags": ["TRANSPARENT", "MOUNTABLE", "FLAT_SURF"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "rotates_to": "INDOORFLOOR", "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "bash": {"str_min": 8, "str_max": 30, "sound": "glass crunching!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 2000, "volume": "75 L"}}, {"type": "furniture", "id": "f_vitrified_planter", "name": "black glass garden", "description": "A planter of dark glass.  Sharp, twisting glass sprouts emerge from the black soil within.", "symbol": "^", "color": "dark_gray", "move_cost_mod": 1, "required_str": 10, "flags": ["TRANSPARENT", "FLAT", "MOUNTABLE", "SHARP", "PAINFUL"], "bash": {"str_min": 6, "str_max": 14, "sound": "crunch.", "sound_fail": "plink!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_flower", "name": "black glass flower", "description": "A delicate little thing; a wispy stem slightly bowed under the weight of translucent petals.  The tiny barbs that line it are almost impossible to see, until it crumbles in your fingers.", "looks_like": "f_flower_tulip", "symbol": "f", "color": "light_gray", "move_cost_mod": 0, "required_str": -1, "flags": ["TRANSPARENT", "TINY", "FLOWER", "SHARP", "PAINFUL"], "bash": {"str_min": 2, "str_max": 6, "sound": "tinkle.", "sound_fail": "plink."}}, {"type": "furniture", "id": "f_vitrified_bench", "name": "black glass bench", "symbol": "#", "description": "A long, low slab of black glass.  You can't tell if the fissures here and there on the surface are natural cracks, or carved names.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 35, "comfort": 1, "floor_bedding_warmth": -1500, "required_str": 8, "flags": ["TRANSPARENT", "MOUNTABLE", "SHORT", "CAN_SIT"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "bash": {"str_min": 10, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_washer", "name": "black glass washer", "description": "A glass washing machine.  The inside is completely filled with black sand, some of which has spilled onto the floor.", "symbol": "{", "bgcolor": "dark_gray", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "flags": ["BLOCKSDOOR", "FLAT_SURF"], "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_dryer", "name": "black glass dryer", "description": "A mechanical dryer of black glass.  The door hangs open, and inside, you can see a jumble of what might once have been clothes, now smashed to pieces.", "symbol": "{", "color": "dark_gray", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "max_volume": "37500 ml", "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "FLAT_SURF", "MINEABLE"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_bookcase", "name": "black glass bookcase", "symbol": "{", "description": "A heavy bookshelf, laden with books fused into the shelves and each other.  You can make out titles on the spines, but the alphabet they're written in is unfamiliar.", "color": "dark_gray", "move_cost_mod": -1, "coverage": 80, "required_str": 9, "flags": ["BLOCKSDOOR"], "rotates_to": "INDOORFLOOR", "max_volume": "2000 L", "bash": {"str_min": 6, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "shoot": {"reduce_damage": [5, 20], "reduce_damage_laser": [5, 20], "destroy_damage": [15, 45]}}, {"type": "furniture", "id": "f_vitrified_counter", "name": "black glass counter", "description": "A thick cube of black glass.  On the surface, fused in place, are statues of utensils and miscellany.", "symbol": "#", "color": "dark_gray", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["TRANSPARENT", "MOUNTABLE", "SHORT", "FLAT_SURF"], "connect_groups": "COUNTER", "connects_to": "COUNTER", "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_vitrified_table", "name": "black glass table", "description": "A slab of smooth black glass on four stout legs.  In a few places, there is shallowly rounded mass, like liquid spilled on the surface.", "symbol": "T", "color": "dark_gray", "move_cost_mod": 2, "coverage": 50, "required_str": 5, "flags": ["TRANSPARENT", "MOUNTABLE", "SHORT", "FLAT_SURF", "PLACE_ITEM"], "deconstruct": {"items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "smash!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}, "examine_action": "workbench", "workbench": {"multiplier": 1.1, "mass": 200000, "volume": "75 L"}}, {"type": "furniture", "id": "f_vitrified_birdbath", "name": "black glass birdbath", "description": "A glass pedestal and bowl.  Inside, frozen in the midst of washing itself in the black sand, is the delicate figure of a bird.", "symbol": "o", "color": "light_gray", "move_cost_mod": -1, "required_str": 10, "flags": ["PLACE_ITEM", "BLOCKSDOOR", "MINEABLE", "LIQUIDCONT", "TRANSPARENT"], "bash": {"str_min": 16, "str_max": 40, "sound": "smash!", "sound_fail": "clunk.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_shower", "name": "black glass shower", "symbol": "~", "description": "From the head of this shower descend a dozen streams of thin black glass, with frozen droplets strung from them like pearls.", "color": "dark_gray", "move_cost_mod": 0, "coverage": 35, "required_str": -1, "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "BLOCKSDOOR"], "bash": {"str_min": 6, "str_max": 30, "sound": "crunch!", "sound_fail": "clunk.", "sound_vol": 16, "sound_fail_vol": 12, "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_water_heater", "name": "black glass water heater", "description": "A cylinder of thick black glass.  The top has burst apart, and rivulets of black sand run down the side.", "symbol": "0", "bgcolor": "white", "move_cost_mod": -1, "coverage": 55, "required_str": -1, "flags": ["NOITEM"], "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_fireplace", "name": "black glass fireplace", "symbol": "#", "description": "A fireplace of pitted glass bricks.  The fire inside has been frozen in glass at the moment it began to leap beyond the grate.", "color": "dark_gray", "move_cost_mod": 2, "coverage": 50, "required_str": -1, "flags": ["TRANSPARENT", "SHARP", "PAINFUL"], "examine_action": "fireplace", "bash": {"str_min": 30, "str_max": 210, "sound": "crash!", "sound_fail": "clunk.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"id": "f_vitrified_firering", "type": "furniture", "name": "ring of black stones", "description": "A circle of smooth glass pebbles.  Inside are frozen tendrils of fire, slightly translucent.  It seems colder as you approach.", "symbol": "#", "color": ["dark_gray"], "flags": ["TRANSPARENT", "SHARP", "PAINFUL"], "move_cost_mod": 2, "required_str": -1, "bash": {"str_min": 15, "str_max": 105, "sound": "crash!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_floor_lamp", "name": "black glass lantern", "symbol": "t", "description": "A tall glass pole with a translucent shade atop it.  You know it could not possibly turn on, but in the corner of your eye, you think you might see a soft glow.", "color": "light_gray", "move_cost_mod": 2, "required_str": 1, "flags": ["TRANSPARENT", "BLOCKSDOOR", "PLACE_ITEM"], "bash": {"str_min": 12, "str_max": 40, "sound": "glass crunching!", "sound_fail": "tink!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_box", "name": "black glass box", "symbol": "X", "description": "A box of thin black glass.  Its edges are glisteningly sharp.", "color": "dark_gray", "move_cost_mod": 7, "coverage": 90, "required_str": 3, "max_volume": "324 L", "flags": ["TRANSPARENT", "PLACE_ITEM", "HIDE_PLACE", "NO_SIGHT", "CONTAINER", "SHARP", "PAINFUL"], "bash": {"str_min": 2, "str_max": 15, "sound": "crunch!", "sound_fail": "clink.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_sofa", "name": "black glass sofa", "symbol": "H", "description": "A long bench, padded with cold glass cushions.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 60, "comfort": 1, "required_str": 10, "flags": ["TRANSPARENT", "BLOCKSDOOR", "MOUNTABLE", "CAN_SIT"], "bash": {"str_min": 12, "str_max": 40, "sound": "smash!", "sound_fail": "whump.", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_dishwasher", "name": "black glass dishwasher", "description": "A glass dishwasher, its door hanging open.  Inside, glass plates and utensils are half buried in black sand.", "symbol": "{", "bgcolor": "dark_gray", "move_cost_mod": -1, "coverage": 60, "required_str": 13, "max_volume": "120 L", "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "FLAT_SURF", "MINEABLE", "NO_SELF_CONNECT"], "bash": {"str_min": 18, "str_max": 50, "sound": "crunch!", "sound_fail": "clang!", "items": [{"item": "black_glass_shard", "count": [40, 80]}]}}, {"type": "furniture", "id": "f_vitrified_doll", "name": "incomplete plush doll", "symbol": "D", "description": "A soft plush torso, empty of features, is lying on this sofa.", "color": "dark_gray", "move_cost_mod": 1, "coverage": 60, "comfort": 1, "required_str": 900, "flags": ["TRANSPARENT", "BLOCKSDOOR", "MOUNTABLE", "CAN_SIT"], "examine_action": {"type": "effect_on_condition", "effect_on_conditions": ["EOC_vitrified_doll_talk"]}, "rotates_to": "INDOORFLOOR", "bash": {"str_min": 900, "str_max": 900, "sound": "smash!", "sound_fail": "whump."}}, {"type": "furniture", "id": "f_vitrified_bookcase_talker", "name": "black glass bookcase", "symbol": "{", "description": "This bookcase is missing a book.", "color": "dark_gray", "looks_like": "f_vitrified_bookcase", "move_cost_mod": -1, "coverage": 80, "required_str": 900, "examine_action": {"type": "effect_on_condition", "effect_on_conditions": ["EOC_vitrified_bookcase_talk"]}, "flags": ["BLOCKSDOOR"], "rotates_to": "INDOORFLOOR", "emissions": ["emit_glimmer"]}]