[{"//": "This file exists purely for discoverability, it functionally does nothing as they would fallback to the default 1000 anyway, with the EoC definitions existing purely for mods to override the weights, potentially disabling them entirely", "type": "effect_on_condition", "id": "EOC_VANILLA_BRIDGE_WEIGHT", "eoc_type": "EVENT", "required_event": "game_begin", "effect": [{"math": ["vanilla_bridge_weight", "=", "1000"]}]}, {"type": "effect_on_condition", "id": "EOC_VANILLA_ROAD_WEIGHT", "eoc_type": "EVENT", "required_event": "game_begin", "effect": [{"math": ["vanilla_road_weight", "=", "1000"]}]}, {"type": "effect_on_condition", "id": "EOC_VANILLA_LAKE_WEIGHT", "//": "Doesn't affect lake_shore", "eoc_type": "EVENT", "required_event": "game_begin", "effect": [{"math": ["vanilla_lake_weight", "=", "1000"]}]}]