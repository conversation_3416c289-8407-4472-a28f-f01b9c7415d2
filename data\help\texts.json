[{"type": "help", "order": 0, "name": "<a|A>: Introduction", "messages": ["Cataclysm: Dark Days Ahead is a turn-based survival game set in a post-apocalyptic world.  You have survived the original onslaught, but the future looks pretty grim.", "You must prepare to face the many hardships to come, including dwindling supplies, hostile creatures, and harmful weather.  Even among fellow survivors you must stay alert, since someone may be plotting behind your back to take your hard-earned loot.", "Though one might think of Cataclysm: Dark Days Ahead as a roguelike, it differs from the traditional roguelikes in several crucial ways.  Rather than exploring an underground dungeon, with a limited area on each level, you are exploring a truly infinite world, stretching in all four cardinal directions.  In this survival game, you will have to find food, keep yourself hydrated, and also sleep periodically.  It's based on the principle of realism, so expect all of the hardships you'd expect in a real-life survival situation, and at least a dozen more from the eldritch and sci-fi nature of the Cataclysm itself.", "While Cataclysm: Dark Days Ahead has more tasks to keep track of than many other games, the modern setting of the game makes some tasks easier.  Firearms, medications, and a wide variety of tools are all available to help you survive."]}, {"type": "help", "order": 1, "name": "<b|B>: Movement", "messages": ["Movement is performed using the numpad, the arrow keys, or vikeys.", "<HELP_DRAW_DIRECTIONS>", "Each step will take 100 movement points (or more, depending on the terrain); you will then replenish a variable amount of movement points, depending on many factors (press <press_player_data> to see the exact amount).", "To attempt to hit a monster with your weapon, simply move into it.", "You may find doors, ('+'); these may be opened with <press_open> or closed with <press_close>.  Some doors are locked.  Locked doors, windows, and some other obstacles can be destroyed by smashing them (<press_smash>, then choose a direction).  Smashing down obstacles is much easier with a good weapon or a strong character.", "There may be times when you want to move more quickly by holding down a movement key.  However, fast movement in this fashion may lead to the player getting into a dangerous situation or even killed before they have a chance to react.  Pressing <press_safemode> will toggle \"safe mode.\"  While this is on, any movement will be ignored if new monsters enter the player's view."]}, {"type": "help", "order": 2, "name": "<c|C>: Viewing", "messages": ["The player can often see more than can be displayed on the screen at a time.  Pressing <press_look> enters \"Look around\" mode, which allows you to scroll around using the movement keys and view items on the map as well as monsters and their stance toward the character.  Pressing <press_listitems> provides a list of nearby visible items, though items shut away in crates, cupboards, refrigerators and the like won't be displayed until you are close enough.  Pressing Shift+vikeys (h,j,k,l,y,u,b,n) will scroll the view persistently, allowing you to keep an eye on things as you move around.", "Places outside of your view but seen previously and still memorized can be visualized, but they will be covered by the \"fog of war.\""]}, {"type": "help", "order": 3, "name": "<d|D>: Hunger, thirst, and sleep", "messages": ["As time passes, you will begin to feel hunger and thirst, and your stomach will remind you of that.  When this happens, status information will appear in the sidebar.  Don't get confused - depending on your condition, it will display both how full your stomach is, as well as hunger intensity in relation to your nutrition status.  Your body nutrition level is distinct from your current hunger: for example, you may feel full after a big meal while still being malnourished, and you will still get hungry while being overweight.  With regular meals, you may even never get hungry and yet become malnourished, if your caloric intake is too low.  In other words, you need to track both your immediate hunger/fullness and the overall nutrition of your whole body.", "Food takes some time to digest, and water travels faster through your digestive system than does solid food.  Sudden overeating and overdrinking may make you feel excessively full, so split your meals.  If you had been starved before, it will take some time to recover your body mass by building up stored calories through regular eating.  Overeating for a prolonged period of time leads to obesity.  Both sides of the spectrum are penalized.  When hunger and thirst progress to severe levels, you will also suffer penalties.  Thirst will kill you faster than hunger.", "You can develop various vitamin deficiencies if you eat poorly.  These deficiencies come in stages; for example, you won't go from perfectly good health into full-blown scurvy in an instant.  Any developing and ongoing deficiencies will be reported in the character sheet.  Deficiencies will inflict various penalties, but luckily, they are always reversible, and multivitamin pills can help you to correct these deficiencies.  You can also ingest too much of any particular vitamin, however, and that can create problems as well.  Be sure to have a balanced diet, or at least not a completely atrocious one.  You can, and should, examine food items (by pressing <press_examine>) to view their nutritional information.", "Eating and drinking poorly will affect your health.  Fast foods, quick snacks and sweet drinks are high in calories, but are a poor choice for a sustainable food source, if you don't want your health to get worse.  On the other hand, vegetables, herbal teas, and many other self-prepared meals are good for your health, and should be welcome additions to your diet.", "Finding food in a city is usually easy; outside of a city, you may have to hunt or forage.  After killing an animal, stand over the animal's corpse and butcher it into small chunks of meat by pressing <press_butcher>.  You might also be able to forage for edible fruit or vegetables; to do this, find a promising plant and examine it.  Likewise, you may have to drink water from a river or another natural source.  To collect it, stand in shallow water and press <press_pickup>.  You'll need a watertight container to store it.  Be forewarned that most sources of water aren't trustworthy, and may yield disease-carrying water.  To make sure it's healthy, purify the water by boiling it or using a water purifier before drinking.", "Every dozen or so hours, you'll find yourself growing sleepy.  If you do not sleep by pressing <press_sleep>, you'll start suffering stat and movement penalties.  You may not always fall asleep right away.  Sleeping indoors, especially on a bed, will help.  If that's not enough, sleeping pills may be of use.  While sleeping, you'll slowly replenish lost hit points.  You'll also be vulnerable to attack, so try to find a safe place to sleep, or set traps for unwary intruders."]}, {"type": "help", "order": 4, "name": "<e|E>: Pain and stimulants", "messages": ["When you take damage from almost any source, you'll start to feel pain.  Pain slows you down and reduces your stats, and finding a way to manage pain is an early imperative.  The most common is drugs: aspirin, codeine, tramadol, oxycodone, and more are all great options.  Be aware that while under the influence of many painkillers, the physiological side effects may slow you down or reduce your stats.", "Note that most painkillers take a little while to kick in.  If you take some oxycodone and don't notice the effects right away, don't start taking more -  you may overdose and die!", "Pain will also disappear with time, so if drugs aren't available and you're in a lot of pain, it may be wise to find a safe spot and simply rest for an extended period of time.", "Stimulants are another common class of drugs.  Stimulants provide you with a temporary rush of energy, increasing your movement speed and many stats (most notably Intelligence), making them useful study aids.  There are two drawbacks to stimulants: they make it more difficult to sleep and, more importantly, most are highly addictive.  Stimulants range from the caffeine rush of cola to the more intense high of Adderall and methamphetamine.", "Depressants are the opposite of stimulants.  They will make you groggy and sluggish, but may help you in falling asleep.", "Extremely elevated levels of stimulants or depressants in your system are dangerous to your health."]}, {"type": "help", "order": 5, "name": "<f|F>: Healing and medication", "messages": ["When you take damage, it will change the status of the affected body part, as shown in the sidebar.  Its overall wellness is represented by a series of bars that will deplete and change color the more damage you take.  If the accumulated damage is more than the body part can suffer, it will break.  If that happens to a critical body part (i.e., your torso or head), you will die.  For other body parts, it means broken bones, significant penalties, and a long process of healing - after placing the affected limb in a splint.", "Normal damage heals with time.  You heal more during a good sleep, but wounds also heal a bit while you are awake.", "You need to treat your wounds properly, because untreated wounds heal very slowly.  First, you need to bandage your wounds, and, if possible, disinfect them as well.  This will set conditions for proper healing and will result in faster recovery.  You can make makeshift bandages if you have the skill, and there are a few options to replenish your stock of disinfectant.  Remember to always treat your wounds as much as you can, as this is the proper way to get them healed.", "You can get bitten if a zombie grabs you.  Bite wounds can be deep; this means that it is infected.  If that happens, the status bar of affected body part will change color to blue.  This means you need to disinfect it as soon as possible to clean the wound.  If you leave it be, the affected body part will develop a serious infection, and its status bar color will change to green.  At this stage, it is too late to disinfect the wound, and you need antibiotics to fight it.  Your body will fight the infection by itself, and regular antibiotic intake may keep you alive long enough for that to happen.  You have to wait it out, in the hope that you will not die in the process.", "If you are bleeding, the status bar of the wounded part turns red.  The affected body part takes damage as long as it bleeding.  The simplest way to stop bleeding is to use a bandage, and there are additional medical items dedicated to stopping hemorrhage.", "You may be affected by one of many additional medical conditions and maladies throughout the game.  In that case, you may want to seek proper medication, if one exists for the affliction you suffer.  There are many medications beside the ones already mentioned, some of which have some extra effects that you might utilize even in a way not originally intended.  With some skill you can also try natural medicine, as many herbs have more or less beneficial effects, and if you are a seasoned chemist you can synthesize your own drugs."]}, {"type": "help", "order": 6, "name": "<g|G>: Addiction", "messages": ["Many drugs (and some consumables) have a potential for addiction.  Each time you consume such a substance, there is a chance that you will grow dependent on it.  Consuming more of that drug will increase your dependence.  Effects vary greatly between addictive substances, but no matter the substance, addiction has only one cure: going cold turkey.  The process may last for days, and will leave you very weak, so try to do it in a safe area.", "If you are suffering from withdrawal effects, taking more of the addictive substance will cause the effects to cease immediately, but may deepen your dependence."]}, {"type": "help", "order": 7, "name": "<h|H>: Morale and learning", "messages": ["Your character has a morale level, which affects you in many ways.  The depressing post-apocalypse world is tough to deal with, and your mood will naturally decrease very slowly.", "There are lots of options for increasing morale: reading an entertaining book, eating delicious food, listening to music, and taking recreational drugs are but a few options.  However, most morale-boosting activities can only elevate your mood to a certain level before they grow boring.", "There are also lots of ways for your morale to decrease, beyond its natural decay toward its normal level.  Eating disgusting food, reading a boring technical book, killing a friendly NPC, or going through drug withdrawal are some prominent examples.", "Low morale will make you sluggish and unmotivated.  If your morale drops very low, you won't be able to perform crafting at all.  If you become sufficiently depressed, you will suffer stat penalties.  Very high morale fills you with gusto and energy, and you will find yourself moving faster.  At extremely high levels, you will receive stat bonuses.", "<PERSON><PERSON><PERSON> is also responsible for ensuring you can learn effectively, via a mechanic referred to as 'focus.'  Your focus level is a measure of how effectively you can learn.  The natural level is 100, which indicates normal learning potential.  Higher or lower focus levels make it easier or harder to learn from practical experience.", "Your focus level has a natural set point that it will trend toward.  When your focus is much lower - or higher - than this set point, it will change faster than when it is near the set point.  Having high morale will raise the set point, and having low morale will lower the set point.  Pain is also factored into the set point calculation - it's harder to learn when you're in pain.", "Your focus is also lowered by certain activities.  Training your skills through real-world practice lowers your focus gradually, by an amount that depends on your current level of focus (higher focus means larger decreases, as well as improved learning).  Training your skills by reading books decreases your focus rapidly, by giving a significant penalty to the set point of your focus.", "You can disable a skill in the Player Info Menu (press <press_player_data>) if you don't want to practice it, and it will prevent focus drain while performing relevant actions, but you will not progress in that skill while it's disabled."]}, {"type": "help", "order": 8, "name": "<i|I>: Radioactivity and mutation", "messages": ["Though it is relatively rare, certain areas of the world may be contaminated with radiation.  It will gradually accumulate in your body, weakening you more and more.  While in radiation-free areas, your radiation level will slowly decrease; taking Prussian blue tablets will help speed this process.  Get yourself a measuring device, as radiation is invisible, and you may not know that you are under its influence until it results in radiation sickness.", "If you become very irradiated, you may develop mutations.  Most of the time, these mutations will be negative; however, many are beneficial, and others have both positive and negative effects.  Your mutations may change your play style considerably.  It is possible to find substances that will remove mutations, though these are extremely rare.", "There are various mutagenic substances, and consuming (or injecting) them will grant mutations.  However, the process taxes your body significantly, and can be addictive.  With enough mutations and certain conditions being met, you will permanently transcend your humanity into a wholly different life-form."]}, {"type": "help", "order": 9, "name": "<j|J>: Bionics", "messages": ["Bionics are biomechanical upgrades to your body.  While many are simply 'built-in' versions of items you would otherwise have to carry, some bionics have unique effects that are otherwise unobtainable.  Some bionics are constantly working, whereas others need to be toggled on and off as needed.", "Most bionics require a source of power, and you will need an internal battery to store energy for them.  Your current amount of energy is displayed in the sidebar right below your health.  Replenishing energy can be done in a variety of ways, but all require the installation of a special bionic just for that purpose.", "Installation of a bionic is only possible using a specialized medical apparatus, ideally operated by a trained professional.  Using machinery to manipulate bionics requires high levels of Intelligence, Health Care, Computers, and Electronics.  Beware, though: a failure may cripple you!  Many bionic modules are difficult to find, but may be purchased from certain wandering vagabonds for a very high price, or acquired by skillful dissection of corpses of deceased bionic users (including zombies).  Bionic modules obtained this way may require extra preparation to become safely usable.", "Any bionic can be removed from your body, but this process may be even harder - and thus riskier - than the initial installation.  Both installation and removal are non-trivial surgical procedures, and therefore require anesthesia.", "For lone survivors, the standard choice for installing or uninstalling bionics is an Autodoc.  Usually, you can find one in a hospital or clinic.  All Autodoc procedures require an anesthetic kit.  Don't even try without proper anesthesia - normal drugs won't help.  However, you can bypass this restriction if you find a way to completely negate pain.", "Take note that bionic installation or removal require narcosis, immobilization of the patient, and only then the operation - which may take hours.  So you have to make sure that you will be safe during this process."]}, {"type": "help", "order": 10, "name": "<k|K>: Crafting", "messages": ["Many important items can be very hard to find, or will cost a great deal of money to trade for.  Fortunately, it is possible to craft a wide variety of goods (as best you can) with the proper tools, materials, and training.", "Some recipes require a set of tools.  These are not used up when crafting, so you can keep your tool set.  All recipes require one or more ingredients; these ARE used up in crafting.", "To craft items, press <press_craft>.  In the base game, there are ten categories: Weapon, Ammo, Food, Chem, Electronic, Armor, Appliance, Other, Animals, and Practice.  In each major category there are several sub-categories.  While a few items require no particular skill to create, the vast majority require you to have some knowledge.  Sometimes, a skilled survivor will work out a given recipe from her or his knowledge of the skill, but more often you will need reference material, commonly a book of some sort.  Reading such references gives a chance to memorize recipes outright, and you can also craft while referring to the book: just have it handy when crafting.  Different skills are useful for different applications:", "-> Fabrication is the generic artisan skill, used for a wide variety of gear.", "-> Food Handling is used to make tasty recipes; more intricate (and usually more nutritious) recipes require higher levels of the Food Handling skill.", "-> Applied Science is the chemistry skill; use this to craft mundane things like lye and various acids, or weaponized chemicals and various beneficial elixirs.", "-> Tailoring is used to create basic clothing, and armor later on.", "-> Electronics lets you make a wide variety of tools with intricate uses.", "In addition to the primary crafting skills, other skills may be necessary to create certain items.  Devices, Marksmanship, and Health Care are all required for certain items.", "Crafting an item with high difficulty may fail and possibly waste some materials.  You should gather and prepare spare material, just in case.", "Crafting very large/heavy items or batches of items is best done at a workbench of some kind.  You could use any ordinary table, or build your own out of metal to handle heavier loads.  Using a workbench or other furniture for crafting is more comfortable; therefore, crafting is faster.", "If the crafting process is interrupted for any reason, the progress is wrapped in a special item representing the craft being made.  You may use it to resume crafting at any point.", "Batch crafting in most cases means saved time, and your companions can also help if they know the ropes."]}, {"type": "help", "order": 11, "name": "<l|L>: Traps", "messages": ["Before sleeping in dangerous territory, it may be wise to set traps to ward off unwanted intrusions - or at least warn you if one is in progress.  There are a few traps to be found in the world, most notably bubble wrap (which will make a loud noise if stepped on, helping to wake you up) and bear traps (which make noise AND damage, and trap anything that steps on them).  Others need to be crafted; this requires the Devices skill, and possibly Mechanics.", "To set a trap, simply use the item (press <press_apply>) and choose a direction; the trap will be placed on the chosen tile.  Some traps may require additional tools, like a shovel, to be set.  Once set, a trap will remain in place until stepped on or disarmed.", "To disarm a trap, examine (press <press_examine>) the space it is on.  Your success depends upon your Devices skill and Dexterity.  If you succeed, the trap is removed and replaced by some or all of its constituent parts; however, if you fail, there is a chance that you will set off the trap, suffering the consequences.", "Many traps are fully or partially hidden.  Your ability to detect traps is entirely dependent upon your Perception.  Should you stumble into a trap, you may have a chance to avoid it, depending on your Dodging skill.", "Some traps can be built via the construction menu.  Pit traps are the most common example."]}, {"type": "help", "order": 12, "name": "<m|M>: Items overview", "messages": ["There is a wide variety of items available for your use.  You may find them lying on the ground; if so, simply press <press_pickup> to pick up items on the same tile.  Some items are found inside a container, which may be drawn as a { with a blue background.  Pressing <press_examine>, then a direction key, will allow you to examine these containers and loot their contents.", "Pressing <press_compare> opens a comparison menu, where you can see two items side-by-side with their attributes color-coded to indicate which is superior.  You can also access the item comparison menu by pressing C after <press_listitems> to open the \"List all items around the player\" menu and selecting an item.", "Almost any item may be used as a melee weapon, though some are better than others.  You can check the melee attributes of an item you're carrying by pressing <press_inventory> to enter your inventory, then pressing the letter of the item.  There are 5 melee values: to-hit bonus (or penalty), moves per attack, and bash, cut, and pierce damage.  The to-hit bonus adjusts the chance of an attack connecting with a monster and the chance of a successful attack becoming a critical hit.  Moves per attack represents how many moves it takes to attack with the weapon, with 100 moves equaling one second.  Bash damage can stun a monster, preventing it from counter-attacking, but is governed by your strength.  Cut damage is usually greater than bash damage, but many monsters have natural armor against it.  Pierce damage usually penetrates armor better than cut damage, but does less damage overall, especially if you do not have a lot of skill in piercing weapons.  It, too, may be reduced by a monster's natural armor.  The typical damage per second values are for your survivor and account for moves per attack, encumbrance, missed strikes, weapon skill, critical hits, and target armor.  The 'Best' value is against an unarmored target with no Dodging skill.  The 'Vs. Agile' value is against an unarmored target with a high Dodging skill.  The 'Vs. Armored' value is against a target with more than 15 Bash and 20 Cut resistance but no Dodging skill.  These are typical values to let you assess the effectiveness of weapons; the amount of damage you actually inflict will vary depending on the situation.", "To wield an item as a weapon, press <press_wield>, then the appropriate letter.  Wielding the item you are currently wielding will unwield it, leaving your hands empty.  A wielded weapon will not contribute to your volume carried, so holding a large item in your hands may be a good option for travel.  When unwielding your weapon, you will be given a choice of what to do with it.", "To wear a piece of clothing, press <press_wear>, then the appropriate letter.  Arm<PERSON> reduces damage, and can help you resist things like smoke.  To take off an item, press <press_take_off>, then the appropriate letter.  Clothing and armor are worn in distinct layers, and provide different coverage, protection and warmth.  Each piece has its own encumbrance, and wearing too much on any given layer can significantly hamper your movement and other abilities, especially during combat.  You can view and sort worn items by pressing <press_sort_armor>.", "Your clothing can sit in one of four base layers on your body: close to skin, normal, outer, and strapped.  You can wear one item from each layer on a body part without incurring an encumbrance penalty for too many worn items.  Any items beyond the first on each layer add the encumbrance of the additional article(s) of clothing to the body part's encumbrance.  The layering penalty applies a minimum of 2 and a maximum of 10 encumbrance per article of clothing.", "For example, on her torso, a character might wear a leather corset (close to skin), a combat shirt (normal), a trenchcoat (outer), and a survivor runner pack (strapped).  Her encumbrance penalty is 0.  If she put on a tank top it would conflict with the leather corset and add the minimum encumbrance penalty of 2.", "Items can be damaged through various means, whether intentional or not.  Indicators such as <color_yellow>|\\</color> show the item's current state.  You may be able to repair the item using specific tools, which are listed in the item's information screen.", "Items are also subject to degradation, which represents the item's structural integrity over its lifetime.  Items that suffer serious damage can become more degraded, which limits how well the item can be repaired.  The higher the degradation, the less you'll be able to repair it.", "In the View Nearby Items menu (press <press_listitems> to open), you can filter or prioritize items.  You can enter item names, or use various advanced filter strings: {<token>:<search param>}.", "Some available tokens:\n    c = category (books, food, etc)    | {c:books}\n    m = material (cotton, Kevlar, etc) | {m:iron}"]}, {"type": "help", "order": 13, "name": "<n|N>: Combat", "messages": ["Zombies in cities will spawn at the start of the game, but may also wander around.  All monsters are represented by letters (or sprites in the Tiles version) on your screen; a list of monster names, as well as their positions relative to you, is displayed on the right side of the screen.  The 'compass' will display monsters that you see but are currently off the screen.", "To attack a monster with a melee weapon, simply move into them.  The time it takes to attack depends on the size and weight of your weapon.  Small, light weapons are the fastest; unarmed attacks increase in speed with your Unarmed Combat skill, and will eventually be VERY fast.  A successful hit with a bashing weapon may stun the monster temporarily.  A miss may make you stumble and lose movement points.  If a monster hits you, your clothing may absorb some of the damage, but you will take what remains.", "Swarms of monsters may call for firearms.  Most firearms in the game require compatible magazines to hold the ammunition.  Compatible magazines are listed in a given firearm's description.  Fortunately, a firearm often spawns with one such magazine in it.", "You can eject a magazine from a firearm by pressing <press_unload> and load it with compatible ammunition separately, or if you have a firearm with a partially filled magazine in it, and some matching loose ammo in the inventory, you can simply reload by pressing <press_reload>; you will automatically eject the magazine, fill it with as much ammo as possible, and then put the magazine back in.  You don't have to worry about chambering a round though.  Of course, all of this does take time, so try not to do it if there are monsters nearby.", "While magazines and clips are often firearm-specific, on some occasions a magazine/speedloader is compatible with several other firearms or calibers.  Below are some examples of calibers that might share magazines or speedloaders:\n\n.380 ACP and 9mm Luger,\n.40 S&W and 10mm Auto,\n.45 ACP and .460 Rowland, and\n.45 Colt and .454 Casull.", "Magazine descriptions also list compatible ammo.", "Magazines can be stored inside several worn accessories for quicker access, such as chest rigs and ammo pouches.  All compatible storage items that can hold a given magazine are listed in its description.  To store a magazine in a clothing item, activate (<press_apply>) the desired clothing item, at which point you'll get to choose which magazine to store.", "To fire, press <press_fire>, move the cursor to the desired tile, press '.' to improve your aim, and then 'f' to fire.  Additionally, you may want to fire at predefined aim levels using 'a', 'c' or 'p'.  Some guns have alternate firing modes, such as burst-fire; to cycle modes, press <press_select_fire_mode>.  Firing continuously, especially in bursts, will severely reduce accuracy.", "The <press_fire> key is not reserved exclusively for guns.  It also works for other ranged weapons like spears or launchers, but the aiming options may vary.", "Fleeing will often be a solid tactic, especially when overwhelmed by a swarm of zombies.  Try to avoid getting cornered inside a building.  Ducking down into the subways or sewers is often an excellent escape tactic."]}, {"type": "help", "order": 14, "name": "<o|O>: Martial arts styles", "messages": ["A variety of fighting styles are available, particularly for the unarmed fighter.  You can start with your choice of a single, commonly-taught style by starting with the Martial Arts Training trait.  Many, many more can be found in their own traits, trained from manuals or by taking lessons from wandering masters.", "To select a fighting style, press <press_pick_style>.  Some styles are relevant only when you are unarmed, others are compatible with (or require) certain weapons.  If your current weapon is compatible with the chosen style, you will start using the selected style immediately.  Otherwise, it will be locked in as your default style.  To start using it, wield a relevant weapon, or empty your hands by pressing <press_wield>, then the key for the item you are currently wielding.  If you wish to prevent yourself from accidentally wielding weapons taken from the ground, enable \"Keep hands free\" found in the styles menu.", "Remember to read the style's description by pressing 'F1' in the style selection menu.  You will be provided with a list of passive and active abilities associated with the style.  These are varied and unique to each style, and range from special combo moves to bonuses depending on the situation you are in.", "Most styles have a variety of special moves associated with them.  Most have a skill requirement, and will be unavailable until you reach that level of skill.  Those special moves or combos are executed automatically during attacks you make.", "Many styles also have special passive effects unlocked under certain conditions.  You can also check these by examining your style."]}, {"type": "help", "order": 15, "name": "<p|P>: Survival tips", "messages": ["The hierarchy of needs for survival in most cases is as follows: shelter, fire, water, food.  The rest is the process of trying to answer a question: What would you do in a survival situation?", "The first thing to do, when you are out of immediate danger, is to check your starting location for useful items.  Your initial storage is limited, and a backpack, trenchcoat, or other storage medium will let you carry a lot more.  Finding a weapon is important, but not a first priority; frying pans, butcher knives, and more are common in houses, and hardware stores may have others, as well as useful tools.  Initially, save any guns you find as a last resort: ammo is scarce, while zombies are plenty, and unwanted attention may be more than you can handle.", "It's also important to carry a few medications; painkillers are a must-have, and drugs such as cigarettes will make your life easier (but beware of addiction).  Leave the city as soon as you have a good stockpile of equipment: while their population density makes cities important places to find food, ammunition, and many other goods, the high concentration of zombies makes them a deathtrap for the unprepared survivor.", "Avoid combat whenever you can.  Run if you have to, but mind your stamina; zombies, unlike yourself, are inexhaustible.  Combat is always risky, and sometimes, one wrong step may be your last.  Therefore, in most cases you gain nothing from combat unless you are equipped for it and killing your enemy will help you achieve something.", "Combat is much easier if you can fight just one monster at a time.  You can sometimes kite singled-out zombies, drawing them to a place of your choosing.  Use doorways as a choke point or stand behind a window and strike as the zombies slowly climb through.  Be wary - in numbers, they will push through anyway.  Never be afraid to just run if you can outpace your enemies.  Irregular terrain, like forests, may help you lose monsters.", "Using firearms is the easiest way to kill an enemy, but the sound will attract unwanted attention.  Save the guns for emergencies, and melee when you can.", "If you need to protect yourself from acid, look for clothing made of plastic, Kevlar, leather, or cloth, in decreasing order of effectiveness.  So, while leather and Kevlar will protect you from active enemies, a hazmat suit and rubber boots will make you nigh immune to acid damage.  Items made of glass, ceramics, diamond, or precious metals will be totally immune to acid.", "When looting, try to fill your inventory as much as possible without being overloaded.  You never know when you might need an item, most are good to sell, and you can easily drop unwanted items on the floor.  But don't go looting empty-handed; you never know what might happen, so have your most crucial survival items with you.", "Keep an eye on the weather.  Wind and humidity will exacerbate dire conditions, so seek shelter if you're unable to withstand them.  Staying dry is important, especially if temperatures are so low that they might cause frostbite.  Use a towel to dry yourself if you are wet from the rain.  If you're having trouble staying warm overnight, make a pile of clothing on the floor to sleep on.  Finding a pillow and a blanket for your bed is always good for the quality of your sleep.  If you can't find those, improvise; even a chair in a basement is better then a patch of wet ground in a field.  Finding or making a shelter with a place to sleep is very important for your survival.", "Winter is harsh.  Liquids and food may freeze, and you may need to defrost them using a cooking appliance.  Placing them by a lit fire might also help.  Basements are warmer in winter and cooler in summer, so they are frequently good places to store your excess food.  Most food decays with time, and that process can be sped up or slowed down by the temperature.  Frozen food will not rot.  It you are able to bring a freezer or a fridge back to life, it will help you in the long run.  If these are out of your reach, think about constructing a root cellar.", "You may preserve food in many other ways, as well; for example, by using a smoking rack, or cooking it into a meal that has a longer shelf life."]}, {"type": "help", "order": 16, "name": "<r|R>: Vehicles and Driving", "messages": ["You control vehicles using the numpad, or vikeys.  Note, however, that you control the vehicle's controls, rather than controlling the vehicle directly.", "<HELP_DRAW_DIRECTIONS>", "In order to assume control of a vehicle, get to a location with working \"vehicle controls\" and a \"seat,\" then press <press_control_vehicle>.  Once you are in control, <press_UP> accelerates, <press_DOWN> slows or reverses, & <press_LEFT> & <press_RIGHT> turn left or right.  Diagonals adjust course and speed.  The gas/brake adjust the speed which the vehicle will attempt to maintain.", "10-30 MPH, or 16-48 KPH, is a good speed for beginning drivers, who tend to fumble the controls.  As your Driving skill improves, you will fumble less and less.  To simply maintain course and speed, hit <press_pause>.", "It's a good idea to pull the handbrake - \"s\" - when parking, just to be safe.  If you want to get out, hit the lights, turn the engine on or off, or otherwise use the vehicle controls, press <press_control_vehicle> to bring up the \"Vehicle controls\" menu, which has options for things you'd do from the driver's seat.", "Examining (<press_examine>) a vehicle brings up the vehicle interaction window.  The left pane shows a top-down view of your vehicle and each part of it.  The middle pane shows a summary of the vehicle's engines, batteries, storage tanks, weapons, and seating.  The right panel is context-sensitive, but normally has descriptions of the vehicle parts in the tile highlighted in the vehicle view on the left.", "Across the bottom is a summary of the most important parameters of your vehicle.  \"Safe speed\" is how fast you can drive without damaging the engine, and \"Top speed\" is how fast you can drive at the engine's maximum output.  \"Acceleration\" is how fast your vehicle can increase speed each turn.  \"Cargo volume\" is how much cargo you can store and how much stuff you have in your vehicle, \"Mass\" is how much the vehicle weighs, and the \"Status\" and \"Wheels\" readouts give a summary of the vehicle condition and wheel condition.  There are also fuel indicators on the lower right that give an estimate of how quickly your fuel will run out.", "Air drag, water drag, rolling drag, and static drag are all values that influence your vehicle's safe and maximum speed.  The interaction of drag and engine power is complicated, but generally speaking, more powerful engines will make your vehicle go faster but also consume fuel faster.", "Air drag increases as your vehicle gets wider or has more parts that increase the vehicle's height like full boards, aisles, or turrets.  It also increases as the vehicle assumes a less aerodynamic profile, such as by having exposed passengers or full boards at the front of the vehicle.  Air drag strongly influences vehicle speed, especially at high speeds.", "Rolling drag increases with vehicle weight and wheel count.  Rigid wheels have less rolling drag per wheel than inflatable wheels, but weigh more, so rolling drag may still increase when moving from inflatable wheels to rigid wheels.  Tank treads have enormous rolling resistance and are very heavy, so tanks have a lot of rolling resistance.  Rolling drag influences vehicle speed, but less than air drag does, especially at high speeds.", "Vehicles that can move in water have a water drag value.  Water drag increases with vehicle width and draft, and draft increases with vehicle weight, but is reduced by adding more boat boards and by widening or lengthening the vehicle.  Water drag has an enormous effect on vehicle speed in the water.  See the section on water vehicles for more about draft and water drag.", "Static drag includes things like plows, and directly reduces your vehicle's acceleration and top speed.", "\"Offroad\" is a rough estimate of how efficient your vehicle moves when not on pavement.  Your vehicle's safe speed, top speed, and acceleration are multiplied by roughly the offroad percentage value when your vehicle is not on the pavement.  The offroad percentage decreases as vehicle mass increases, and increases with the size and number of wheels.  Tank treads will give your vehicle an excellent offroad percentage but your vehicle will be slower due to the treads' very high rolling drag.", "Skilled survivors can use wrenches, hacksaws, and welding tools to add or remove parts on a vehicle.  You can even create a vehicle entirely from scratch, by using the Construction (<press_construction>) menu to 'Start Vehicle Construction' when you have a frame available.  Tuning a vehicle for speed, fuel consumption, offroad capacity, protection against threats, and cargo capacity is a complicated process.", "BOATS AND AMPHIBIOUS VEHICLES", "You can use boat hull parts to create boats or amphibious vehicles that can move between water and land.", "Each boat hull you add to your vehicle increases the height of the vehicle's watertight hull.  If the height of the watertight hull is less than the vehicle's draft, then water will flow into your vehicle, and it will sink and be destroyed if it goes into deep water.  If your vehicle's draft is less than the height of the watertight hull, your vehicle will not take on water and will not sink in deep water.", "Draft is determined by the length and width of your vehicle, the mass of your vehicle, and the percentage of the vehicle's underbody covered in boat hulls.  In general, a vehicle should be able to float if its mass in tons is less than a quarter of the product of its length and width, provided that the watertight hull is high enough.  A boat that is 5 tiles wide and 9 tiles long has a good chance of floating if its entire underbody is covered in boat boards and it weighs less than 10 tons.", "Increased draft increases water drag, and water drag reduces a vehicle's speed in water.  If you want your boat to go fast, make it as light as possible.", "Amphibious vehicles have both wheels and boat hulls.  As long as your amphibious vehicle has enough wheels on land and enough boat hulls to not sink in deep water, you can freely transition from land to water.  However, most amphibious vehicles are much slower in water than they are on land, so be careful when entering water to not leave your speed above your vehicle's safe water speed or you can burn out your engine!"]}, {"type": "help", "order": 17, "name": "<s|S>: Skills and proficiencies", "messages": ["SKILLS", "Each character has a collection of skills they may learn over time.  These are grouped into four categories: <PERSON>ee, Ranged, Crafting, and Interaction.  Each skill begins at level 0 and increases with study and practice up to a maximum of level 10.  Each level is harder to attain than the one before, but each level also increases the character's ability to do things using that skill, and may unlock crafting recipes or other actions gated by skill level.  Reaching level 10 in any skill represents true mastery, near the limit of human capability.", "The lower left column of the <press_player_data> screen displays all skills and their current levels, along with percentage progress toward the next level.  Skills highlighted in light red represent those where the character's knowledge has exceeded their practical experience.  This may occur when learning skills primarily from books, without practicing activities or crafts that also exercise those skills.", "Combat skills in the Melee and Ranged categories are quite specialized, focused on specific damage types and firearm flavors.  The Crafting and Interaction categories are more general, each covering a potentially wide range of techniques and abilities.", "PROFICIENCIES", "Separate from Skills, Proficiencies represent a more granular level of character talents.  They are generally mastered over a shorter time span, and their names and purviews tend to be more concrete and specific than those of skills.", "Proficiencies are often defined as a tree of knowledge, where beginners must first master the basic principles before moving onto more advanced topics.  For instance, before a character learns \"Blacksmithing\", they must first acquire \"Principles of Metalworking\".  Once they have learned \"Blacksmithing\", they may dive deeper into \"Armorsmithing\", \"Bladesmithing\", or \"Manual Tooling\".", "The lower right column of the <press_player_data> screen shows each Proficiency, starting in white with those fully learned, followed in gray by those partially learned, and their current progress.  Partially learned proficiencies give some benefit, gradually reducing penalties until they are fully learned.  Once fully learned, proficiencies are considered mastered, and are not forgotten.", "Many crafting recipes involve one or more proficiencies.  Trying to craft these recipes without the necessary skill set is a long and arduous process with a high rate of failure.  Learning the missing proficiencies offers a bonus to time and to chance of success.  The effect of missing proficiencies are multiplied when several proficiencies are involved, making some advanced recipes extremely time-consuming and error-prone without appropriate knowledge.  For this reason, it's recommended to learn at least some of the proficiencies with easier recipes before moving onto more advanced multiple-proficiency recipes.  Books may also grant a portion of the proficiency bonus."]}, {"type": "help", "order": 18, "name": "<1>: List of item types and data", "messages": ["~       Liquid\n%%       Food\n!       Medication\nThese are all consumed by using <press_eat>.  They may provide a certain amount of nutrition, may quench your thirst, may be a stimulant or a depressant, and may provide (or reduce!) morale.  There may also be more subtle effects.", "/       Large weapon\n;       Small weapon or tool\n,       Tiny item\nThese are all generic items, useful only to be wielded as a weapon.  However, some have special uses; they will show up under the TOOLS section in your inventory.  Press <press_apply> to use these.", ")       Container\nThese items may hold other items.  Some are passable weapons.  Many will be listed with their contents, e.g. \"plastic bottle > water.\"  Those containing comestibles may be eaten with <press_eat>; this may leave an empty container.", "[       Clothing\nThis may be worn with the <press_wear> key or removed with the <press_take_off> key.  It may cover one or more body parts; you can wear multiple articles of clothing on any given body part, but this will encumber you severely.  Each article of clothing may provide storage space, warmth, encumbrance, and resistance to bashing and/or cutting attacks.  Some may protect against environmental effects.", "(       Firearm\nThis weapon may be loaded with ammunition with <press_reload>, unloaded with <press_unload>, and fired with <press_fire>.  Some have automatic fire, which may be used with <press_select_fire_mode> at a penalty to accuracy.  The color corresponds to the type; handguns are gray, shotguns are red, submachine guns are cyan, rifles are brown, assault rifles are blue, and heavy machine guns are light red.  Each has a dispersion rating, a bonus to damage, a rate of fire, and a maximum load.  Note that, while most firearms load fully in one action, shotguns must be loaded one shell at a time.", "=       Ammunition\nAmmunition is worthless without a gun to load it into.  Generally, there are several variants for any particular caliber.  Ammunition has damage, dispersion, and range ratings, as well as an armor-piercing value.", "*       Thrown weapon; simple projectile or grenade\nThese items are suited for throwing, and many are only useful when thrown, such as grenades, Molotov cocktails, or tear gas.  Once activated, be certain to throw these items by pressing <press_throw>, then the letter of the item.", "?       Book or magazine\nThis can be read for training or entertainment by pressing <press_read>.  Most require a basic level of Intelligence; some require a base level of knowledge in the relevant subject.  Some may contain useful crafting recipes."]}, {"type": "help", "order": 19, "name": "<2>: Description of map symbols", "messages": ["MAP SYMBOLS:", "<color_brown>.</color>           Field - Empty grassland with occasional wild fruit.", "<color_green>F</color>           Forest - May be dense or sparse.  Movement will be slowed.  You might be able to forage for food here.", "<color_dark_gray>│─└┌┐┘├┴┤┬┼</color> Road - Safe from burrowing animals.", "<color_dark_gray>H=</color>          Highway - Like roads, but lined with guard rails.", "<color_dark_gray>|-</color>          Bridge - Helps you cross rivers.", "<color_blue>R</color>           River - Most creatures cannot swim across them, but you can.", "<color_dark_gray>O</color>           Parking lot - Empty lot, few items.  Mostly useless.", "<color_light_green>^>v<</color>        House - Filled with a variety of items.  Good place to sleep.", "<color_light_blue>^>v<</color>        Gas station - A good place to collect gasoline.  Risk of explosion.", "<color_light_red>^>v<</color>        Pharmacy - The best source for vital medications.", "<color_green>^>v<</color>        Grocery store - A good source of canned food and other supplies.", "<color_cyan>^>v<</color>        Hardware store - Home to tools, melee weapons and crafting goods.", "<color_light_cyan>^>v<</color>        Sporting Goods store - Several survival tools and melee weapons.", "<color_magenta>^>v<</color>        Liquor store - Alcohol is good for crafting Molotov cocktails.", "<color_red>^>v<</color>        Gun store - Firearms and ammunition are very valuable.", "<color_blue>^>v<</color>        Clothing store - High-capacity clothing, some light armor.", "<color_brown>^>v<</color>        Library - Home to books, both entertaining and informative.", "<color_white>^>v<</color>        Man-made buildings - The pointed side indicates the front door.", "            There are many others out there… search for them!", "<DRAW_NOTE_COLORS>"]}, {"type": "help", "order": 20, "name": "<3>: Description of gun types", "messages": ["<color_light_gray>( Handguns</color>\nHandguns are small weapons held in one or both hands.  They are much more difficult to aim and control than larger firearms, and this is reflected in their poor accuracy.  However, their small size makes them appropriate for short-range combat.  They are also relatively quick to reload and use a very wide variety of ammunition.  Their small size and low weight make it possible to carry several loaded handguns, switching from one to the next as their ammo is spent.", "<color_green>( Crossbows</color>\nThe best feature of crossbows is low noise they produce.  The bolts they fire are only rarely destroyed; if you pick up the bolts after firing them, your ammunition supply will last much longer.  Crossbows suffer from a short range and a very long reload time (modified by your strength); plus, most only hold a single round.\nFor this reason, it is advisable to carry a few loaded crossbows.  Crossbows can be very difficult to find; however, it is possible to craft one given a high enough Fabrication skill.  Likewise, it is possible to make wooden bolts from any number of wooden objects, though these are much less effective than steel bolts.  Crossbows use the Rifles skill.", "<color_yellow>( Bows</color>\nSilent, deadly, relatively easy to make, and the arrows are reusable.  Bows have been used forever.  Bows are two-handed, and require the user to have a certain strength level.  If you do not have enough strength to draw the string back, you won't be able to use the bow.\nMost normal bows require strength of at least 8, others require an above average strength of 10, or a significant strength of 12.  Bows use the archery skill.", "<color_red>( Shotguns</color>\nShotguns are some of the most powerful weapons in the game, capable of taking out almost any enemy with a single hit.  Birdshot and 00 shot spread out after leaving the barrel, making it very easy to hit nearby monsters.  However, they are very ineffective against armor, and some armored monsters might shrug off 00 shot completely.  Shotgun slugs are the answer to this problem; they also offer much better range than shot.\nThe biggest drawback to shotguns is their noisiness.  They are very loud, and impossible to silence.  A shot that kills one zombie might attract three more!  Beware of that.", "<color_cyan>( Submachine Guns</color>\nSubmachine guns are small weapons (some are barely larger than a handgun), designed for relatively close combat and the ability to spray large numbers of bullets.  However, they are more effective when firing single shots, so use discretion.  They mainly use 9mm and .45 ammunition; however, other SMGs exist.  They reload moderately quickly, and are suitable for close or medium-long range combat.", "<color_brown>( <PERSON><PERSON>per and Marksman Rifles</color>\nSniper and marksman rifles are popular for their superior range and accuracy.  What's more, their scopes or sights make shots fired at targets at very long range as accurate as those fired at shorter range.  Unlike assault rifles, sniper and marksman rifles usually have no automatic fire.  They also may be slow to reload and fire, so when facing a large group of nearby enemies, they are not the best pick.", "<color_blue>( Assault Rifles</color>\nAssault rifles are similar to hunting rifles in many ways; they are also suited for long range combat, with similar bonuses and penalties.  Unlike hunting rifles, assault rifles are capable of automatic fire.  Assault rifles are less accurate than hunting rifles, and this is worsened under automatic fire, so save it for when you're highly skilled.\nAssault rifles are an excellent choice for medium or long range combat, or even close-range bursts against a large number of enemies.  They are difficult to use, and are best used by skilled riflemen.", "<color_light_red>( Machine Guns</color>\nMachine guns are one of the most powerful firearms available.  They are even larger than assault rifles; however, they are capable of holding 100 or more rounds of highly-damaging ammunition.  They are not built for accuracy, and firing single rounds is not very effective.  However, they also possess a very high rate of fire and somewhat low recoil, making them very good at clearing out large numbers of enemies.", "<color_magenta>( Energy Weapons</color>\n\"Energy weapons\" is an umbrella term used to describe a variety of rifles and handguns which fire lasers, plasma, or energy attacks.  They are mostly prototypes that started to appear in military use just prior to the start of the apocalypse, and as such are very difficult to find.\nEnergy weapons have no recoil at all; they are nearly silent, have a long range, and are fairly damaging.  The biggest drawback to energy weapons is scarcity of ammunition; it is wise to reserve the precious ammo for when you really need it."]}, {"type": "help", "order": 21, "name": "<4>: Description of book colors", "messages": ["BOOK COLORS:", "<color_red>Red</color> = You don't yet know what the book is about.  You can skim it to find out.", "<color_blue>Blue</color> = Reading will increase your skills (or learn & level spells in the case of Magiclysm spellbooks).", "<color_pink>Pink</color> = You do not have the necessary skills to understand and improve your skills.", "<color_yellow>Yellow</color> = Reading will not increase your skills, but the book has recipes you haven't learned yet.", "<color_light_gray>Grey</color> = Reading will not increase your skills (or spells for Magiclysm spellbooks), nor will you learn recipes (or spells).", "BOOK RECIPE COLORS", "<color_brown>Brown</color> = You do not have the primary skill requirement to craft that item using the book.", "<color_light_gray>Grey</color> = You can craft the item using the book.  The book must be close by or in your inventory to do this.", "<color_white>White</color> = You already know the recipe, and do not need the book to craft it."]}, {"type": "help", "order": 22, "name": "<5>: FAQ (contains spoilers!)", "messages": ["Q: What is safe mode, and why does it prevent me from moving?\nA: Safe mode is a way to guarantee that you won't die by holding a movement key down.  When a monster comes into view, your movement will be ignored until safe mode is turned off with the <press_safemode> key.  This ensures that the sudden appearance of a monster won't catch you off guard.", "Q: It seems like everything I eat makes me sick!  What's wrong?\nA: Lots of the food found in towns is perishable and will only last a few days after the start of a new game.  The electricity went out several days ago, so fruit, milk, and similar foods are the first to go bad.  After the first couple of days, you should switch to canned food, jerky, and hunting.  Also, you should make sure to cook any food and purify any water you come across, as it may contain parasites or otherwise be unsafe.", "Q: How can I remove boards from boarded-up windows and doors?\nA: Use a hammer and choose the direction of the boarded-up window or door to remove the boards.", "Q: The game just told me to quit, and other weird stuff is happening.\nA: You have the \"Kaluptic Psychosis\" trait, which might make the game seem buggy.", "Q: How can I prevent monsters from attacking me while I sleep?\nA: Find a safe place to sleep; for example, in a cleared building far from the front door.  Set traps if you have them, or build a fire to scare off wild animals.", "Q: Why do I always sink when I try to swim?\nA: Your swimming ability is reduced greatly by the weight you are carrying, and is also adversely affected by most clothing you wear.  Until you reach a high level of the athletics skill, you'll need to drop your equipment and remove your clothing to swim, making it a last-ditch escape plan.  Diving gear may significantly help you in swimming and diving.", "Q: How can I cure a fungal infection?\nA: The Blood Filter bionic and some antifungal chemicals can cure fungal infection.  Antifungal chemicals to cure the fungal infection can either be found as random loot or made from other ingredients.", "Q: How do I get into science labs?\nA: You can enter the front door if you have an appropriate ID card by examining (<press_examine>) the keypad.  If you are skilled in computers and have an electrohack, it is possible to hack the keypad.  An EMP blast has a chance to force the doors open, but it's more likely to break them.  You can also sneak in through the sewers sometimes, or try to smash through the walls with explosions.", "Q: Why does my crafting fail so often?\nA: Check the difficulty of the recipe, and the primary skill used; your skill level should be around one and a half times the difficulty to be confident that it will succeed.  Also, note any proficiencies or other skills that may influence your chances of success.", "Q: Why can't I carry anything?\nA: At the start of the game you only have the space in your pockets.  A good first goal of many survivors is to find a backpack or pouch to store things in.", "Q: Help!  I started a fire and now my house is burning down!\nA: Fires will spread to nearby flammable tiles if they are able.  Lighting a fire in a deployed brazier, stove, wood stove, stone fireplace, pit, or even a kitchen stove will stop it from spreading.  Fire extinguishers can put out fires that get out of control.  You can put out a fire in a fireplace by examining it.", "Q: I'm cold and can't sleep at night!\nA: Gather some clothes and put them in the place where you're trying to sleep.  Being hungry, thirsty, wet, or injured can also make you feel the cold more, so try to avoid these effects before you go to sleep.  Pillows and some other appliances will help you sleep, and the quality of your sleeping spot is also very important.", "Q: There are too many calibers and guns.  It confuses me - I don't usually understand what is compatible with what.\nA: Try to remember a few common calibers:\n9x19 (or simply 9mm) - fits most basic pistols (Glock, for example) and SMGs, quite easy to find and gets job done against usual zombies;\n 00 buckshot - for most shotguns.  Very powerful against unarmored target at closer ranges;\n .223 (5.56) - for rifles.  Good long range option.\n .308 (7.62) - for even more powerful rifles.  Also a good long range option.\n  These should be enough in the beginning.  If you're still confused: just grab any shotgun, fill it with buckshot and give them hell!", "Q: I have a question that's not addressed here.  How can I get an answer?\nA: Ask the helpful people on the forum or chat channels.  Links are provided in the MOTD in the main menu."]}]