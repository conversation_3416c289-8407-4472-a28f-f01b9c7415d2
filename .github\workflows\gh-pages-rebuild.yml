name: Github pages rebuild

on:
  workflow_run:
    workflows: ["Experimental Release"]
    types: [completed]
    branches: [master, 0.H-branch]

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  refresh:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - name: Trigger GitHub pages rebuild
        shell: bash
        run: |
          curl --fail --request POST \
            --url https://api.github.com/repos/${{ github.repository }}/pages/builds \
            --header "Authorization: Bearer ${{ secrets.USER_TOKEN }}" \
            --header "Accept: application/vnd.github.v3+json"
