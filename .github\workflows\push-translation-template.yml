name: "Push translation template"

concurrency: push_transifex

on:
  workflow_run:
    workflows: ["Experimental Release"]
    types:
      - completed


permissions:
  contents: read

jobs:
  push-template:
    runs-on: ubuntu-latest
    if: >
      ${{ github.event.workflow_run.event == 'push' &&
      github.event.workflow_run.conclusion == 'success' &&
      github.event.workflow_run.status == 'success' &&
      github.repository == 'CleverRaven/Cataclysm-DDA' }}
    steps:
      - name: "Install gettext tools"
        run: sudo apt install gettext
      - name: "Install Transifex CLI"
        run: |
          curl -sL https://github.com/transifex/cli/releases/download/v1.6.4/tx-linux-amd64.tar.gz | sudo tar zxvf - -C /usr/bin tx
      - name: "Install Python3"
        run: |
          sudo apt install python3-pip
          sudo pip3 install polib
      - name: "Checkout"
        uses: actions/checkout@v4
      - name: "Generate translation template"
        run: |
          lang/update_pot.sh
      - name: "Push translation template to Transifex server"
        env: 
          TX_TOKEN: ${{ secrets.TX_TOKEN }}
        run: tx push -s
