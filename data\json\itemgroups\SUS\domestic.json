[{"id": "SUS_silverware", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a silverware drawer.", "subtype": "collection", "entries": [{"item": "knife_butter", "count": [1, 6]}, {"item": "knife_butter", "count": [1, 6], "prob": 75}, {"item": "knife_small", "variant": "knife_steak", "count": [2, 6], "prob": 85}, {"item": "knife_small", "variant": "knife_paring", "prob": 75}, {"item": "fork", "count": [1, 6]}, {"item": "fork", "count": [1, 6], "prob": 75}, {"item": "spoon", "count": [1, 6]}, {"item": "spoon", "count": [1, 6], "prob": 75}, {"item": "chopsticks", "prob": 70, "count": [1, 6]}, {"collection": [{"item": "plastic_spoon", "count": [0, 6]}, {"item": "plastic_knife", "count": [0, 6]}, {"item": "plastic_fork", "count": [0, 6]}], "prob": 60}, {"collection": [{"item": "plastic_spoon", "count": [0, 6]}, {"item": "plastic_knife", "count": [0, 6]}, {"item": "plastic_fork", "count": [0, 6]}], "prob": 30}, {"item": "plastic_spoon_kids", "prob": 35, "count": [1, 6]}, {"item": "corkscrew", "prob": 75}, {"item": "bottle_opener", "prob": 75}, {"item": "plastic_straw", "prob": 50, "count": [1, 6]}]}, {"id": "SUS_utensils", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a utensils drawer.", "subtype": "collection", "entries": [{"item": "tongs", "prob": 95}, {"item": "tongs", "prob": 50}, {"item": "ladle", "prob": 95}, {"item": "ladle", "prob": 50}, {"item": "spatula", "prob": 95, "count": [1, 2]}, {"item": "spatula", "prob": 40, "count": [1, 2]}, {"item": "whisk", "prob": 95}, {"item": "whisk", "prob": 50}, {"item": "rolling_pin", "prob": 80}, {"item": "rolling_pin", "prob": 30}, {"item": "can_opener", "prob": 95}, {"item": "can_opener", "prob": 70}, {"item": "peeler", "prob": 95, "count": [1, 2]}, {"item": "peeler", "prob": 75}, {"item": "garlic_press", "prob": 65}, {"item": "potato_masher", "prob": 85}, {"item": "carving_fork", "prob": 50}, {"item": "corkscrew", "prob": 90}, {"item": "bottle_opener", "prob": 95}, {"item": "pizza_cutter", "prob": 30}, {"item": "icecream_scoop", "prob": 25}, {"item": "cheese_grater", "prob": 40}, {"item": "mortar_pestle", "prob": 50}]}, {"id": "SUS_knife_drawer", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a drawer full of knives and related implements.", "subtype": "collection", "entries": [{"distribution": [{"item": "knife_large", "variant": "knife_chef", "prob": 10}, {"item": "knife_cleaver", "variant": "knife_vegetable_cleaver", "prob": 3}]}, {"item": "knife_small", "variant": "knife_paring"}, {"item": "knife_small", "variant": "knife_paring", "prob": 75}, {"item": "knife_large", "variant": "knife_bread", "prob": 95}, {"item": "knife_large", "variant": "knife_bread", "prob": 45}, {"distribution": [{"item": "knife_huge", "variant": "knife_butcher", "prob": 10}, {"item": "knife_cleaver", "variant": "knife_meat_cleaver", "prob": 10}], "prob": 65}, {"item": "knife_small", "variant": "knife_steak", "count": [2, 4], "prob": 75}, {"collection": [{"item": "knife_large", "variant": "knife_carving"}, {"item": "carving_fork", "prob": 90}], "prob": 85}, {"item": "scissors", "prob": 80}, {"item": "peeler", "prob": 75, "count": [1, 2]}, {"item": "cutting_board", "count": [1, 2]}, {"item": "cutting_board", "prob": 75}]}, {"id": "SUS_junk_drawer", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a junk drawer.", "subtype": "collection", "entries": [{"item": "xacto", "prob": 80}, {"item": "scissors", "prob": 80}, {"item": "bag_garbage", "prob": 95, "count": [1, 25]}, {"item": "bag_zipper", "prob": 85, "count": [1, 18]}, {"item": "bag_zipper_gallon", "prob": 80, "count": [1, 9]}, {"item": "screwdriver", "prob": 70}, {"item": "hammer", "prob": 60}, {"item": "pliers", "prob": 60}, {"item": "flashlight", "prob": 75, "charges": [0, 300]}, {"item": "hammer", "prob": 75}, {"item": "permanent_marker", "prob": 75, "charges": [50, -1]}, {"item": "permanent_marker", "prob": 40, "charges": [50, -1]}, {"item": "paper", "prob": 55, "count": [5, 20]}, {"item": "light_battery_cell", "count": [1, 2], "prob": 85, "charges": [1, -1]}, {"item": "battery_charger", "prob": 85}, {"item": "string_36", "count": [1, 4], "prob": 80}, {"item": "string_36", "count": [1, 4], "prob": 50}, {"item": "string_36", "count": [1, 4], "prob": 20}, {"item": "duct_tape", "prob": 40, "charges": [25, 400]}, {"group": "superglue", "prob": 30}, {"item": "plastic_straw", "prob": 70, "count": [1, 6]}, {"item": "plastic_straw", "prob": 40, "count": [1, 6]}, {"item": "corkscrew", "prob": 60}, {"item": "bottle_opener", "prob": 75}, {"item": "candle", "prob": 75, "charges": [10, -1]}, {"item": "extension_cable", "prob": 45}, {"item": "long_extension_cable", "prob": 20}, {"item": "RPG_die", "prob": 10}, {"item": "metal_RPG_die", "prob": 1}, {"item": "aluminum_foil", "prob": 25}, {"item": "wrapper_foil", "prob": 12, "count": [5, 40], "container-item": "wrapper_foil_roll"}, {"item": "nuts_bolts", "prob": 20, "charges": [1, 4]}, {"item": "plastic_shopping_bag", "prob": 60, "count": [1, 10]}, {"distribution": [{"item": "matches", "charges": [1, -1]}, {"item": "lighter", "charges": [1, -1]}, {"item": "small_lighter", "charges": [1, -1]}, {"item": "ref_matches", "charges": [1, -1]}], "prob": 90}]}, {"id": "SUS_junk_drawer_messy", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a messy junk drawer.  The idea is that it should be filled with random crud, much of it obvious trash.  Like you might find in the home of someone with a small hoarding problem.", "subtype": "collection", "entries": [{"item": "cash_card", "count": [1, 3], "charges": [1, 1150]}, {"item": "cash_card", "count": [1, 4], "charges": 0}, {"item": "lighter", "prob": 90, "charges": [0, 2]}, {"item": "bag_garbage", "prob": 95, "count": [1, 25]}, {"item": "bottle_opener", "prob": 90}, {"group": "pens", "prob": 90, "charges": [1, 25]}, {"item": "coin_quarter", "prob": 90}, {"item": "toothbrush_plain", "custom-flags": ["FILTHY"], "prob": 80}, {"item": "matches", "prob": 80, "count": [1, 5], "charges": [0, 2]}, {"item": "ref_matches", "prob": 80, "count": [1, 5], "charges": [0, 2]}, {"item": "coin_nickel", "prob": 70}, {"item": "coin_quarter", "prob": 70}, {"item": "old_key", "prob": 70, "count": [1, 7]}, {"item": "plastic_straw", "prob": 60, "count": [1, 2]}, {"item": "plastic_spoon", "prob": 50}, {"item": "plastic_fork", "prob": 50}, {"item": "plastic_knife", "prob": 50}, {"item": "flyer", "prob": 70}, {"item": "feces_roach", "prob": 70, "count": 4}, {"item": "lighter", "prob": 65, "charges": [0, 9]}, {"item": "light_battery_cell", "count": [1, 7], "prob": 60, "charges": 0}, {"item": "bag_plastic", "prob": 60, "count": [1, 5]}, {"item": "corkscrew", "prob": 60}, {"item": "stapler", "prob": 45}, {"item": "gum", "prob": 55, "count": [1, 2]}, {"item": "can_opener", "prob": 55}, {"item": "ceramic_shard", "prob": 30}, {"item": "e_scrap", "prob": 50, "count": [1, 5]}, {"item": "razor_blade", "prob": 40}, {"item": "flashlight", "prob": 55, "charges": [2, 8]}, {"item": "bottle_plastic_small", "prob": 42}, {"item": "glowstick_dead", "prob": 30}, {"item": "condom", "prob": 40}, {"item": "razor_shaving", "prob": 40}, {"item": "scorecard", "prob": 40}, {"item": "gummy_vitamins", "prob": 40}, {"item": "survnote", "prob": 25}, {"item": "joint_roach", "prob": 30}, {"item": "file", "prob": 30}, {"item": "string_floss", "prob": 30}, {"item": "comb_pocket", "prob": 30}, {"item": "aluminum_foil", "prob": 30, "charges": [1, 4]}, {"item": "fishing_hook_basic", "prob": 25}, {"item": "balloon", "prob": 20, "count": [1, 2]}, {"item": "lighter", "prob": 25, "count": [2, 7], "charges": [0, 12]}, {"group": "adhesive_bandages_box_used", "prob": 25}, {"item": "marble", "prob": 20, "charges": [1, 3]}, {"item": "coin_nickel", "prob": 20, "count": [1, 8]}, {"item": "button_plastic", "prob": 25, "count": [2, 8], "charges": 1}, {"item": "button_steel", "prob": 15, "count": [2, 4], "charges": 1}, {"item": "bearing", "prob": 15, "charges": [1, 3]}, {"prob": 15, "group": "adderall_bottle_plastic_pill_prescription_1"}, {"item": "lighter", "prob": 10, "charges": [0, 90]}, {"item": "zipper_short_plastic", "prob": 10}, {"item": "bracelet_friendship", "prob": 10}, {"item": "paper", "prob": 10, "count": [5, 20]}, {"item": "plastic_straw", "prob": 10, "count": [20, 30]}, {"group": "pens", "prob": 10, "count": [1, 30], "charges": [0, 15]}, {"item": "polaroid_photo", "prob": 10}, {"item": "RPG_die", "prob": 8}, {"item": "bead_necklace", "prob": 8}, {"item": "pipe_cleaner", "prob": 8}, {"item": "ring_engagement", "prob": 8}, {"item": "spoon", "prob": 8}, {"item": "magnifying_glass", "prob": 8}, {"item": "golf_tee", "prob": 8}, {"item": "golf_ball", "prob": 8}, {"item": "shuttlecock", "prob": 8}, {"item": "bronze_medal", "prob": 8}, {"item": "book_binder", "prob": 8}, {"item": "fp_loyalty_card", "prob": 8}, {"item": "coin_quarter", "prob": 6, "count": [2, 12]}, {"item": "wristwatch", "prob": 6}, {"item": "copper_hairpin", "prob": 6}, {"item": "copper_necklace", "prob": 6}, {"item": "dog_whistle", "prob": 6}, {"item": "whistle_multitool", "prob": 6}, {"item": "slingshot", "prob": 5}, {"prob": 5, "group": "weak_antibiotic_bottle_plastic_pill_prescription_1"}, {"item": "paint_brush", "prob": 5}, {"item": "chopsticks", "prob": 2}, {"item": "office_holepunch", "prob": 2, "count": [1, 3]}, {"item": "nuts_bolts", "prob": 50, "charges": [3, 12]}, {"prob": 1, "count": 1, "group": "melatonin_tablet_bottle_plastic_pill_supplement_10"}, {"item": "bat_anklet", "prob": 10}, {"item": "flower_anklet", "prob": 10}, {"item": "woven_anklet", "prob": 10}, {"item": "gold_lip_ring_loop", "prob": 5}, {"item": "gold_lip_ring_stud", "prob": 5}, {"item": "silver_lip_ring_loop", "prob": 6}, {"item": "silver_lip_ring_stud", "prob": 6}, {"item": "gold_nose_ring_loop", "prob": 5}, {"item": "gold_nose_ring_stud", "prob": 5}, {"item": "silver_nose_ring_loop", "prob": 6}, {"item": "silver_nose_ring_stud", "prob": 6}, {"item": "gold_toe_ring", "prob": 5}, {"item": "silver_toe_ring", "prob": 6}]}, {"id": "SUS_junk_drawer_artsy", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a home crafter's junk drawer filled with art supplies.", "subtype": "collection", "entries": [{"item": "pen", "count": [1, 20], "charges": [70, -1]}, {"item": "paper", "charges": [10, 30]}, {"item": "scissors"}, {"item": "scissors_wavy", "prob": 10}, {"item": "string_36", "count": [1, 4], "prob": 80}, {"item": "string_36", "count": [1, 4], "prob": 70}, {"item": "string_6", "count": [1, 20], "prob": 70}, {"item": "permanent_marker", "count": [1, 20], "prob": 80, "charges": [70, -1]}, {"item": "office_holepunch", "prob": 70}, {"item": "stapler", "prob": 70}, {"item": "paint_brush", "prob": 70}, {"item": "xacto", "prob": 65}, {"item": "yarn", "prob": 60, "charges": [60, 200]}, {"item": "razor_blade", "count": [1, 4], "prob": 50}, {"item": "cotton_patchwork", "count": [3, 4], "prob": 50}, {"item": "thread", "prob": 40}, {"item": "thread", "prob": 30}, {"item": "sewing_kit", "prob": 47, "charges": [50, 150]}, {"item": "solder_wire", "prob": 40}, {"group": "superglue", "prob": 40}, {"item": "knitting_needles", "prob": 40}, {"item": "duct_tape", "prob": 40, "charges": [5, 160]}, {"item": "wax_paraffin", "prob": 10}, {"item": "zipper_short_plastic", "prob": 35}, {"item": "zipper_short_plastic", "prob": 15}, {"item": "felt_patch", "count": [1, 3], "prob": 30}, {"item": "bandana", "prob": 30}, {"item": "pin_reamer", "prob": 30}, {"item": "deck_of_cards", "prob": 30}, {"item": "glasses_reading", "prob": 30}, {"item": "button_wood", "prob": 30, "count": [1, 12], "charges": 1}, {"item": "button_plastic", "prob": 30, "count": [2, 18], "charges": 1}, {"item": "bracelet_friendship", "count": [3, 6], "prob": 25}, {"item": "boxcutter", "prob": 20}, {"item": "feather", "prob": 20, "charges": 1}, {"item": "tailors_kit", "prob": 12, "charges": [50, 350]}, {"item": "electric_spinwheel", "prob": 12}, {"item": "polaroid_photo", "prob": 10}, {"item": "RPG_die", "prob": 10}, {"item": "chopsticks", "prob": 10}, {"item": "soldering_iron", "prob": 10}, {"item": "soldering_iron_portable", "prob": 10, "charges": [0, 50]}, {"item": "pipe_cleaner", "count": [3, 15], "prob": 10}, {"item": "balloon", "prob": 8, "count": [10, 20]}, {"item": "bead_bracelet", "count": [5, 9], "prob": 5}, {"item": "bead_necklace", "count": [5, 9], "prob": 5}, {"item": "old_key", "prob": 5, "count": [1, 12]}, {"item": "office_letter_opener", "prob": 5}, {"item": "glowstick_dead", "prob": 5}, {"item": "bat_anklet", "prob": 10}, {"item": "flower_anklet", "prob": 10}, {"item": "woven_anklet", "prob": 10}]}, {"id": "SUS_junk_drawer_handy", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "The junk drawer of a DIYer who does her own home repairs.", "subtype": "collection", "entries": [{"group": "tools_common", "count": 2}, {"group": "superglue", "prob": 90}, {"item": "boxcutter", "prob": 85}, {"item": "duct_tape", "prob": 85, "charges": [5, 200]}, {"item": "flashlight", "prob": 80, "charges": [100, 300]}, {"item": "toothbrush_plain", "custom-flags": ["FILTHY"], "prob": 80}, {"item": "bottle_opener", "prob": 75}, {"item": "pockknife", "prob": 75}, {"item": "cordless_drill", "prob": 70}, {"item": "corded_powerdrill", "prob": 20}, {"item": "scissors", "prob": 65}, {"item": "pliers", "prob": 65}, {"item": "permanent_marker", "prob": 70, "charges": [50, -1]}, {"group": "adhesive_bandages_box_used", "prob": 60}, {"group": "pens", "count": [1, 3], "prob": 55, "charges": [50, -1]}, {"item": "sandpaper", "prob": 50}, {"item": "old_key", "prob": 50}, {"item": "medium_battery_cell", "prob": 40, "charges": [400, 500]}, {"item": "ear_plugs", "prob": 40}, {"item": "screwdriver", "prob": 40}, {"item": "hammer", "prob": 40}, {"item": "multitool", "prob": 35}, {"item": "can_opener", "prob": 35}, {"item": "hinge", "prob": 30, "count": 3}, {"item": "bearing", "prob": 25, "charges": [12, 75]}, {"item": "stapler", "prob": 25}, {"item": "soldering_iron", "prob": 25}, {"item": "soldering_iron_portable", "prob": 150, "charges": [0, 50]}, {"item": "xacto", "prob": 25}, {"item": "kevlar_shears", "prob": 10}, {"item": "tin_snips", "prob": 10}, {"item": "wristwatch", "prob": 25}, {"item": "light_battery_cell", "count": 4, "prob": 25, "charges": 300}, {"item": "old_key", "prob": 20, "count": [1, 6]}, {"item": "office_letter_opener", "prob": 15}, {"item": "razor_blade", "count": [1, 3], "prob": 15}, {"prob": 15, "group": "bottle_otc_painkiller_1_20"}, {"item": "reading_light", "prob": 15, "charges": [0, 50]}, {"item": "book_binder", "prob": 15}, {"item": "paint_brush", "prob": 10}, {"item": "gum", "prob": 10, "count": [1, 10]}, {"item": "comb_pocket", "prob": 10}, {"item": "picklocks", "prob": 5}, {"item": "fitness_band", "prob": 5}, {"item": "thermometer", "prob": 3}, {"item": "hygrometer", "prob": 3}, {"item": "barometer", "prob": 3}, {"item": "smart_lamp", "prob": 3}, {"item": "nuts_bolts", "prob": 95, "charges": [10, 20]}]}, {"id": "SUS_junk_drawer_tidy", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "A well-organized and deliberately selected collection of small objects useful to keep close at hand.", "subtype": "collection", "entries": [{"group": "pens", "count": 2, "charges": [70, -1]}, {"item": "paper", "charges": [25, 100]}, {"item": "scissors", "prob": 80}, {"item": "stapler", "prob": 80}, {"item": "flashlight", "prob": 80, "charges": 300}, {"item": "hairbrush", "prob": 80}, {"item": "office_letter_opener", "prob": 75}, {"item": "candle", "count": [1, 2], "prob": 75, "charges": [70, -1]}, {"item": "corkscrew", "prob": 70}, {"group": "adhesive_bandages_box_used", "prob": 60}, {"item": "duct_tape", "prob": 40, "charges": [100, 400]}, {"group": "superglue", "prob": 30}, {"item": "light_battery_cell", "count": 4, "prob": 50, "charges": 100}, {"group": "batteries", "prob": 50}, {"item": "deck_of_cards", "prob": 50}, {"item": "teleumbrella", "prob": 50}, {"prob": 50, "group": "bottle_otc_painkiller_20"}, {"item": "fancy_sunglasses", "prob": 50}, {"item": "matches", "prob": 50, "charges": 20}, {"item": "ref_matches", "prob": 50, "charges": 32}, {"item": "dog_whistle", "prob": 40}, {"item": "gum", "prob": 40, "count": 10}, {"item": "multitool", "prob": 40}, {"item": "hairpin", "prob": 40}, {"item": "candlestick", "prob": 30}, {"item": "gold_hairpin", "prob": 25}, {"item": "magnifying_glass", "prob": 25}, {"item": "bandana", "prob": 25}, {"item": "light_battery_cell", "count": 4, "prob": 25, "charges": 300}, {"item": "fitness_band", "prob": 15}, {"item": "gloves_light", "prob": 10}, {"item": "gloves_golf", "prob": 10}, {"item": "long_glove_white", "prob": 2}]}, {"id": "SUS_dishes", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a cupboard full of dining dishes.", "subtype": "distribution", "entries": [{"item": "wine_glass", "prob": 50, "count": [1, 4]}, {"item": "bowl_pewter", "prob": 50, "count": [1, 4]}, {"item": "ceramic_cup", "prob": 75, "count": [1, 4]}, {"item": "glass", "count": [1, 6]}, {"item": "ceramic_mug", "count": [1, 6]}, {"item": "ceramic_plate", "count": [1, 6]}, {"item": "ceramic_bowl", "count": [1, 6]}, {"collection": [{"item": "glass_plate", "count": [1, 4]}, {"item": "glass_bowl", "count": [1, 4]}], "prob": 60}, {"collection": [{"item": "tumbler_plastic", "count": [1, 4]}, {"item": "plastic_plate", "count": [1, 4]}], "prob": 60}, {"collection": [{"item": "plastic_bowl_kids", "count": [1, 4]}, {"item": "sippy_cup", "count": [1, 4]}, {"item": "plastic_spoon_kids", "count": [1, 4]}], "prob": 60}]}, {"id": "SUS_cookware", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a cupboard full of pots and pans.", "subtype": "distribution", "entries": [{"item": "pot", "count": [1, 2]}, {"item": "pot", "prob": 50}, {"collection": [{"item": "iron_pot"}, {"item": "iron_pot", "prob": 60}], "prob": 70}, {"item": "casserole", "prob": 90}, {"item": "pot_copper", "prob": 75}, {"item": "stock_pot", "prob": 50}, {"item": "pot_canning", "prob": 25}, {"item": "colander_steel", "prob": 80}, {"item": "splatter_guard", "prob": 20}, {"item": "pan", "count": [1, 2]}, {"item": "steel_pan", "prob": 80}, {"item": "copper_pan", "prob": 30}, {"item": "dutch_oven", "prob": 5}, {"item": "kettle", "prob": 80}, {"item": "coffeepot", "prob": 50}, {"item": "aluminum_pan", "prob": 15}, {"item": "pot_aluminum", "prob": 15}]}, {"id": "SUS_kitchen_sink", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for the cleaning materials under a kitchen sink.", "subtype": "collection", "entries": [{"item": "detergent", "charges": [1, 40], "container-item": "box_small"}, {"item": "cotton_patchwork", "count": [1, 3]}, {"item": "dish_towel", "count": [1, 6]}, {"item": "brush"}, {"item": "sponge"}, {"item": "brush", "prob": 85}, {"item": "plastic_shopping_bag", "prob": 10, "count": [1, 3]}, {"item": "chem_washing_soda", "prob": 50, "charges": [1, -1]}, {"item": "bleach", "prob": 75, "charges": [1, -1]}, {"item": "ammonia_hydroxide", "prob": 65, "charges": [1, -1]}]}, {"id": "SUS_pantry", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a regular stocked kitchen pantry.", "//3": "Frequency of pet food appearing based on percentage of Massachusetts homes with a pet.", "subtype": "collection", "entries": [{"group": "pantry_liquids", "prob": 50, "count": [1, 2]}, {"group": "pasta", "prob": 70, "count": [1, 3]}, {"group": "cannedfood", "prob": 100, "count": [1, 6]}, {"group": "big_canned_food", "prob": 20, "count": [1, 2]}, {"group": "dry_goods", "prob": 80, "count": [1, 3]}, {"group": "preserved_food", "prob": 20, "count": [1, 2]}, {"item": "garlic", "prob": 66, "count": [1, 3]}, {"item": "onion", "prob": 66, "count": [1, 3]}, {"item": "potato", "prob": 30, "count": [2, 12]}, {"group": "pet_food", "prob": 50}]}, {"id": "SUS_appliances_cupboard", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a cupboard where various appliances are stored.", "subtype": "collection", "entries": [{"item": "microwave", "prob": 90}, {"item": "food_processor", "prob": 70}, {"item": "waffleiron", "prob": 45}, {"item": "multi_cooker", "prob": 25}, {"item": "pastaextruder", "prob": 10}, {"item": "dehydrator", "prob": 10}, {"item": "hotplate", "prob": 10}, {"item": "hotplate_induction", "prob": 1}, {"item": "snow_machine_home", "prob": 5}, {"collection": [{"item": "charcoal", "prob": 70}, {"item": "char_smoker", "prob": 100}], "prob": 5}, {"collection": [{"item": "small_propane_tank", "prob": 70, "count": [1, 2], "charges": [1000, 3000]}, {"item": "propane_cooker", "prob": 80}], "prob": 1}, {"item": "can_sealer", "prob": 2}, {"item": "oil_press_manual", "prob": 2}, {"group": "dishes_utility", "prob": 50, "count": [1, 3]}]}, {"id": "SUS_coffee_cupboard", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a cupboard where coffee and tea supplies are kept.", "subtype": "collection", "entries": [{"distribution": [{"item": "coffeemaker", "prob": 90}, {"item": "atomic_coffeepot", "prob": 10}], "prob": 75}, {"distribution": [{"item": "coffee_raw", "prob": 30}, {"item": "instant_coffee", "prob": 40, "count": [1, 5]}, {"item": "coffee_raw", "prob": 30, "count": [1, 200], "container-item": "null", "entry-wrapper": "can_food_big"}], "prob": 80}, {"count": [1, 10], "prob": 80, "group": "tea_raw_bag_plastic_33"}, {"count": [1, 10], "prob": 40, "group": "tea_green_raw_bag_plastic_33"}, {"item": "teapot", "prob": 70}, {"item": "kettle", "prob": 50}, {"prob": 90, "sealed": false, "group": "sugar_jar_glass_sealed_10_140"}, {"distribution": [{"item": "milk_powder", "count": [5, 20], "container-item": "null", "entry-wrapper": "jar_3l_glass_sealed", "prob": 60, "sealed": false}, {"item": "con_milk", "count": [1, 6], "prob": 10}, {"item": "milk_evap", "count": [1, 6], "prob": 20}, {"item": "milk_UHT", "prob": 10}], "prob": 30}, {"distribution": [{"item": "honey_glassed", "prob": 20}, {"item": "honey_bottled", "prob": 80}], "prob": 60}, {"item": "spoon", "count": [1, 6]}, {"item": "ceramic_mug", "count": [1, 6]}, {"group": "teabag_box", "prob": 60}]}, {"id": "SUS_breakfast_cupboard", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a cupboard where breakfast supplies are kept.", "subtype": "collection", "entries": [{"collection": [{"item": "toaster"}, {"distribution": [{"item": "bread", "count": [2, 14], "prob": 70, "container-item": "null", "entry-wrapper": "bag_plastic"}, {"item": "sourdough_bread", "count": [2, 12], "prob": 30, "container-item": "null", "entry-wrapper": "bag_plastic"}], "prob": 80}, {"distribution": [{"item": "toastem", "count": [1, 8], "prob": 40, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "toastem2", "count": [1, 8], "prob": 30, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "toastem3", "count": [1, 8], "prob": 30, "container-item": "null", "entry-wrapper": "box_small"}], "prob": 60}], "prob": 80}, {"item": "jam_fruit", "prob": 80}, {"item": "peanutbutter", "prob": 80}, {"item": "syrup", "prob": 50}, {"distribution": [{"item": "cereal", "prob": 50}, {"item": "cereal2", "prob": 50}, {"item": "cereal3", "prob": 50}]}, {"distribution": [{"item": "cereal", "prob": 50}, {"item": "cereal2", "prob": 50}, {"item": "cereal3", "prob": 50}], "prob": 75}, {"distribution": [{"item": "milk_powder", "count": [5, 20], "container-item": "null", "entry-wrapper": "jar_3l_glass_sealed", "prob": 60, "sealed": false}, {"item": "milk_evap", "count": [1, 6], "prob": 20}, {"item": "milk_UHT", "prob": 20}], "prob": 20}]}, {"id": "SUS_dishwasher", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a dishwasher that may have an assortment of dishes in it.", "subtype": "collection", "entries": [{"group": "dishes_dining", "prob": 50, "count": [1, 20]}, {"group": "dishes_utility", "prob": 50, "count": [1, 20]}]}, {"id": "SUS_oven", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for an oven that might have some pots and pans in the warming drawer.", "subtype": "collection", "entries": [{"distribution": [{"item": "extinguisher", "prob": 20, "charges": 100}, {"item": "sm_extinguisher", "prob": 80, "charges": 10}], "prob": 65}, {"distribution": [{"item": "pot", "prob": 20}, {"item": "tuna_noodle_casserole", "count": 4, "container-item": "null", "entry-wrapper": "casserole", "prob": 10}, {"item": "hot_corn_casserole", "count": 4, "container-item": "null", "entry-wrapper": "casserole", "prob": 10}, {"item": "cheesy_spinach_casserole", "count": 4, "container-item": "null", "entry-wrapper": "casserole", "prob": 5}, {"item": "lazy_lobster_casserole", "count": 4, "container-item": "null", "entry-wrapper": "casserole", "prob": 2}, {"item": "pot_copper", "prob": 5}, {"item": "iron_pot", "prob": 5}], "prob": 25}, {"collection": [{"item": "pan", "count": [1, 2], "prob": 60}, {"item": "steel_pan", "count": [1, 2], "prob": 50}, {"item": "copper_pan", "count": [1, 2], "prob": 30}, {"item": "dutch_oven", "prob": 5}], "prob": 50}]}, {"id": "SUS_spice_collection", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for cupboard, drawer, or rack with spices.", "subtype": "collection", "entries": [{"count": [2, 4], "group": "salt_various"}, {"group": "pepper_various"}, {"prob": 75, "group": "cinnamon_various"}, {"prob": 75, "group": "chilly-p_various"}, {"prob": 15, "group": "sugar_various"}, {"prob": 5, "group": "artificial_sweetener_box_small_71"}, {"prob": 25, "group": "curry_powder_various"}, {"prob": 25, "group": "garlic_powder_various"}, {"prob": 10, "group": "sprinkles_bottle_plastic_small_62"}, {"item": "thyme", "prob": 65}, {"prob": 50, "group": "seasoning_italian_various"}, {"prob": 50, "group": "seasoning_salt_various"}]}, {"id": "SUS_bathroom_sink", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a bathroom sink.", "subtype": "collection", "entries": [{"item": "soap", "count": [1, 4], "prob": 75, "charges": [1, 10]}, {"item": "soap_holder", "prob": 40}, {"item": "razor_shaving", "count": [1, 2], "prob": 20}, {"group": "cotton_ball_bag_used", "prob": 50}, {"group": "cotton_ball_bag_full", "prob": 5}, {"item": "comb_lice", "prob": 2}, {"item": "bleach", "prob": 15, "charges": [1, -1]}, {"item": "ammonia_hydroxide", "prob": 20, "charges": [1, -1]}, {"item": "chem_washing_soda", "prob": 10, "charges": [1, -1]}, {"item": "string_floss", "count": [1, 2], "prob": 60}, {"group": "tampon_box_used", "prob": 10}, {"group": "menstrual_pad_box_used", "prob": 10}, {"item": "menstrual_cup", "prob": 1}, {"item": "toothbrush_plain", "count": [1, 3], "prob": 45}, {"item": "dentures", "count": [1, 2], "prob": 15}, {"item": "toothbrush_electric", "count": [1, 3], "prob": 25}]}, {"id": "SUS_bathroom_medicine", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for the medicine cabinet.", "subtype": "collection", "entries": [{"distribution": [{"item": "aspirin", "prob": 50, "count": [1, 30], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}, {"item": "acetaminophen", "prob": 70, "count": [1, 30], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}, {"item": "ibuprofen", "prob": 80, "count": [1, 30], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}, {"item": "naproxen", "prob": 20, "count": [1, 30], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}, {"item": "codeine", "prob": 30, "count": [1, 15], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}, {"item": "tramadol", "prob": 10, "count": [1, 15], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_painkiller"}], "prob": 95}, {"item": "comb_lice", "prob": 10}, {"item": "eyedrops", "prob": 15, "count": 10}, {"item": "steroid_eyedrops", "prob": 5, "count": 5}, {"item": "pepto", "prob": 60, "charges": [1, -1], "container-item": "bottle_plastic_small"}, {"prob": 60, "group": "tums_bottle_plastic_small_1_20"}, {"item": "inhaler", "prob": 25, "charges": [10, 100]}, {"group": "tampon_box_used", "prob": 20}, {"group": "menstrual_pad_box_used", "prob": 20}, {"item": "menstrual_cup", "prob": 2}, {"item": "disinfectant", "prob": 40, "charges": [1, -1]}, {"group": "alcohol_wipes_box_used", "prob": 60}, {"group": "alcohol_wipes_box_full", "prob": 20}, {"distribution": [{"item": "pills_sleep", "prob": 60, "count": [1, 10], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_prescription"}, {"item": "melatonin_tablet", "prob": 40, "count": [1, 30], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_prescription"}], "prob": 10}, {"distribution": [{"item": "nyquil", "prob": 60, "charges": [1, -1]}, {"item": "dayquil", "prob": 20, "charges": [1, -1]}], "prob": 50}, {"distribution": [{"group": "adhesive_bandages_box_used", "prob": 30}, {"item": "bandages", "prob": 30, "count": [1, 6]}, {"item": "liq_bandage_spray", "prob": 5, "charges": [1, -1]}, {"item": "medical_gauze", "prob": 20}, {"group": "full_1st_aid", "prob": 10}], "prob": 50}, {"distribution": [{"item": "vitamins", "prob": 60, "count": [1, 40], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}, {"item": "gummy_vitamins", "prob": 20, "count": [1, 20], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}, {"item": "vitc_tablet", "prob": 7, "count": [1, 30], "entry-wrapper": "bottle_plastic_small"}, {"item": "calcium_tablet", "prob": 10, "count": [1, 40], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}], "prob": 80}, {"distribution": [{"item": "weak_antibiotic", "prob": 40, "count": [1, 20], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}, {"item": "antibiotics", "prob": 10, "count": [1, 20], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}], "prob": 10}, {"distribution": [{"item": "antifungal", "prob": 5, "count": [1, 10], "container-item": "null", "entry-wrapper": "bottle_plastic_small"}, {"item": "antiparasitic", "prob": 20, "count": [1, 10], "container-item": "null", "entry-wrapper": "bottle_plastic_pill_prescription"}], "prob": 5}]}, {"id": "SUS_hair_drawer", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a drawer for haircare tools.", "subtype": "collection", "entries": [{"item": "hairbrush", "count": [1, 2], "prob": 90}, {"item": "comb_pocket", "count": [1, 2], "prob": 75}, {"item": "scissors", "count": [1, 2], "prob": 60}, {"item": "hair_dryer", "prob": 60}, {"collection": [{"item": "curling_iron", "count": [1, 2]}, {"item": "curler_hair"}], "prob": 25}, {"item": "elec_hairtrimmer", "prob": 30, "charges": [0, 50]}]}, {"id": "SUS_toilet", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for things you often find stored on or next to a toilet.", "subtype": "collection", "entries": [{"distribution": [{"item": "plunger_toilet", "prob": 90}, {"item": "plunger_futuristic", "prob": 10}], "prob": 90}, {"item": "brush_toilet", "prob": 75}, {"item": "toilet_paper", "prob": 95, "container-item": "wrapper"}, {"item": "bathroom_scale", "prob": 50}]}, {"id": "SUS_bathroom_cabinet", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a bathroom cabinet under the sink.", "subtype": "collection", "entries": [{"item": "soap", "count": [1, 4], "prob": 70, "charges": [1, 10]}, {"item": "liquid_soap", "count": [1, 6], "prob": 70, "charges": [1, -1]}, {"item": "bath_toy", "count": [1, 4], "prob": 40}, {"distribution": [{"item": "razor_shaving", "count": [1, 2], "prob": 50}, {"item": "shavingkit", "charges": [0, 10], "prob": 50}], "prob": 75}, {"item": "mirror", "prob": 25}, {"item": "sponge", "count": [1, 3], "prob": 75}, {"item": "chem_hydrogen_peroxide", "prob": 40, "charges": [1, -1]}, {"collection": [{"item": "candle", "count": [1, 2], "charges": [10, -1]}, {"item": "matches", "charges": [1, -1]}, {"item": "ref_matches", "charges": [1, -1]}], "prob": 20}, {"item": "toilet_paper", "prob": 80}, {"item": "towel", "count": [1, 2], "prob": 75}]}, {"id": "SUS_desks_bedroom_unisex", "type": "item_group", "//": "clothing and wearable items for domestic desks, tables, etc.", "subtype": "collection", "entries": [{"group": "accesories_personal_unisex", "prob": 70}, {"group": "accesories_personal_womens", "prob": 50}, {"group": "accesories_personal_mens", "prob": 50}, {"group": "masks_unisex", "prob": 20}, {"group": "socks_unisex", "prob": 60}, {"group": "scarfs_unisex", "prob": 30}, {"group": "gloves_womens", "prob": 10}, {"group": "gloves_unisex", "prob": 30}, {"group": "hats_unisex", "prob": 50}]}, {"id": "SUS_wardrobe_mens", "type": "item_group", "//": "clothing found in a man's wardrobe", "subtype": "collection", "entries": [{"group": "shoes_unisex", "prob": 50}, {"group": "pants_mens", "prob": 1}, {"item": "fishing_waders", "prob": 1}, {"item": "football_armor", "prob": 2}, {"item": "sleeping_bag_roll", "prob": 5}, {"item": "bscabbard", "prob": 1}, {"item": "chaps_leather", "prob": 1}, {"group": "coats_unisex", "prob": 50}, {"group": "suits_unisex", "prob": 50}, {"group": "suits_mens", "prob": 10}, {"group": "bags_unisex", "prob": 20}, {"group": "hats_unisex", "prob": 20}, {"group": "scarfs_unisex", "prob": 30}, {"item": "crutches", "prob": 1}]}, {"id": "SUS_wardrobe_womens", "type": "item_group", "//": "clothing found in a woman's wardrobe", "subtype": "collection", "entries": [{"group": "shoes_unisex", "prob": 50}, {"group": "shoes_womens", "prob": 60}, {"item": "fishing_waders", "prob": 1}, {"item": "football_armor", "prob": 1}, {"item": "pom_poms", "prob": 1}, {"item": "tank_top", "variant": "tank_top_cheerleader", "prob": 1}, {"item": "cheerleader_top_short", "prob": 1}, {"item": "cheerleader_skirt", "prob": 1}, {"item": "cheerleader_skirt_short", "prob": 1}, {"group": "pants_womens", "prob": 10}, {"item": "bscabbard", "prob": 1}, {"item": "sleeping_bag_roll", "prob": 5}, {"item": "chaps_leather", "prob": 1}, {"group": "coats_unisex", "prob": 50}, {"group": "suits_unisex", "prob": 40}, {"group": "suits_womens", "prob": 50}, {"group": "bags_unisex", "prob": 20}, {"group": "hats_unisex", "prob": 20}, {"group": "scarfs_unisex", "prob": 20}, {"item": "crutches", "prob": 1}, {"item": "woven_anklet", "prob": 5}, {"item": "bat_anklet", "prob": 5}, {"item": "flower_anklet", "prob": 5}]}, {"id": "SUS_dresser_mens", "type": "item_group", "//": "clothing found in a man's dresser", "subtype": "collection", "entries": [{"group": "accesories_personal_unisex", "prob": 10}, {"group": "accesories_personal_mens", "prob": 10}, {"group": "masks_unisex", "prob": 3}, {"group": "socks_unisex", "prob": 70}, {"group": "scarfs_unisex", "prob": 30}, {"group": "shirts_unisex", "prob": 50}, {"group": "underwear_mens", "prob": 80}, {"group": "underwear_unisex", "prob": 80}, {"group": "pants_unisex", "prob": 50}, {"group": "pants_male", "prob": 60}, {"group": "gloves_unisex", "prob": 30}, {"group": "hats_unisex", "prob": 10}]}, {"id": "SUS_dresser_womens", "type": "item_group", "//": "clothing found in a woman's dresser", "subtype": "collection", "entries": [{"group": "accesories_personal_unisex", "prob": 30}, {"group": "accesories_personal_womens", "prob": 5}, {"group": "masks_unisex", "prob": 3}, {"group": "socks_unisex", "prob": 70}, {"group": "scarfs_unisex", "prob": 30}, {"group": "shirts_womens", "prob": 60}, {"group": "shirts_unisex", "prob": 50}, {"group": "underwear_womens", "prob": 80}, {"group": "underwear_unisex", "prob": 80}, {"group": "pants_unisex", "prob": 50}, {"group": "pants_female", "prob": 60}, {"group": "gloves_womens", "prob": 5}, {"group": "gloves_unisex", "prob": 30}, {"group": "hats_unisex", "prob": 10}]}, {"type": "item_group", "id": "adderall_bottle_plastic_pill_prescription_1", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_pill_prescription", "entries": [{"item": "adderall", "container-item": "null"}]}, {"type": "item_group", "id": "weak_antibiotic_bottle_plastic_pill_prescription_1", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_pill_prescription", "entries": [{"item": "weak_antibiotic", "container-item": "null"}]}, {"type": "item_group", "id": "garlic_powder_various", "subtype": "distribution", "entries": [{"prob": 18, "group": "garlic_powder_bottle_plastic_small"}, {"prob": 18, "group": "garlic_powder_bottle_glass_small"}, {"prob": 2, "group": "garlic_powder_bottle_plastic"}, {"prob": 2, "group": "garlic_powder_bottle_glass"}, {"prob": 4, "group": "garlic_powder_bag_plastic"}, {"prob": 1, "group": "garlic_powder_bag_paper"}]}, {"type": "item_group", "id": "garlic_powder_bottle_plastic_small", "subtype": "collection", "container-item": "bottle_plastic_seasoning_small", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [10, 50]}]}, {"type": "item_group", "id": "garlic_powder_bottle_glass_small", "subtype": "collection", "container-item": "bottle_glass_seasoning_small", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [10, 50]}]}, {"type": "item_group", "id": "garlic_powder_bottle_plastic", "subtype": "collection", "container-item": "bottle_plastic_seasoning", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [10, 100]}]}, {"type": "item_group", "id": "garlic_powder_bottle_glass", "subtype": "collection", "container-item": "bottle_glass_seasoning", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [10, 100]}]}, {"type": "item_group", "id": "garlic_powder_bag_plastic", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic_small", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [10, 100]}]}, {"type": "item_group", "id": "garlic_powder_bag_paper", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_paper_powder", "entries": [{"item": "garlic_powder", "container-item": "null", "count": [25, 500]}]}, {"type": "item_group", "id": "tums_bottle_plastic_small_1_20", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bottle_plastic_small", "entries": [{"item": "tums", "container-item": "null", "count": [1, 20]}]}]