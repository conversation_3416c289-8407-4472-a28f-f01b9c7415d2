[{"type": "vehicle_part", "id": "ap_apartment_fridge", "name": {"str": "fridge"}, "looks_like": "f_fridge", "categories": ["cargo"], "color": "light_blue", "broken_color": "light_blue", "damage_modifier": 80, "durability": 100, "description": "A standard household refrigerator.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 28 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-42 W", "size": "311 L", "item": "apartment_fridge", "requirements": {"removal": {"time": "2 m"}, "repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}}, "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"group": "apartment_fridge_destroyed"}], "damage_reduction": {"all": 32}, "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_minifridge", "copy-from": "ap_apartment_fridge", "name": {"str": "minifridge"}, "looks_like": "f_fridge", "categories": ["cargo"], "description": "A small fridge.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 30 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-45 W", "size": "96 L", "item": "minifridge", "location": "center", "//1": "20 cm weld per quadrant of damage", "requirements": {"install": {"skills": [["mechanics", 3]], "time": "60 m", "using": [["vehicle_wrench_2", 1]]}, "removal": {"skills": [["mechanics", 2]], "time": "30 m", "using": [["vehicle_wrench_2", 1]]}, "repair": {"skills": [["mechanics", 4]], "time": "20 m", "using": [["repair_welding_standard", 4], ["soldering_standard", 5]]}}, "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"group": "minifridge_destroyed"}], "damage_reduction": {"all": 32}, "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_big_portable_fridge", "copy-from": "ap_apartment_fridge", "name": {"str": "food truck fridge"}, "description": "A heavy, portable fridge.  When turned on, it will cool the food inside, extending the time until the food spoils.", "looks_like": "fridge", "epower": "-51 W", "//": "34 kwh, but since you open it and use it, and because it's upright, 1.5 as much", "size": "250 L", "item": "big_portable_fridge", "breaks_into": [{"group": "big_portable_fridge_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_glass_fridge", "copy-from": "ap_apartment_fridge", "name": {"str": "single glass door fridge"}, "looks_like": "f_glass_fridge", "categories": ["cargo"], "durability": 50, "description": "A commercial refrigerator with a glass door.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 94 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-141 W", "size": "347 L", "item": "glass_fridge", "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"group": "glass_fridge_destroyed"}], "damage_reduction": {"all": 16}}, {"type": "vehicle_part", "id": "ap_glass_fridge_double", "copy-from": "ap_apartment_fridge", "name": {"str": "double glass door fridge"}, "looks_like": "f_glass_fridge", "categories": ["cargo"], "durability": 50, "description": "A commercial refrigerator with two glass doors.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 240 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-360 W", "size": "991 L", "item": "glass_fridge_double", "requirements": {"removal": {"time": "2 m"}, "repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}}, "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"group": "glass_fridge_double_destroyed"}], "damage_reduction": {"all": 16}}, {"type": "vehicle_part", "id": "ap_heavy_duty_fridge", "copy-from": "ap_apartment_fridge", "name": {"str": "heavy-duty fridge"}, "looks_like": "f_fridge", "categories": ["cargo"], "description": "A heavy-duty refrigerator.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 40 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-60 W", "size": "700 L", "item": "heavy_duty_fridge", "requirements": {"removal": {"time": "2 m"}, "repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}}, "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "breaks_into": [{"group": "heavy_duty_fridge_destroyed"}], "damage_reduction": {"all": 32}}, {"type": "vehicle_part", "id": "ap_display_fridge", "copy-from": "ap_apartment_fridge", "name": {"str": "display fridge"}, "looks_like": "f_fridge", "categories": ["cargo"], "description": "A display fridge.  When turned on, it will cool the food inside, extending the time until the food spoils.", "//2": "Rated 137 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-205 W", "size": "600 L", "item": "display_fridge", "requirements": {"removal": {"time": "2 m"}, "repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}}, "breaks_into": [{"group": "display_fridge_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FRIDGE", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"]}, {"type": "vehicle_part", "id": "ap_apartment_freezer", "copy-from": "ap_apartment_fridge", "name": {"str": "freezer"}, "looks_like": "f_fridge", "categories": ["cargo"], "description": "A common upright freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "size": "122 L", "//": "Rated 30 kwh on average, but since you use it, and it's upright, 1.5x as much", "epower": "-45 W", "item": "apartment_freezer", "requirements": {"removal": {"time": "2 m"}, "repair": {"skills": [["mechanics", 4]], "time": "60 m", "using": [["repair_welding_standard", 3], ["soldering_standard", 5]]}}, "breaks_into": [{"group": "apartment_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"]}, {"type": "vehicle_part", "id": "ap_minifreezer", "copy-from": "ap_apartment_freezer", "name": {"str": "minifreezer"}, "description": "A small freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "categories": ["cargo"], "epower": "-46 W", "//": "31 kwh, but since you open and use it, and because it's upright, 1.5x as much", "size": "90 L", "item": "minifreezer", "location": "center", "//1": "20 cm weld per quadrant of damage", "requirements": {"install": {"skills": [["mechanics", 3]], "time": "60 m", "using": [["vehicle_wrench_2", 1]]}, "removal": {"skills": [["mechanics", 2]], "time": "30 m", "using": [["vehicle_wrench_2", 1]]}, "repair": {"skills": [["mechanics", 4]], "time": "20 m", "using": [["repair_welding_standard", 4], ["soldering_standard", 5]]}}, "breaks_into": [{"group": "minifreezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"]}, {"type": "vehicle_part", "id": "ap_chest_minifreezer", "copy-from": "ap_apartment_freezer", "name": {"str": "chest minifreezer"}, "description": "A small chest freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "looks_like": "minifridge", "epower": "-26 W", "//": "22 kwh, but since you open it and use it, 1.2 as much", "//2": "listed capacity is 99L, but internal dimensions are 117L. 100 L for round number.", "item": "chest_minifreezer", "size": "99 L", "breaks_into": [{"group": "chest_minifreezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_big_portable_freezer", "copy-from": "ap_apartment_freezer", "name": {"str": "food truck freezer"}, "description": "A heavy, portable freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "looks_like": "fridge", "epower": "-211 W", "//": "141 kwh, but since you open it and use it, and because it's upright, 1.5 as much", "size": "250 L", "item": "big_portable_freezer", "breaks_into": [{"group": "big_portable_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_chest_freezer", "copy-from": "ap_apartment_freezer", "name": {"str": "chest freezer"}, "description": "A big chest freezer.  When turned on, it will freeze the food inside, preventing any further expiration.  Don't fall in!", "looks_like": "fridge", "epower": "-36 W", "//": "30 kwh, but since you open it and use it, 1.2 as much", "size": "350 L", "item": "chest_freezer", "breaks_into": [{"group": "chest_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_heavy_duty_freezer", "copy-from": "ap_apartment_freezer", "name": {"str": "heavy-duty freezer"}, "description": "A heavy-duty freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "looks_like": "fridge", "epower": "-576 W", "//": "384 kwh, but since you open it and use it, and because it's upright, 1.5 as much", "size": "745 L", "item": "heavy_duty_freezer", "breaks_into": [{"group": "heavy_duty_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_glass_freezer", "copy-from": "ap_apartment_freezer", "name": {"str": "glass door freezer"}, "description": "A glass door freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "looks_like": "fridge", "epower": "-456 W", "//": "304 kwh, but since you open it and use it, and because it's upright, 1.5 as much", "size": "400 L", "item": "glass_freezer", "breaks_into": [{"group": "glass_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}, {"type": "vehicle_part", "id": "ap_display_freezer", "copy-from": "ap_apartment_freezer", "name": {"str": "display freezer"}, "description": "A display freezer.  When turned on, it will freeze the food inside, preventing any further expiration.", "looks_like": "fridge", "epower": "-27 W", "//": "23 kwh, but since you open it and use it, 1.2 as much", "size": "320 L", "item": "display_freezer", "breaks_into": [{"group": "display_freezer_destroyed"}], "flags": ["CARGO", "OBSTACLE", "FREEZER", "COVERED", "ENABLED_DRAINS_EPOWER", "APPLIANCE", "CTRL_ELECTRONIC"], "variants": [{"symbols": "H", "symbols_broken": "#"}]}]