{"problemMatcher": [{"owner": "cata-catch2", "pattern": [{"regexp": "(?:\\(.*\\)=>[\\s\\t]*)(?:.\\[[0-9;]+m)*[\\s\\t]*(?:\\.\\./)*([^:]*):(\\d+): .*FAILED:.*$", "file": 1, "line": 2}, {"regexp": "(?:\\(.*\\)=>[\\s\\t]*)(?:.\\[[0-9;]+m)*[\\s\\t]*(.+)$", "code": 1}, {"regexp": "(?:\\(.*\\)=>[\\s\\t]*)(?:.\\[[0-9;]+m)*[\\s\\t]*(with expansion|due to a fatal error condition):$"}, {"regexp": "(?:\\(.*\\)=>[\\s\\t]*)(?:.\\[[0-9;]+m)*[\\s\\t]*(.+)$", "message": 1}]}]}