[{"id": "SUS_fridge", "type": "item_group", "//": "Select a fridge from several available options.", "subtype": "distribution", "items": [{"group": "SUS_fridge_stocked", "prob": 70}, {"group": "SUS_fridge_partial_empty", "prob": 20}, {"group": "SUS_fridge_bachelor", "prob": 5}, {"group": "SUS_fridge_healthy_vegan", "prob": 5}]}, {"id": "SUS_fridge_stocked", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a well-stocked refrigerator with a variety of things.", "subtype": "collection", "entries": [{"group": "fresh_produce", "count": [1, 8], "prob": 50}, {"distribution": [{"item": "milk", "charges": [1, -1], "prob": 89, "container-item": "jug_plastic", "sealed": false}, {"item": "almond_milk", "charges": [1, -1], "prob": 3, "container-item": "jug_plastic", "sealed": false}, {"item": "soy_milk", "charges": [1, -1], "prob": 3, "container-item": "jug_plastic", "sealed": false}, {"item": "milk_choc", "charges": [1, -1], "prob": 5, "container-item": "jug_plastic", "sealed": false}], "prob": 95}, {"group": "ketchup_sealed_rng", "prob": 85}, {"group": "mustard_sealed_rng", "prob": 85}, {"group": "barbecue_sauce_sealed_rng", "prob": 40}, {"group": "honey_mustard_sealed_rng", "prob": 25}, {"item": "mayonnaise", "charges": [1, -1], "prob": 75}, {"item": "soysauce", "charges": [1, -1], "prob": 65}, {"item": "horseradish", "charges": [1, -1], "prob": 25}, {"prob": 15, "group": "salsa_jar_glass_sealed_1_inf"}, {"item": "jam_fruit", "charges": [1, -1], "prob": 60}, {"prob": 60, "group": "cheese_bag_plastic"}, {"item": "cottage_cheese", "prob": 12}, {"prob": 55, "group": "cheese_hard_wrapper_8"}, {"item": "yoghurt", "prob": 80}, {"prob": 80, "group": "butter_wrapper_32"}, {"item": "pudding", "prob": 30}, {"item": "gelatin_dessert_processed", "prob": 20}, {"prob": 85, "group": "egg_bird_unfert_carton_egg_1_12"}, {"prob": 25, "group": "bacon_bag_plastic_2"}, {"distribution": [{"item": "lunchmeat", "prob": 60}, {"item": "bologna", "prob": 40}, {"item": "tofu", "prob": 30}], "prob": 60}, {"prob": 25, "sealed": false, "group": "sauerkraut_jar_glass_sealed_1_inf"}, {"prob": 60, "sealed": false, "group": "pickle_jar_glass_sealed_1_inf"}, {"prob": 10, "sealed": false, "group": "meat_pickled_jar_glass_sealed_1_inf"}, {"prob": 20, "sealed": false, "group": "fish_pickled_jar_glass_sealed_1_inf"}, {"prob": 40, "sealed": false, "group": "veggy_pickled_jar_glass_sealed_1_inf"}, {"distribution": [{"item": "veggy_salad", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "bowl_plastic"}, {"item": "blt", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "protein_shake", "charges": [1, -1], "prob": 5}, {"item": "fries", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "cheese_fries", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "onion_rings", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "milkshake", "charges": [1, -1], "prob": 10}, {"item": "milkshake_fastfood", "charges": [1, -1], "prob": 10}, {"item": "frozen_lemonade", "charges": [1, -1], "prob": 10}, {"item": "milkshake_deluxe", "charges": [1, -1], "prob": 5}, {"item": "pizza_meat", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_veggy", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_cheese", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_supreme", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "cheeseburger", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "hamburger", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "fish_fried", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "fish_sandwich", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "lobster_roll", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "sloppy<PERSON>e", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "sandwich_t", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "junk_burrito", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "bag_plastic"}, {"item": "chili", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "can_medium"}, {"item": "hotdogs_cooked", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "hotdogs_newyork", "count": [1, -1], "prob": 5, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "crab_cakes", "count": [1, -1], "prob": 5, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "lobster_cakes", "count": [1, -1], "prob": 5, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "stuffed_clams", "count": [1, -1], "prob": 3, "container-item": "null", "entry-wrapper": "box_small"}], "prob": 75}, {"distribution": [{"distribution": [{"collection": [{"item": "water_clean", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "water_clean", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 90}, {"collection": [{"item": "water_mineral", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "water_mineral", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "oj", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "oj", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "pineapple_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "pineapple_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "cranberry_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cranberry_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "sports_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "sports_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "lemonade", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "lemonade", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "cola", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "choc_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "choc_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "rootbeer", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "rootbeer", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 65}, {"collection": [{"item": "tonic_water", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "tonic_water", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "purple_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "purple_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "creamsoda", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "creamsoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "lemonlime", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "lemonlime", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "orangesoda", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "orangesoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "crispycran", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "crispycran", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "colamdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "colamdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "zerocola", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerocola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeroorange", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeroorange", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zerotropical", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerotropical", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeromdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeromdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "foodplace_zero_drink", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "foodplace_zero_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 5}, {"collection": [{"item": "sparkling", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_peach", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_peach", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_pomegranate", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_pomegranate", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}], "prob": 70}, {"distribution": [{"item": "oj", "count": [1, 6], "prob": 50}, {"item": "cranberry_juice", "count": [1, 6], "prob": 50}, {"item": "juice", "count": [1, 6], "prob": 50}, {"item": "tonic_water", "count": [1, 6], "prob": 20}, {"item": "sports_drink", "count": [1, 6], "prob": 30}, {"item": "lemonade", "count": [1, 6], "prob": 50}, {"item": "cola", "count": [1, 6], "prob": 70}, {"item": "choc_drink", "count": [1, 6], "prob": 30}, {"item": "horchata", "count": [1, 6], "prob": 15}, {"item": "rootbeer", "count": [1, 6], "prob": 65}, {"item": "purple_drink", "count": [1, 6], "prob": 35}, {"item": "creamsoda", "count": [1, 6], "prob": 35}, {"item": "lemonlime", "count": [1, 6], "prob": 35}, {"item": "orangesoda", "count": [1, 6], "prob": 20}, {"item": "crispycran", "count": [1, 6], "prob": 20}, {"item": "colamdew", "count": [1, 6], "prob": 35}, {"item": "zerocola", "count": [1, 6], "prob": 35}, {"item": "zeroorange", "count": [1, 6], "prob": 35}, {"item": "zerotropical", "count": [1, 6], "prob": 35}, {"item": "zeromdew", "count": [1, 6], "prob": 35}, {"item": "foodplace_zero_drink", "count": [1, 6], "prob": 10}, {"item": "sparkling", "count": [1, 6], "prob": 20}, {"item": "sparkling_peach", "count": [1, 6], "prob": 20}, {"item": "sparkling_pomegranate", "count": [1, 6], "prob": 20}], "prob": 15}, {"distribution": [{"item": "cola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "creamsoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "lemonlime", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "orangesoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "colamdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "rootbeer", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerocola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeroorange", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerotropical", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeromdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "foodplace_zero_drink", "charges": [1, -1], "prob": 4, "container-item": "bottle_twoliter"}, {"item": "sparkling", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}, {"item": "sparkling_peach", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}, {"item": "sparkling_pomegranate", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}], "prob": 15}], "prob": 90}, {"distribution": [{"item": "beer", "count": [1, 6], "prob": 35}, {"item": "european_pilsner", "count": [1, 6], "prob": 25}, {"item": "pale_ale", "count": [1, 6], "prob": 25}, {"item": "india_pale_ale", "count": [1, 6], "prob": 25}, {"item": "drink_hard_seltzer", "count": [1, 6], "prob": 25}, {"item": "stout", "count": [1, 6], "prob": 15}, {"item": "belgian_ale", "count": [1, 6], "prob": 10}, {"item": "imperial_stout", "count": [1, 6], "prob": 4}], "prob": 70}, {"distribution": [{"item": "apple_cider", "charges": [1, -1], "prob": 20, "container-item": "bottle_plastic", "sealed": false}, {"item": "fruit_wine", "charges": [1, -1], "prob": 15}, {"item": "beer", "charges": [1, -1], "prob": 35}, {"item": "european_pilsner", "charges": [1, -1], "prob": 25}, {"item": "pale_ale", "charges": [1, -1], "prob": 25}, {"item": "india_pale_ale", "charges": [1, -1], "prob": 25}, {"item": "drink_hard_seltzer", "charges": [1, -1], "prob": 25}, {"item": "stout", "charges": [1, -1], "prob": 15}, {"item": "belgian_ale", "charges": [1, -1], "prob": 10}, {"item": "imperial_stout", "charges": [1, -1], "prob": 4}, {"item": "wine_barley", "charges": [1, -1], "prob": 2}], "prob": 60}]}, {"id": "SUS_fridge_bachelor", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a refrigerator belonging to someone who doesn't make much food for themselves.  It is meant to be a bit of a humorous sterotype.", "subtype": "collection", "entries": [{"group": "fresh_produce", "count": [1, 2], "prob": 20}, {"distribution": [{"item": "milk", "charges": [1, 8], "prob": 60, "container-item": "jug_plastic", "sealed": false}, {"item": "milk", "prob": 40, "charges": 1, "count": [2, 3], "container-item": "jug_plastic", "sealed": false}], "prob": 75}, {"group": "ketchup_sealed_rng", "prob": 55}, {"group": "barbecue_sauce_sealed_rng", "prob": 50}, {"group": "honey_mustard_sealed_rng", "prob": 50}, {"group": "mustard_sealed_rng", "prob": 85}, {"distribution": [{"item": "mayonnaise", "charges": [1, -1], "prob": 60}, {"item": "mayonnaise", "prob": 40, "charges": 1, "count": [2, 3], "sealed": false}], "prob": 65}, {"distribution": [{"item": "soysauce", "charges": [1, -1], "prob": 60}, {"item": "soysauce", "prob": 40, "charges": 1, "count": [2, 3], "sealed": false}], "prob": 25}, {"item": "horseradish", "charges": [1, -1], "prob": 5}, {"prob": 5, "group": "salsa_jar_glass_sealed_1_inf"}, {"item": "jam_fruit", "charges": [1, -1], "prob": 40}, {"prob": 60, "group": "cheese_bag_plastic"}, {"item": "cottage_cheese", "prob": 4}, {"prob": 35, "group": "cheese_hard_wrapper_8"}, {"item": "yoghurt", "prob": 20}, {"prob": 30, "group": "butter_wrapper_32"}, {"item": "pudding", "prob": 10}, {"prob": 55, "group": "egg_bird_unfert_carton_egg_1_2"}, {"prob": 65, "group": "bacon_bag_plastic_2"}, {"distribution": [{"item": "pickle", "prob": 60, "count": [1, -1], "entry-wrapper": "jar_glass_sealed", "sealed": false}, {"item": "pickle", "prob": 40, "count": [2, 3], "entry-wrapper": "jar_3l_glass_sealed", "sealed": false}], "prob": 75}, {"prob": 10, "group": "blt_wrapper_1_inf"}, {"item": "protein_shake", "charges": [1, -1], "prob": 5}, {"prob": 10, "group": "fries_box_small_1_inf"}, {"prob": 10, "group": "cheese_fries_box_small_1_inf"}, {"prob": 10, "group": "onion_rings_box_small_1_inf"}, {"item": "milkshake", "charges": [1, -1], "prob": 10}, {"item": "milkshake_fastfood", "charges": [1, -1], "prob": 10}, {"item": "frozen_lemonade", "charges": [1, -1], "prob": 10}, {"item": "milkshake_deluxe", "charges": [1, -1], "prob": 5}, {"prob": 10, "group": "pizza_meat_box_small_1_inf"}, {"prob": 10, "group": "pizza_veggy_box_small_1_inf"}, {"prob": 10, "group": "pizza_cheese_box_small_1_inf"}, {"prob": 10, "group": "pizza_supreme_box_small_1_inf"}, {"prob": 10, "group": "nachosv_bag_plastic_1_inf"}, {"prob": 10, "group": "nachosmc_bag_plastic_1_inf"}, {"prob": 10, "group": "nachosc_bag_plastic_1_inf"}, {"item": "spaghetti_bolognese", "prob": 10, "count": [1, 3]}, {"prob": 10, "group": "cheeseburger_wrapper_1_inf"}, {"prob": 10, "group": "hamburger_wrapper_1_inf"}, {"prob": 10, "group": "fish_fried_box_small_1_inf"}, {"prob": 10, "group": "fish_sandwich_wrapper_1_inf"}, {"prob": 10, "group": "lobster_roll_wrapper_1_inf"}, {"prob": 10, "group": "sloppyjoe_wrapper_1_inf"}, {"prob": 10, "group": "sandwich_t_wrapper_1_inf"}, {"prob": 10, "group": "junk_burrito_bag_plastic_1_inf"}, {"prob": 10, "group": "chili_can_medium_1_inf"}, {"prob": 10, "group": "hotdogs_cooked_wrapper_1_inf"}, {"prob": 5, "group": "hotdogs_newyork_wrapper_1_inf"}, {"prob": 5, "group": "crab_cakes_box_small_1_inf"}, {"prob": 3, "group": "stuffed_clams_box_small_1_inf"}, {"distribution": [{"distribution": [{"collection": [{"item": "sports_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "sports_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "cola", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "choc_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "choc_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "rootbeer", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "rootbeer", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 65}, {"collection": [{"item": "tonic_water", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "tonic_water", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "purple_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "purple_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "creamsoda", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "creamsoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "lemonlime", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "lemonlime", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "orangesoda", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "orangesoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "crispycran", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "crispycran", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "colamdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "colamdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "zerocola", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerocola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeroorange", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeroorange", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zerotropical", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerotropical", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeromdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeromdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "foodplace_zero_drink", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "foodplace_zero_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 5}, {"collection": [{"item": "sparkling", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_peach", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_peach", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_pomegranate", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_pomegranate", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}], "prob": 70}, {"distribution": [{"item": "sports_drink", "count": [1, 6], "prob": 30}, {"item": "cola", "count": [1, 6], "prob": 70}, {"item": "choc_drink", "count": [1, 6], "prob": 30}, {"item": "horchata", "count": [1, 6], "prob": 15}, {"item": "rootbeer", "count": [1, 6], "prob": 65}, {"item": "tonic_water", "count": [1, 6], "prob": 20}, {"item": "purple_drink", "count": [1, 6], "prob": 35}, {"item": "creamsoda", "count": [1, 6], "prob": 35}, {"item": "lemonlime", "count": [1, 6], "prob": 35}, {"item": "orangesoda", "count": [1, 6], "prob": 20}, {"item": "crispycran", "count": [1, 6], "prob": 20}, {"item": "colamdew", "count": [1, 6], "prob": 35}, {"item": "zerocola", "count": [1, 6], "prob": 20}, {"item": "zeroorange", "count": [1, 6], "prob": 20}, {"item": "zerotropical", "count": [1, 6], "prob": 20}, {"item": "zeromdew", "count": [1, 6], "prob": 20}, {"item": "foodplace_zero_drink", "count": [1, 6], "prob": 5}, {"item": "sparkling", "count": [1, 6], "prob": 15}, {"item": "sparkling_peach", "count": [1, 6], "prob": 15}, {"item": "sparkling_pomegranate", "count": [1, 6], "prob": 15}], "prob": 15}, {"distribution": [{"item": "cola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "creamsoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "lemonlime", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "orangesoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "colamdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "rootbeer", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerocola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeroorange", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerotropical", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeromdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "foodplace_zero_drink", "charges": [1, -1], "prob": 4, "container-item": "bottle_twoliter"}, {"item": "sparkling", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}, {"item": "sparkling_peach", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}, {"item": "sparkling_pomegranate", "charges": [1, -1], "prob": 10, "container-item": "bottle_twoliter"}], "prob": 15}], "prob": 70}, {"distribution": [{"item": "beer", "count": [1, 6], "prob": 35}, {"item": "european_pilsner", "count": [1, 6], "prob": 25}, {"item": "pale_ale", "count": [1, 6], "prob": 25}, {"item": "india_pale_ale", "count": [1, 6], "prob": 25}, {"item": "drink_hard_seltzer", "count": [1, 6], "prob": 25}, {"item": "stout", "count": [1, 6], "prob": 15}, {"item": "belgian_ale", "count": [1, 6], "prob": 10}, {"item": "imperial_stout", "count": [1, 6], "prob": 4}], "prob": 80}, {"distribution": [{"item": "apple_cider", "charges": [1, -1], "prob": 20}, {"item": "fruit_wine", "charges": [1, -1], "prob": 15}, {"item": "beer", "charges": [1, -1], "prob": 35}, {"item": "european_pilsner", "charges": [1, -1], "prob": 25}, {"item": "pale_ale", "charges": [1, -1], "prob": 25}, {"item": "india_pale_ale", "charges": [1, -1], "prob": 25}, {"item": "drink_hard_seltzer", "charges": [1, -1], "prob": 25}, {"item": "stout", "charges": [1, -1], "prob": 15}, {"item": "belgian_ale", "charges": [1, -1], "prob": 10}, {"item": "imperial_stout", "charges": [1, -1], "prob": 4}, {"item": "wine_barley", "charges": [1, -1], "prob": 2}], "prob": 70}]}, {"id": "SUS_fridge_healthy_vegan", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a refrigerator belonging to someone who is vegan and avoids junk-food.", "subtype": "collection", "entries": [{"group": "fresh_produce", "count": [1, 8], "prob": 50}, {"distribution": [{"item": "almond_milk", "charges": [1, -1], "prob": 50, "container-item": "jug_plastic", "sealed": false}, {"item": "soy_milk", "charges": [1, -1], "prob": 50, "container-item": "jug_plastic", "sealed": false}], "prob": 95}, {"group": "ketchup_sealed_rng", "prob": 55}, {"item": "soysauce", "charges": [1, -1], "prob": 75}, {"item": "jam_fruit", "charges": [1, -1], "prob": 70}, {"item": "tofu", "prob": 90, "count": [1, 5]}, {"prob": 45, "sealed": false, "group": "sauerkraut_jar_glass_sealed_1_inf"}, {"prob": 60, "sealed": false, "group": "pickle_jar_glass_sealed_1_inf"}, {"prob": 60, "sealed": false, "group": "veggy_pickled_jar_glass_sealed_1_inf"}, {"prob": 50, "group": "veggy_salad_bowl_plastic_1_inf"}, {"item": "protein_shake", "charges": [1, -1], "prob": 45}, {"prob": 10, "group": "fries_box_small_1_inf"}, {"prob": 10, "group": "onion_rings_box_small_1_inf"}, {"prob": 20, "group": "pizza_veggy_box_small_1_inf"}, {"prob": 20, "group": "nachosv_bag_plastic_1_inf"}, {"item": "gelatin_dessert_vegan", "prob": 30}, {"item": "gelatin_dessert_vegan_fruit", "prob": 30}, {"distribution": [{"collection": [{"item": "water_clean", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "water_clean", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 90}, {"collection": [{"item": "water_mineral", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "water_mineral", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "oj", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "oj", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "pineapple_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "pineapple_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "cranberry_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cranberry_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "sports_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "sports_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}], "prob": 80}, {"distribution": [{"item": "beer", "count": [1, 6], "prob": 35}, {"item": "european_pilsner", "count": [1, 6], "prob": 25}, {"item": "pale_ale", "count": [1, 6], "prob": 25}, {"item": "india_pale_ale", "count": [1, 6], "prob": 25}, {"item": "drink_hard_seltzer", "count": [1, 6], "prob": 25}, {"item": "stout", "count": [1, 6], "prob": 15}, {"item": "belgian_ale", "count": [1, 6], "prob": 10}, {"item": "imperial_stout", "count": [1, 6], "prob": 4}], "prob": 40}, {"distribution": [{"item": "apple_cider", "charges": [1, -1], "prob": 20}, {"item": "fruit_wine", "charges": [1, -1], "prob": 15}, {"item": "beer", "charges": [1, -1], "prob": 35}, {"item": "european_pilsner", "charges": [1, -1], "prob": 25}, {"item": "pale_ale", "charges": [1, -1], "prob": 25}, {"item": "india_pale_ale", "charges": [1, -1], "prob": 25}, {"item": "drink_hard_seltzer", "charges": [1, -1], "prob": 25}, {"item": "stout", "charges": [1, -1], "prob": 15}, {"item": "belgian_ale", "charges": [1, -1], "prob": 10}, {"item": "imperial_stout", "charges": [1, -1], "prob": 4}, {"item": "wine_barley", "charges": [1, -1], "prob": 2}], "prob": 40}]}, {"id": "SUS_fridge_partial_empty", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a partially-emptied refrigerator. It's basically the SUS_fridge_stocked item group, just with lower chances to spawn for all items and item groups.", "subtype": "collection", "entries": [{"group": "fresh_produce", "count": [1, 8], "prob": 20}, {"distribution": [{"item": "milk", "charges": [1, -1], "prob": 94, "container-item": "jug_plastic", "sealed": false}, {"item": "almond_milk", "charges": [1, -1], "prob": 3, "container-item": "jug_plastic", "sealed": false}, {"item": "soy_milk", "charges": [1, -1], "prob": 3, "container-item": "jug_plastic", "sealed": false}], "prob": 20}, {"group": "ketchup_sealed_rng", "prob": 20}, {"group": "mustard_sealed_rng", "prob": 20}, {"group": "honey_mustard_sealed_rng", "prob": 5}, {"item": "mayonnaise", "charges": [1, -1], "prob": 20}, {"item": "soysauce", "charges": [1, -1], "prob": 20}, {"item": "horseradish", "charges": [1, -1], "prob": 25}, {"prob": 15, "group": "salsa_jar_glass_sealed_1_inf"}, {"item": "jam_fruit", "charges": [1, -1], "prob": 20}, {"prob": 20, "group": "cheese_bag_plastic"}, {"item": "cottage_cheese", "prob": 2}, {"prob": 20, "group": "cheese_hard_wrapper_8"}, {"item": "yoghurt", "prob": 20}, {"prob": 20, "group": "butter_wrapper_32"}, {"item": "pudding", "prob": 20}, {"item": "gelatin_dessert_processed", "prob": 10}, {"item": "egg_bird_unfert", "prob": 20, "count": [1, 12]}, {"prob": 20, "group": "egg_bird_unfert_carton_egg_1_12"}, {"prob": 25, "group": "bacon_bag_plastic_2"}, {"distribution": [{"item": "lunchmeat", "prob": 60}, {"item": "bologna", "prob": 40}, {"item": "tofu", "prob": 30}], "prob": 20}, {"prob": 25, "sealed": false, "group": "sauerkraut_jar_glass_sealed_1_inf"}, {"prob": 20, "sealed": false, "group": "pickle_jar_glass_sealed_1_inf"}, {"prob": 10, "sealed": false, "group": "meat_pickled_jar_glass_sealed_1_inf"}, {"prob": 20, "sealed": false, "group": "fish_pickled_jar_glass_sealed_1_inf"}, {"prob": 20, "sealed": false, "group": "veggy_pickled_jar_glass_sealed_1_inf"}, {"distribution": [{"item": "veggy_salad", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "bowl_plastic"}, {"item": "blt", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "protein_shake", "charges": [1, -1], "prob": 5}, {"item": "fries", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "cheese_fries", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "onion_rings", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "milkshake", "charges": [1, -1], "prob": 10}, {"item": "milkshake_fastfood", "charges": [1, -1], "prob": 10}, {"item": "frozen_lemonade", "charges": [1, -1], "prob": 10}, {"item": "milkshake_deluxe", "charges": [1, -1], "prob": 5}, {"item": "pizza_meat", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_veggy", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_cheese", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "pizza_supreme", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "cheeseburger", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "hamburger", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "fish_fried", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "fish_sandwich", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "sloppy<PERSON>e", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "sandwich_t", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "junk_burrito", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "bag_plastic"}, {"item": "chili", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "can_medium"}, {"item": "hotdogs_cooked", "count": [1, -1], "prob": 10, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "hotdogs_newyork", "count": [1, -1], "prob": 5, "container-item": "null", "entry-wrapper": "wrapper"}, {"item": "crab_cakes", "count": [1, -1], "prob": 5, "container-item": "null", "entry-wrapper": "box_small"}, {"item": "stuffed_clams", "count": [1, -1], "prob": 3, "container-item": "null", "entry-wrapper": "box_small"}], "prob": 20}, {"distribution": [{"distribution": [{"collection": [{"item": "water_clean", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "water_clean", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 90}, {"collection": [{"item": "water_mineral", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "water_mineral", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "oj", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "oj", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "pineapple_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "pineapple_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "cranberry_juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cranberry_juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "juice", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "juice", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "sports_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "sports_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "lemonade", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "lemonade", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 50}, {"collection": [{"item": "cola", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "cola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 70}, {"collection": [{"item": "choc_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "choc_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 30}, {"collection": [{"item": "rootbeer", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "rootbeer", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 65}, {"collection": [{"item": "tonic_water", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "tonic_water", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "purple_drink", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "purple_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "creamsoda", "container-item": "bottle_plastic", "charges": 1, "prob": 50, "sealed": false}, {"item": "creamsoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "lemonlime", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "lemonlime", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "orangesoda", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "orangesoda", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "crispycran", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "crispycran", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "colamdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "colamdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 35}, {"collection": [{"item": "zerocola", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerocola", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeroorange", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeroorange", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zerotropical", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zerotropical", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "zeromdew", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "zeromdew", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 20}, {"collection": [{"item": "foodplace_zero_drink", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "foodplace_zero_drink", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 5}, {"collection": [{"item": "sparkling", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_peach", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_peach", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}, {"collection": [{"item": "sparkling_pomegranate", "charges": 1, "container-item": "bottle_plastic", "prob": 50, "sealed": false}, {"item": "sparkling_pomegranate", "container-item": "bottle_plastic", "count": [1, 6], "sealed": false}], "prob": 15}], "prob": 20}, {"distribution": [{"item": "oj", "count": [1, 6], "prob": 50}, {"item": "pineapple_juice", "count": [1, 6], "prob": 50}, {"item": "cranberry_juice", "count": [1, 6], "prob": 50}, {"item": "juice", "count": [1, 6], "prob": 50}, {"item": "sports_drink", "count": [1, 6], "prob": 30}, {"item": "lemonade", "count": [1, 6], "prob": 50}, {"item": "cola", "count": [1, 6], "prob": 70}, {"item": "choc_drink", "count": [1, 6], "prob": 30}, {"item": "horchata", "count": [1, 6], "prob": 15}, {"item": "rootbeer", "count": [1, 6], "prob": 65}, {"item": "tonic_water", "count": [1, 6], "prob": 20}, {"item": "purple_drink", "count": [1, 6], "prob": 35}, {"item": "creamsoda", "count": [1, 6], "prob": 35}, {"item": "lemonlime", "count": [1, 6], "prob": 35}, {"item": "orangesoda", "count": [1, 6], "prob": 20}, {"item": "crispycran", "count": [1, 6], "prob": 20}, {"item": "colamdew", "count": [1, 6], "prob": 35}, {"item": "zerocola", "count": [1, 6], "prob": 20}, {"item": "zeroorange", "count": [1, 6], "prob": 20}, {"item": "zerotropical", "count": [1, 6], "prob": 20}, {"item": "zeromdew", "count": [1, 6], "prob": 20}, {"item": "foodplace_zero_drink", "count": [1, 6], "prob": 5}, {"item": "sparkling", "count": [1, 6], "prob": 15}, {"item": "sparkling_peach", "count": [1, 6], "prob": 15}, {"item": "sparkling_pomegranate", "count": [1, 6], "prob": 15}], "prob": 15}, {"distribution": [{"item": "cola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "creamsoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "lemonlime", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "orangesoda", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "colamdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "rootbeer", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerocola", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeroorange", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zerotropical", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "zeromdew", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "foodplace_zero_drink", "charges": [1, -1], "prob": 4, "container-item": "bottle_twoliter"}, {"item": "sparkling", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "sparkling_peach", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}, {"item": "sparkling_pomegranate", "charges": [1, -1], "prob": 15, "container-item": "bottle_twoliter"}], "prob": 15}], "prob": 20}, {"distribution": [{"item": "beer", "count": [1, 6], "prob": 35}, {"item": "european_pilsner", "count": [1, 6], "prob": 25}, {"item": "drink_hard_seltzer", "count": [1, 6], "prob": 25}, {"item": "pale_ale", "count": [1, 6], "prob": 25}, {"item": "india_pale_ale", "count": [1, 6], "prob": 25}, {"item": "stout", "count": [1, 6], "prob": 15}, {"item": "belgian_ale", "count": [1, 6], "prob": 10}, {"item": "imperial_stout", "count": [1, 6], "prob": 4}], "prob": 20}, {"distribution": [{"item": "apple_cider", "charges": [1, -1], "prob": 20, "container-item": "bottle_plastic", "sealed": false}, {"item": "fruit_wine", "charges": [1, -1], "prob": 15}, {"item": "beer", "charges": [1, -1], "prob": 35}, {"item": "european_pilsner", "charges": [1, -1], "prob": 25}, {"item": "pale_ale", "charges": [1, -1], "prob": 25}, {"item": "india_pale_ale", "charges": [1, -1], "prob": 25}, {"item": "drink_hard_seltzer", "charges": [1, -1], "prob": 25}, {"item": "stout", "charges": [1, -1], "prob": 15}, {"item": "belgian_ale", "charges": [1, -1], "prob": 10}, {"item": "imperial_stout", "charges": [1, -1], "prob": 4}, {"item": "wine_barley", "charges": [1, -1], "prob": 2}], "prob": 20}]}, {"id": "SUS_fridge_breakroom", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a refrigerator which can be found in office kitchenettes, or workplace breakrooms.", "subtype": "collection", "entries": [{"item": "apple", "prob": 25, "count": [1, 3]}, {"item": "orange", "prob": 25, "count": [1, 3]}, {"item": "banana", "prob": 25, "count": [1, 3]}, {"item": "pear", "prob": 10, "count": [1, 2]}, {"item": "grapes", "prob": 10}, {"item": "peach", "prob": 5, "count": [1, 2]}, {"item": "watermelon", "prob": 2}, {"item": "melon", "prob": 2}, {"item": "kiwi", "prob": 2, "count": [1, 3]}, {"item": "milk", "charges": [1, -1], "prob": 20, "container-item": "jug_plastic", "sealed": false}, {"item": "milk", "charges": [1, -1], "prob": 55, "container-item": "bottle_twoliter", "sealed": false}, {"item": "almond_milk", "charges": [1, -1], "prob": 1, "container-item": "jug_plastic", "sealed": false}, {"item": "almond_milk", "charges": [1, -1], "prob": 3, "container-item": "bottle_twoliter", "sealed": false}, {"item": "soy_milk", "charges": [1, -1], "prob": 2, "container-item": "jug_plastic", "sealed": false}, {"item": "soy_milk", "charges": [1, -1], "prob": 6, "container-item": "bottle_twoliter", "sealed": false}, {"item": "milk_choc", "charges": [1, -1], "prob": 2, "container-item": "jug_plastic", "sealed": false}, {"item": "milk_choc", "charges": [1, -1], "prob": 6, "container-item": "bottle_twoliter", "sealed": false}, {"item": "horchata", "charges": [1, -1], "prob": 3, "container-item": "bottle_plastic", "sealed": false}, {"prob": 40, "group": "cheese_bag_plastic"}, {"item": "cottage_cheese", "prob": 9}, {"prob": 50, "group": "cheese_hard_wrapper_8"}, {"item": "yoghurt", "count": [1, 3], "prob": 75}, {"prob": 50, "group": "butter_wrapper_32"}, {"item": "pudding", "prob": 30}, {"item": "gelatin_dessert_processed", "prob": 10}, {"prob": 19, "group": "veggy_salad_bowl_plastic_1_inf"}, {"prob": 13, "group": "blt_wrapper_1_inf"}, {"item": "protein_shake", "charges": [1, -1], "prob": 6}, {"prob": 13, "group": "sandwich_t_wrapper_1_inf"}, {"prob": 10, "group": "sandwich_veggy_wrapper_1_inf"}, {"prob": 1, "group": "sandwich_jam_butter_wrapper_1_inf"}, {"prob": 6, "group": "sandwich_jam_wrapper_1_inf"}, {"prob": 6, "group": "sandwich_cheese_wrapper_1_inf"}, {"prob": 6, "group": "sandwich_deluxe_wrapper_1_inf"}, {"prob": 3, "group": "sandwich_reuben_wrapper_1_inf"}, {"prob": 6, "group": "sandwich_pb_wrapper_1_inf"}, {"prob": 6, "group": "sandwich_pbj_wrapper_1_inf"}, {"prob": 6, "group": "fish_sandwich_wrapper_1_inf"}, {"group": "softdrinks_canned", "count": [1, 5], "prob": 75}, {"item": "water_clean", "container-item": "bottle_plastic", "prob": 65, "count": [1, 3], "sealed": false}, {"item": "water_mineral", "container-item": "bottle_plastic", "prob": 35, "count": [1, 2], "sealed": false}]}, {"id": "SUS_fridge_survivor", "type": "item_group", "//": "SUS item groups are collections that contain a reasonable realistic distribution of items that might spawn in a given storage furniture.", "//2": "This group is for a refrigerator which can be found in survivor/bandits locations.", "//3": "It contains mostly canned foods.", "subtype": "collection", "entries": [{"group": "big_canned_food", "count": [3, 6], "prob": 40}, {"group": "foodintincan", "count": [4, 8], "prob": 80}, {"group": "condiments", "count": [1, 2], "prob": 25}, {"group": "preserved_food", "count": [3, 6], "prob": 40}, {"group": "softdrinks_canned", "count": [3, 7], "prob": 75}, {"group": "alcohol_bottled_canned", "count": [2, 4], "prob": 30}, {"item": "water_clean", "container-item": "bottle_plastic", "prob": 65, "count": [2, 6], "sealed": false}]}, {"type": "item_group", "id": "salsa_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "salsa", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "butter_wrapper_32", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "butter", "container-item": "null", "count": 32}]}, {"type": "item_group", "id": "sauerkraut_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "sauerkraut", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "pickle_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "pickle", "count": [1, -1]}]}, {"type": "item_group", "id": "meat_pickled_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "meat_pickled", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "fish_pickled_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "fish_pickled", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "veggy_pickled_jar_glass_sealed_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "jar_glass_sealed", "entries": [{"item": "veggy_pickled", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "egg_bird_unfert_carton_egg_1_2", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "carton_egg", "entries": [{"item": "egg_bird_unfert", "count": [1, 2]}]}, {"type": "item_group", "id": "blt_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "blt", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "fries_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "fries", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "cheese_fries_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "cheese_fries", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "onion_rings_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "onion_rings", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "pizza_meat_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "pizza_meat", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "pizza_veggy_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "pizza_veggy", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "pizza_cheese_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "pizza_cheese", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "pizza_supreme_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "pizza_supreme", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "nachosv_bag_plastic_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic", "entries": [{"item": "nachosv", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "nachosmc_bag_plastic_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic", "entries": [{"item": "nachosmc", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "nachosc_bag_plastic_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic", "entries": [{"item": "nachosc", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "cheeseburger_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "cheeseburger", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "hamburger_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "hamburger", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "fish_fried_box_small_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "box_small", "entries": [{"item": "fish_fried", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "fish_sandwich_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "fish_sandwich", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "lobster_roll_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "lobster_roll", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sloppyjoe_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sloppy<PERSON>e", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_t_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_t", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "junk_burrito_bag_plastic_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bag_plastic", "entries": [{"item": "junk_burrito", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "chili_can_medium_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "can_medium", "entries": [{"item": "chili", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "veggy_salad_bowl_plastic_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "bowl_plastic", "entries": [{"item": "veggy_salad", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_veggy_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_veggy", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_jam_butter_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_jam_butter", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_jam_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_jam", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_cheese_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_cheese", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_deluxe_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_deluxe", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_reuben_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_reuben", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_pb_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_pb", "container-item": "null", "count": [1, -1]}]}, {"type": "item_group", "id": "sandwich_pbj_wrapper_1_inf", "subtype": "collection", "//": "This group was created automatically and may contain errors.", "container-item": "wrapper", "entries": [{"item": "sandwich_pbj", "container-item": "null", "count": [1, -1]}]}]