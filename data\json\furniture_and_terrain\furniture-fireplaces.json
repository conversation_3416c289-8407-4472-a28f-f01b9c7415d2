[{"type": "furniture", "id": "f_fireplace", "name": "fireplace", "symbol": "#", "description": "A common fixture for safely hosting a fire indoors, with a chimney to vent the smoke to the outside.", "bgcolor": "white", "move_cost_mod": 2, "coverage": 50, "required_str": -1, "crafting_pseudo_item": "fake_fireplace", "flags": ["TRANSPARENT", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM", "MINEABLE"], "examine_action": "fireplace", "bash": {"str_min": 30, "str_max": 210, "sound": "crash!", "sound_fail": "whump!", "items": [{"item": "rock", "count": [15, 30]}]}}, {"type": "furniture", "id": "f_woodstove", "name": "wood stove", "symbol": "#", "bgcolor": "red", "description": "A simple metal stove for hosting wood-fueled fires.  Good for cooking or heating food, and safe to use indoors.", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "max_volume": "250 L", "crafting_pseudo_item": "fake_woodstove", "flags": ["TRANSPARENT", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM", "MOUNTABLE"], "examine_action": "fireplace", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [3, 6]}, {"item": "pipe", "prob": 50}]}, "deconstruct": {"items": [{"item": "metal_tank", "count": 1}, {"item": "pipe", "count": 1}]}}, {"type": "furniture", "id": "f_brazier", "name": "brazier", "description": "A raised metal dish in which to safely burn things.", "symbol": "#", "color": "red", "move_cost_mod": 2, "coverage": 35, "required_str": 8, "crafting_pseudo_item": "fake_fireplace", "flags": ["PLACE_ITEM", "TRANSPARENT", "FIRE_CONTAINER", "EASY_DECONSTRUCT"], "deployed_item": "brazier", "deconstruct": {"items": [{"item": "brazier", "count": 1}]}, "examine_action": "fireplace", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [5, 15]}, {"item": "steel_chunk", "count": [2, 6]}, {"item": "sheet_metal_small", "count": [2, 6]}]}}, {"type": "furniture", "id": "f_makeshift_brazier", "name": "makeshift brazier", "looks_like": "bucket", "description": "A metal bucket with holes, in which you can safely start a fire.  It's too small to fit any fuel item larger than some splintered wood.", "symbol": "#", "color": "red", "move_cost_mod": 2, "coverage": 35, "required_str": 4, "max_volume": "5 L", "crafting_pseudo_item": "fake_fireplace", "flags": ["PLACE_ITEM", "TRANSPARENT", "FIRE_CONTAINER", "EASY_DECONSTRUCT"], "deployed_item": "makeshift_brazier", "deconstruct": {"items": [{"item": "makeshift_brazier", "count": 1}]}, "examine_action": "fireplace", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [3, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [1, 3]}]}}, {"type": "furniture", "id": "f_hobo_stove", "name": "hobo stove", "description": "A small improvised wood stove, made from a metal can.  It's too small to fit any fuel much larger than splintered wood.", "symbol": "#", "color": "red", "move_cost_mod": 2, "coverage": 35, "required_str": 4, "flags": ["PLACE_ITEM", "TRANSPARENT", "FIRE_CONTAINER", "EASY_DECONSTRUCT"], "deployed_item": "hobo_stove", "deconstruct": {"items": [{"item": "hobo_stove", "count": 1}]}, "examine_action": "fireplace", "max_volume": "680 ml", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 4]}, {"item": "steel_chunk", "count": [1, 2]}, {"item": "sheet_metal_small", "count": [1, 2]}]}}, {"type": "furniture", "id": "f_portable_stove", "name": "portable stove", "looks_like": "f_woodstove", "symbol": "#", "bgcolor": "red", "description": "A foldable metal stove for hosting wood-fueled fires.  Good for cooking or heating food, and safe to use indoors.", "move_cost_mod": 2, "coverage": 40, "required_str": 4, "max_volume": "20 L", "deployed_item": "portable_stove", "crafting_pseudo_item": "fake_woodstove", "flags": ["TRANSPARENT", "CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM", "MOUNTABLE"], "examine_action": "fireplace", "bash": {"str_min": 6, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 3]}, {"item": "pipe", "prob": 50}]}, "deconstruct": {"items": [{"item": "sheet_metal_small", "count": 4}, {"item": "pipe", "count": 1}]}}, {"type": "furniture", "id": "f_55gal_firebarrel", "name": "fire barrel (200 L)", "description": "A huge metal barrel used to safely contain a fire.  It has multiple holes punched in the sides for air supply.", "symbol": "#", "color": "red", "looks_like": "55gal_drum", "move_cost_mod": 2, "coverage": 35, "required_str": 8, "flags": ["PLACE_ITEM", "TRANSPARENT", "FIRE_CONTAINER", "EASY_DECONSTRUCT"], "deployed_item": "55gal_firebarrel", "deconstruct": {"items": [{"item": "55gal_firebarrel", "count": 1}]}, "examine_action": "fireplace", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [8, 20]}, {"item": "sheet_metal_small", "count": [3, 10]}, {"item": "sheet_metal", "count": [0, 1]}]}}, {"type": "furniture", "id": "f_30gal_firebarrel", "name": "fire barrel (100 L)", "description": "A large metal barrel used to safely contain a fire.  It has multiple holes punched in the sides for air supply.", "symbol": "#", "color": "red", "looks_like": "30gal_drum", "move_cost_mod": 2, "coverage": 35, "required_str": 8, "flags": ["PLACE_ITEM", "TRANSPARENT", "FIRE_CONTAINER", "EASY_DECONSTRUCT"], "deployed_item": "30gal_firebarrel", "deconstruct": {"items": [{"item": "30gal_firebarrel", "count": 1}]}, "examine_action": "fireplace", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [5, 15]}, {"item": "sheet_metal_small", "count": [1, 9]}]}}, {"id": "f_firering", "type": "furniture", "name": "fire ring", "description": "A ring of stones to safely contain a fire.", "symbol": "#", "bgcolor": ["white"], "move_cost_mod": 2, "required_str": -1, "bash": {"str_min": 15, "str_max": 105, "sound": "crash!", "sound_fail": "whump!", "items": [{"item": "rock", "count": [5, 15]}]}, "deconstruct": {"items": [{"item": "rock", "count": 20}]}, "flags": ["TRANSPARENT", "CONTAINER", "FIRE_CONTAINER", "PLACE_ITEM", "EASY_DECONSTRUCT"], "examine_action": "fireplace"}, {"type": "furniture", "id": "f_clay_oven", "name": "clay oven", "looks_like": "f_fireplace", "description": "A simple clay oven with a chimney, with a firebrick alcove in the middle that can be used to cook food or just to contain a fire.", "symbol": "U", "color": "brown", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "crafting_pseudo_item": "clay_dutch_oven", "flags": ["CONTAINER", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "PLACE_ITEM", "MINEABLE"], "deconstruct": {"items": [{"item": "rock", "count": 30}, {"item": "fire_brick", "count": 10}]}, "examine_action": "fireplace", "bash": {"str_min": 18, "str_max": 50, "sound": "crash!", "sound_fail": "whump.", "items": [{"item": "rock", "count": [20, 30]}, {"item": "fire_brick", "count": [4, 8]}]}}]