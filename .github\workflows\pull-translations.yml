name: "Pull translations from Transifex"

on:
  schedule:
    - cron: "42 1 * * 6"

jobs:
  pull-translations:
    if: github.repository == 'CleverRaven/Cataclysm-DDA'
    runs-on: ubuntu-22.04
    steps:
      - name: install gettext tools
        run: sudo apt-get install gettext
      - name: "Install Transifex CLI"
        run: |
          curl -sL https://github.com/transifex/cli/releases/download/v1.6.4/tx-linux-amd64.tar.gz | sudo tar zxvf - -C /usr/bin tx
      - name: "Delete existing i18n branch"
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.TX_PR_CREATOR }}
          script: |
            try {
              await github.rest.git.deleteRef({
                owner: context.repo.owner,
                repo: context.repo.repo,
                ref: "heads/i18n"
              })
            } catch (error) {
              console.log(error)
            }
      - name: "Checkout"
        uses: actions/checkout@v4
      - name: "Get current date"
        uses: josStorer/get-current-time@v2.0.2
        id: current-date
        with:
          format: 'DD MMMM YYYY'
      - name: "Pull translations"
        env:
          TX_TOKEN: ${{ secrets.TX_TOKEN }}
        run: tx pull --force
      - name: "Discard invalid translations"
        run: ./lang/discard_invalid_po.sh
      - name: "Update stats"
        run: ./lang/update_stats.sh
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: Routine i18n updates on ${{ steps.current-date.outputs.formattedTime }}
          token: ${{ secrets.TX_PR_CREATOR }}
          branch: i18n
          base: master
          title: Routine i18n updates on ${{ steps.current-date.outputs.formattedTime }}
          body: "#### Summary\nNone\n\n#### Additional context\n`Translation file changes are intended`"
          labels: Translation
