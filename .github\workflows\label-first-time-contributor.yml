name: Sort new contributors for test approval

on:
  pull_request_target:
    types:
      - opened

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  PR_NUMBER: ${{ github.event.pull_request.number }}

jobs:
  triage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Check if user has any merged PRs in this reposiory
        id: pr-check
        run: |
          author="${{ github.event.pull_request.user.login }}"
          pr_count=$(gh pr list --state merged --author $author --json number | jq 'length')

          echo "Debug: $author with $pr_count merged PRs."
          echo "pr_count=$pr_count" >> $GITHUB_OUTPUT

      - name: Label contributors with no merged PRs
        if: steps.pr-check.outputs.pr_count == 0
        run: |
          gh pr edit "$PR_NUMBER" --add-label "new contributor"
