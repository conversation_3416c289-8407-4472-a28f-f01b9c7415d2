[{"type": "furniture", "id": "f_air_conditioner", "name": "cooling unit", "looks_like": "f_machinery_light", "description": "A large, blocky appliance encased in sheet metal.  This commonplace fixture is used for cooling large indoor areas.", "symbol": "{", "bgcolor": "white", "move_cost_mod": -1, "coverage": 55, "required_str": -1, "deconstruct": {"items": [{"item": "pipe", "count": 1}, {"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "cable", "charges": [1, 15]}, {"item": "cu_pipe", "count": [2, 5]}, {"item": "hose", "count": [0, 2]}, {"item": "thermostat", "count": 1}, {"item": "refrigerant_tank", "count": 1}, {"item": "motor_small", "count": 1}, {"item": "pipe_fittings", "count": [2, 6]}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 2]}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "pipe_fittings", "count": [1, 3]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_air_filter", "name": "central air filter", "description": "A large synthetic membrane used to filter out dust, smoke, mites, and other contaminants from air that passes through it.", "symbol": "#", "bgcolor": "white", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "looks_like": "f_air_conditioner", "deconstruct": {"items": [{"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "sheet_metal", "count": [2, 4]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [3, 6]}, {"item": "motor_small", "count": 1}]}, "bash": {"str_min": 12, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 1]}, {"item": "e_scrap", "count": [5, 10]}, {"item": "plastic_chunk", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_dishwasher", "name": "dishwasher", "looks_like": "f_oven", "description": "A large, boxy machine that uses hot water and soap to efficiently clean batches of dishes.  Now that it's sat powerless for a while, a putrid scent of rot is leaking from inside.", "symbol": "{", "bgcolor": "white", "move_cost_mod": -1, "coverage": 60, "required_str": 13, "max_volume": "120 L", "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "household_dishwasher"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "FLAT_SURF", "MINEABLE", "EASY_DECONSTRUCT", "NO_SELF_CONNECT"], "deconstruct": {"items": [{"item": "household_dishwasher", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "cotton_patchwork", "count": [5, 10]}, {"item": "pipe_fittings", "count": [1, 3]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 1]}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_dryer", "name": "dryer", "description": "A common household appliance used to quickly dry large batches of clothing after they have been washed.", "symbol": "{", "bgcolor": "white", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "max_volume": "37500 ml", "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "FLAT_SURF", "MINEABLE", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "element", "count": [2, 3]}, {"item": "wire_mesh", "count": 1}, {"item": "cable", "charges": [1, 15]}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "motor_tiny", "count": 1}, {"item": "cu_pipe", "count": [1, 3]}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [0, 6]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 4]}, {"item": "pipe_fittings", "count": [1, 3]}, {"item": "element", "count": [1, 3]}, {"item": "cable", "charges": [1, 15]}]}}, {"type": "furniture", "id": "f_home_furnace", "name": "furnace", "looks_like": "t_sewage_pipe", "description": "A gas-powered forced-air central heating unit, with an internal fan to push the air through a building's ventilation system to keep it warm.", "symbol": "0", "bgcolor": "white", "move_cost_mod": -1, "coverage": 60, "required_str": -1, "flags": ["CONTAINER", "PLACE_ITEM", "FIRE_CONTAINER", "SUPPRESS_SMOKE", "BLOCKSDOOR"], "examine_action": "fireplace", "deconstruct": {"items": [{"item": "pipe", "count": 1}, {"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "cable", "charges": [1, 15]}, {"item": "cu_pipe", "count": [2, 5]}, {"item": "thermostat", "count": 1}, {"item": "pipe_fittings", "count": [2, 6]}, {"item": "motor_small", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal", "count": [2, 6]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 2]}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_washer", "name": "washing machine", "description": "An appliance used to clean laundry.  It needs to be plugged into a grid that provides it with electricity and large amounts of water to be used.", "symbol": "{", "bgcolor": "white", "move_cost_mod": -1, "coverage": 60, "required_str": 12, "max_volume": "135 L", "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "household_washing_machine"}, "flags": ["CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "FLAT_SURF", "MINEABLE", "EASY_DECONSTRUCT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "household_washing_machine", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 4]}, {"item": "cable", "charges": [1, 15]}, {"item": "hose", "count": [0, 2]}, {"item": "pipe_fittings", "count": [1, 3]}, {"item": "cu_pipe", "count": [1, 4]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_oven", "name": "oven", "symbol": "#", "description": "A standard electric convection-based oven, commonly used for heating and cooking food.  It can safely contain a fire, although it's not adequately ventilated.  You could disconnect it and plug it into a working power grid, or power it with a UPS.", "color": "dark_gray", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "oven"}, "//": "TODO: Replace FIRE_CONTAINER with some flag choking out small files", "flags": ["PLACE_ITEM", "TRANSPARENT", "EASY_DECONSTRUCT", "FIRE_CONTAINER", "CONTAINER", "BLOCKSDOOR", "MOUNTABLE", "SMALL_HIDE", "NO_SELF_CONNECT"], "deconstruct": {"items": [{"item": "oven", "count": 1}]}, "max_volume": "120 L", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "sheet_metal", "count": [1, 4]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "scrap", "count": [0, 6]}, {"item": "element", "count": [1, 3]}, {"item": "cable", "charges": [1, 3]}]}, "crafting_pseudo_item": "oven"}, {"type": "furniture", "id": "f_gas_oven", "name": "gas oven", "looks_like": "f_oven", "symbol": "#", "description": "A standard gas-powered oven, commonly used for heating and cooking food.  With the state of nearby gas lines unknown, it's best not to put any fire near it.", "color": "dark_gray", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["PLACE_ITEM", "TRANSPARENT", "CONTAINER", "BLOCKSDOOR", "MOUNTABLE", "NO_SELF_CONNECT"], "deconstruct": {"items": [{"item": "sheet_metal", "count": 4}, {"item": "sheet_metal_small", "count": [10, 12]}, {"item": "steel_chunk", "count": [2, 3]}, {"item": "scrap", "count": [4, 6]}, {"item": "cable", "charges": 3}, {"item": "pilot_light", "count": 1}]}, "max_volume": "120 L", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "sheet_metal", "count": [1, 4]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "scrap", "count": [0, 6]}, {"item": "cable", "charges": [1, 3]}, {"item": "pilot_light", "count": 1}]}}, {"type": "furniture", "id": "f_gas_oven_microwave_combo", "name": "gas oven and microwave combo", "copy-from": "f_gas_oven", "symbol": "#", "description": "A standard gas-powered oven, commonly used for heating and cooking food.  This one has an attached microwave.  With the state of nearby gas lines unknown, it's best not to put any fire near it.  You could disassemble the shelving holding them together and pull the microwave out.", "color": "dark_gray", "flags": ["PLACE_ITEM", "TRANSPARENT", "EASY_DECONSTRUCT", "CONTAINER", "BLOCKSDOOR", "MOUNTABLE", "NO_SELF_CONNECT", "SMALL_HIDE"], "deconstruct": {"items": [{"item": "microwave", "count": 1}], "furn_set": "f_gas_oven"}, "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "sheet_metal", "count": [1, 4]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "scrap", "count": [0, 6]}, {"item": "cable", "charges": [1, 3]}, {"item": "pilot_light", "count": 1}, {"item": "microwave", "count": 1}]}}, {"type": "furniture", "id": "f_bellows", "name": "blacksmith bellows", "symbol": "#", "description": "An old device for pushing air into a blacksmith's forge to strengthen the fire and maintain a high temperature.  Useless in its current state, but good for parts.", "color": "dark_gray", "looks_like": "f_machinery_old", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["TRANSPARENT"], "deconstruct": {"items": [{"item": "tanned_hide", "count": [1, 2]}, {"item": "2x4", "count": [2, 4]}, {"item": "spring", "count": [1, 2]}, {"item": "lc_wire", "count": [1, 4]}]}, "max_volume": "40 L", "bash": {"str_min": 8, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "leather", "count": [1, 4]}, {"item": "2x4", "count": [1, 2]}, {"item": "scrap", "count": [0, 6]}, {"item": "lc_wire", "count": [1, 3]}]}}, {"type": "furniture", "id": "f_drophammer", "name": "blacksmith drop hammer", "symbol": "#", "description": "An anvil with a large metal hammer suspended above it in a metal framework.  It would be useful for shaping softened metal plates, or you could use the anvil by itself.", "color": "white", "looks_like": "f_machinery_old", "move_cost_mod": 2, "coverage": 60, "required_str": 10, "flags": ["TRANSPARENT"], "crafting_pseudo_item": "fake_drop_hammer", "deconstruct": {"items": [{"item": "metal_tank", "count": [1, 2]}, {"item": "water_faucet", "count": 2}, {"item": "scrap", "count": [2, 6]}, {"item": "chain", "count": [2, 4]}, {"item": "anvil", "count": 1}, {"item": "pipe", "count": [1, 3]}]}, "max_volume": "40 L", "bash": {"str_min": 12, "str_max": 30, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "water_faucet", "count": [1, 2]}, {"item": "anvil", "count": 1}, {"item": "chain", "count": [1, 3]}, {"item": "scrap", "count": [10, 20]}, {"item": "pipe", "count": [1, 3]}]}}, {"type": "furniture", "id": "f_shredder", "name": "document shredder", "description": "A simple electronic device mounted to a large basket.  It is designed to efficiently destroy paper documents with sensitive information.  Good for parts, as identity theft and corporate espionage probably aren't big concerns anymore.", "symbol": "H", "bgcolor": "white", "move_cost_mod": 5, "required_str": 6, "looks_like": "f_filing_cabinet", "flags": ["TRANSPARENT", "CONTAINER", "PLACE_ITEM", "BLOCKSDOOR", "NO_SELF_CONNECT"], "deconstruct": {"items": [{"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "steel_lump", "count": [1, 3]}, {"item": "plastic_chunk", "count": [1, 3]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "sheet_metal", "count": [2, 4]}, {"item": "pipe", "count": [1, 4]}, {"item": "cable", "charges": [1, 15]}, {"item": "motor_tiny", "count": 1}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [2, 7]}, {"item": "steel_chunk", "count": [0, 3]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "pipe", "count": [1, 2]}, {"item": "cable", "charges": [1, 15]}, {"item": "e_scrap", "count": [5, 10]}, {"item": "plastic_chunk", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_server", "looks_like": "f_utility_shelf", "name": "server stack", "description": "A large rack of specialized computers for storing and transmitting information.  Powerless and largely useless for its intended purpose, the laptops mounted inside can still be used if removed.", "symbol": ":", "color": "blue_white", "move_cost_mod": -1, "coverage": 90, "required_str": 8, "flags": ["BLOCKSDOOR"], "deconstruct": {"items": [{"item": "sheet_metal_small", "count": [4, 6]}, {"item": "plastic_chunk", "count": [2, 12]}, {"item": "pipe", "count": [4, 8]}, {"item": "laptop", "count": [2, 4]}, {"item": "pipe_fittings", "count": [2, 4]}, {"item": "cable", "charges": [5, 10]}]}, "bash": {"str_min": 16, "str_max": 40, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "scrap", "count": [4, 8]}, {"item": "plastic_chunk", "count": [1, 6]}, {"item": "sheet_metal_small", "count": [1, 4]}, {"item": "pipe", "count": 1}, {"item": "e_scrap", "count": [20, 50]}, {"item": "cable", "charges": [2, 8]}]}}, {"type": "furniture", "id": "f_melchior_unit", "name": "supercomputer", "looks_like": "f_server", "description": "Hanging platters assembled into an inverted cone, their positions kept stable by dense threading of copper cabling.", "symbol": "V", "color": "yellow", "move_cost_mod": -1, "coverage": 50, "required_str": -1, "connect_groups": "INDOORFLOOR", "flags": ["TRANSPARENT", "NOITEM", "INDOORS", "SHORT", "PERMEABLE"], "bash": {"str_min": 16, "str_max": 150, "sound": "crunch!", "sound_fail": "whack!", "items": [{"item": "melchior_platter", "count": [4, 6]}, {"item": "RAM", "count": [0, 2], "prob": 50}, {"item": "cable", "charges": [4, 6], "prob": 50}, {"item": "e_scrap", "count": [1, 4], "prob": 50}, {"item": "circuit", "count": [0, 2], "prob": 50}, {"item": "power_supply", "prob": 25}, {"item": "amplifier", "prob": 25}, {"item": "scrap", "count": [2, 6], "prob": 50}]}}, {"type": "furniture", "id": "f_satellite", "name": "large satellite dish", "looks_like": "t_radio_tower", "description": "A large, concave metal panel with simple electronics used to receive signals from satellites.  While the hundreds of expensive machines orbiting the planet will likely continue to function indefinitely, their various purposes have all been lost.", "symbol": ")", "color": "white_green", "move_cost_mod": -1, "coverage": 70, "required_str": -1, "deconstruct": {"items": [{"item": "pipe", "count": [6, 12]}, {"item": "rebar", "count": [6, 12]}, {"item": "scrap", "count": [2, 6]}, {"item": "steel_chunk", "count": [1, 3]}, {"item": "steel_lump", "count": [4, 6]}, {"item": "sheet_metal_small", "count": [0, 4]}, {"item": "sheet_metal", "count": [8, 10]}, {"item": "cable", "charges": [1, 15]}, {"item": "pipe_fittings", "count": [4, 8]}, {"item": "motor_tiny", "count": 2}]}, "bash": {"str_min": 18, "str_max": 50, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "pipe", "count": [1, 10]}, {"item": "rebar", "count": [1, 10]}, {"item": "scrap", "count": [6, 16]}, {"item": "steel_chunk", "count": [1, 4]}, {"item": "steel_lump", "count": [0, 6]}, {"item": "sheet_metal_small", "count": [8, 12]}, {"item": "sheet_metal", "count": [1, 2]}, {"item": "cable", "charges": [1, 15]}, {"item": "scrap_copper", "count": [0, 2]}]}}, {"type": "furniture", "id": "f_solar_unit", "name": "mounted polycrystalline solar panel", "description": "A set of photovoltaic power generators, which turns solar radiation into useable electricity.  While useful before the Cataclysm, they have become priceless tools, invaluable to any survivor.  This one uses less efficient polycrystalline solar panels.", "symbol": "#", "color": "yellow", "move_cost_mod": 2, "required_str": -1, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "ground_solar_panel"}, "flags": ["TRANSPARENT"], "deconstruct": {"items": [{"item": "ground_solar_panel", "count": 1}]}, "bash": {"str_min": 10, "str_max": 20, "sound": "whack!", "sound_fail": "clang!", "items": [{"item": "solar_cell", "count": [4, 20]}, {"item": "scrap", "count": [12, 24]}, {"item": "amplifier", "count": [0, 2]}, {"item": "cable", "charges": [40, 60]}, {"item": "power_supply", "count": [0, 2]}, {"item": "scrap", "count": [16, 24]}, {"item": "plastic_chunk", "count": [4, 8]}, {"item": "steel_chunk", "count": 12}]}}, {"type": "furniture", "id": "f_solar_unit_v2", "copy-from": "f_solar_unit", "name": "mounted monocrystalline solar panel", "description": "A set of photovoltaic power generators, which turns solar radiation into useable electricity.  While useful before the Cataclysm, they have become priceless tools, invaluable to any survivor.  This one uses more efficient monocrystalline solar panels.", "symbol": "#", "color": "light_blue", "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "ground_solar_panel_v2"}, "deconstruct": {"items": [{"item": "ground_solar_panel_v2", "count": 1}]}, "bash": {"str_min": 10, "str_max": 20, "sound": "whack!", "sound_fail": "clang!", "items": [{"item": "solar_cell_v2", "count": [4, 20]}, {"item": "scrap", "count": [12, 24]}, {"item": "amplifier", "count": [0, 2]}, {"item": "cable", "charges": [40, 60]}, {"item": "power_supply", "count": [0, 2]}, {"item": "scrap", "count": [16, 24]}, {"item": "plastic_chunk", "count": [4, 8]}, {"item": "steel_chunk", "count": 12}]}}, {"type": "furniture", "id": "f_oxygen_concentrator", "name": "oxygen concentrator", "description": "A machine capable of generating pure oxygen from the air.  It needs power to function.", "symbol": "&", "color": "light_gray", "move_cost_mod": -1, "required_str": 12, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "oxygen_concentrator"}, "flags": ["OBSTACLE"], "deconstruct": {"items": [{"item": "oxygen_concentrator", "count": 1}]}, "bash": {"str_min": 35, "str_max": 140, "sound": "crunch!", "sound_fail": "clang!", "items": [{"item": "plastic_chunk", "count": [4, 7]}, {"item": "steel_chunk", "count": [3, 6]}, {"item": "sheet_metal_small", "count": [4, 8]}, {"item": "scrap", "count": [4, 7]}, {"item": "element", "count": [0, 2]}, {"item": "glass_shard", "prob": 50}, {"item": "motor_small", "prob": 25}]}}, {"type": "furniture", "id": "f_hd_compressor_unit", "name": "high pressure compressor unit", "description": "A machine designed to compress an input gas to a very high pressure.  It needs large amounts of power to function.", "symbol": "&", "color": "light_gray", "move_cost_mod": -1, "required_str": 16, "examine_action": {"type": "appliance_convert", "furn_set": "f_null", "item": "hd_compressor_unit"}, "flags": ["OBSTACLE"], "deconstruct": {"items": [{"item": "hd_compressor_unit", "count": 1}]}, "bash": {"str_min": 45, "str_max": 180, "sound": "metal screeching!", "sound_fail": "clang!", "items": [{"item": "plastic_chunk", "count": [1, 2]}, {"item": "scrap", "count": [6, 12]}, {"item": "steel_chunk", "count": [8, 16]}, {"item": "sheet_metal_small", "count": [1, 4]}]}}]