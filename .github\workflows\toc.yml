on:
  push:
    branches:
    - master
    paths:
    - 'doc/JSON_INFO.md'
name: TOC Generator
permissions:
  contents: read

jobs:
  generateTOC:
    if: github.repository == 'CleverRaven/Cataclysm-DDA'
    name: Update the table of contents
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npx doctoc doc/JSON_INFO.md --github
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: Update the table of contents
          branch: update-toc
          token: ${{ secrets.TX_PR_CREATOR }}
          title: Update the table of contents in doc/JSON_INFO.md
          body: |
            ```sh
            #### Summary
            None
            npx doctoc doc/JSON_INFO.md --github
            ```
