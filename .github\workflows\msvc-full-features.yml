name: Cataclysm Windows build

on:
  push:
    branches:
    - master
    paths-ignore:
    - 'android/**'
    - 'build-data/osx/**'
    - 'doc/**'
    - 'doxygen_doc/**'
    - 'gfx/**'
    - 'lang/**'
    - 'lgtm/**'
    - 'tools/**'
    - '!tools/format/**'
    - 'utilities/**'
  pull_request:
    branches:
    - master
    paths-ignore:
    - 'android/**'
    - 'build-data/osx/**'
    - 'doc/**'
    - 'doxygen_doc/**'
    - 'gfx/**'
    - 'lang/**'
    - 'lgtm/**'
    - 'tools/**'
    - '!tools/format/**'
    - 'utilities/**'
    types: [opened, reopened, synchronize, ready_for_review]

# We only care about the latest revision of a PR, so cancel previous instances.
concurrency:
  group: msvc-build-${{ github.event.pull_request.number || github.ref_name }}
  cancel-in-progress: true

env:
  # Enable pretty backtraces
  BACKTRACE: 1
  # Compressed size ~1GB based on observations
  CCACHE_LIMIT: 8GB
  CDDA_CCACHE_PATH: ${{ github.workspace }}\ccache\
  CDDA_USE_CCACHE: true
  # There's not enough disk space to build both release and debug versions of
  # our dependencies, so we hack the triplet file to build only release versions
  # Have to use github.workspace because runner namespace isn't available yet.
  VCPKG_OVERLAY_TRIPLETS: ${{ github.workspace }}\.github\vcpkg_triplets
  # Also conveniently disable ccache internal compression
  CCACHE_HARDLINK: true
  # vcpkg with object_creator deps totals ~500MB at this compression level
  ZSTD_CLEVEL: 17

jobs:
  build_catatclysm:
    name: Build
    runs-on: windows-2019
    if: ${{ github.event_name != 'pull_request' || github.event.pull_request.draft == false }}

    steps:
    - uses: actions/checkout@v4

    - name: Setup msys2
      uses: msys2/setup-msys2@v2
      with:
        msystem: mingw64
        install: >-
          gettext
          make

    - name: Add msbuild to PATH
      uses: microsoft/setup-msbuild@v2

    - name: Use GNU tar to enable zstd for actions/cache
      run: |
        echo "Adding GNU tar to PATH"
        echo C:\Program Files\Git\usr\bin>>"%GITHUB_PATH%"
      shell: cmd

    - name: Install stable CMake
      uses: lukka/get-cmake@latest

    - name: Install vcpkg
      uses: lukka/run-vcpkg@v11
      id: runvcpkg
      with:
        vcpkgDirectory: '${{ runner.workspace }}/b/vcpkg'
        vcpkgGitCommitId: '66444e13a86da7087ee24c342f91801cc6eb9877'

    - name: Integrate vcpkg
      run: |
        vcpkg integrate install --vcpkg-root '${{ runner.workspace }}\b\vcpkg'

    - name: Download ccache
      uses: robinraju/release-downloader@v1.9
      with:
        repository: 'ccache/ccache'
        tag: 'v4.6.1'
        fileName: 'ccache-4.6.1-windows-x86_64.zip'

    - name: Install ccache
      id: install-ccache
      run: |
        unzip ccache-4.6.1-windows-x86_64.zip
        cp ccache-4.6.1-windows-x86_64/ccache.exe ccache-4.6.1-windows-x86_64/cl.exe
        cp ccache-4.6.1-windows-x86_64/ccache.exe ccache-4.6.1-windows-x86_64/clang-cl.exe
        mv ccache-4.6.1-windows-x86_64 ${{ env.CDDA_CCACHE_PATH }}

    - name: Get ccache vars
      id: get-vars
      run: |
        echo "datetime=$(/bin/date -u "+%Y%m%d%H%M")" >> $GITHUB_OUTPUT
        echo "ccache-path=$(echo "$APPDATA\\ccache")" >> $GITHUB_OUTPUT
      shell: bash

    - name: ccache cache files
      uses: actions/cache@v4
      with:
        path: ${{ steps.get-vars.outputs.ccache-path }}
        # double-dash after compiler is not a typo, it is to disambiguate between g++-<date> and g++-11-<date> for restore key prefix matching
        key: ccache-${{ github.ref_name }}-${{ runner.os }}-msvc--${{ steps.get-vars.outputs.datetime }}
        restore-keys: |
          ccache-master-${{ runner.os }}-msvc--

    - name: Configure ccache
      run: |
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -M 10G
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -s -v
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -z

    - name: Symlink intermediates to C
      run: |
        mkdir -p "$env:TEMP\objwin"
        New-Item -ItemType SymbolicLink -Target "$env:TEMP\objwin" objwin

    - name: Build
      run: |
          msbuild -m -p:Configuration=Release -p:Platform=x64 "-target:Cataclysm-vcpkg-static;Cataclysm-test-vcpkg-static;JsonFormatter-vcpkg-static" msvc-full-features/Cataclysm-vcpkg-static.sln

    - name: Post-build ccache manipulation
      if: ${{ !failure() }}
      run: |
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -s -v
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -M ${{ env.CCACHE_LIMIT }}
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -c
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -s -v

    - name: clear ccache on PRs
      if: ${{ github.ref_name != 'master' }}
      run: |
        ${{ env.CDDA_CCACHE_PATH }}\ccache.exe -C

    - name: Dump vcpkg logs if build failed
      if: failure()
      run: |
        echo =================================================
        Get-ChildItem "${{ runner.workspace }}/Cataclysm-DDA/msvc-full-features/vcpkg_installed" -Recurse
        echo =================================================
        Get-ChildItem "${{ runner.workspace }}/b/vcpkg/buildtrees" |
        Foreach-Object {
            Get-ChildItem $_.FullName -Filter *.log |
            Foreach-Object {
              echo =================================================
              echo $_.FullName
              echo =================================================
              type $_.FullName
            }
        }

    - name: Compile .mo files for localization
      if: ${{ github.ref_name != 'master' }}
      shell: msys2 {0}
      run: |
          make -C lang -j2
          mkdir -p ./data/mods/TEST_DATA/lang/mo/ru/LC_MESSAGES
          msgfmt -f -o ./data/mods/TEST_DATA/lang/mo/ru/LC_MESSAGES/TEST_DATA.mo ./data/mods/TEST_DATA/lang/po/ru.po

    - name: Enable GitHub Actions problem matchers
      if: ${{ github.ref_name != 'master' }}
      run: |
          Write-Output "::add-matcher::build-scripts/problem-matchers/catch2.json"
          Write-Output "::add-matcher::build-scripts/problem-matchers/debugmsg.json"

    - name: Run tests
      if: ${{ github.ref_name != 'master' }}
      run: |
          .\Cataclysm-test-vcpkg-static-Release-x64.exe --min-duration 20 --rng-seed time

    - name: Dump disk usage logs if job failed
      if: failure()
      run: |
        echo =================================================
        df
        echo =================================================
        du -sh *
        echo =================================================
        du -sh msvc-full-features/*
        echo =================================================
        du -sh tools/*
        echo =================================================
      shell: bash

    - name: Don't upload vcpkg cache on failure or PRs
      if: ${{ failure() || cancelled() || github.ref_name != 'master' }}
      run: |
        echo "RUNVCPKG_NO_CACHE=1" >> $GITHUB_ENV
      shell: bash
