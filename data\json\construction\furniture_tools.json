[{"type": "construction", "id": "constr_anvil", "group": "place_anvil", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["anvil", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_anvil", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "constr_anvil_bronze", "group": "place_anvil_bronze", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["anvil_bronze", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_anvil_bronze", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "constr_anvil_crude", "group": "place_anvil_crude", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["anvil_crude", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_anvil_crude", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "constr_anvil_heavy", "group": "place_anvil_heavy", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["anvil_heavy", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_anvil_heavy", "activity_level": "MODERATE_EXERCISE"}, {"type": "construction", "id": "constr_arcfurnace", "group": "build_arc_furnace", "category": "FURN", "required_skills": [["fabrication", 3]], "time": "120 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 1}], [{"id": "DRILL", "level": 3}], [{"id": "WRENCH", "level": 2}]], "using": [["welding_standard", 250]], "components": [[["fire_brick", 40]], [["cu_pipe", 4]], [["cable", 12]], [["pipe", 4]], [["clamp", 1]], [["motor_tiny", 1]], [["medium_storage_battery", 2]], [["frame", 1]], [["mortar_build", 10], ["mortar_lime", 10]]], "pre_special": "check_empty", "post_terrain": "f_arcfurnace_empty"}, {"type": "construction", "id": "constr_bellows", "skill": "fabrication", "group": "build_a_bellow", "//": "faction camp workshop recipe", "category": "CONSTRUCT", "difficulty": 1, "time": "60m", "on_display": false, "qualities": [{"id": "HAMMER", "level": 1}], "components": [[["2x4", 4]], [["tanned_hide", 2]], [["scrap", 20]], [["lc_wire", 8]]], "pre_flags": ["FLAT"], "post_terrain": "f_bellows"}, {"type": "construction", "id": "constr_butcher_rack", "group": "build_butchering_rack", "category": "FURN", "required_skills": [["fabrication", 1], ["survival", 3]], "time": "45 m", "qualities": [[{"id": "CUT", "level": 2}], [{"id": "SAW_W", "level": 1}]], "components": [[["stick_long", 6]], [["rope_natural_short", 1, "LIST"], ["cordage", 2, "LIST"], ["lc_wire", 8]], [["pointy_stick", 2], ["spike", 2]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_butcher_rack"}, {"type": "construction", "id": "constr_chain_hoist_metal", "group": "build_chain_hoist", "category": "TOOL", "time": "30 m", "required_skills": [["fabrication", 3]], "qualities": [[{"id": "DRILL", "level": 3}], [{"id": "WRENCH", "level": 2}]], "components": [[["chain", 1]], [["block_and_tackle", 1]], [["nuts_bolts", 2]]], "pre_terrain": "t_metal_floor", "post_terrain": "f_hoist_chain"}, {"type": "construction", "id": "constr_chain_hoist_strconc", "group": "build_chain_hoist", "category": "TOOL", "time": "30 m", "required_skills": [["fabrication", 3]], "qualities": [[{"id": "DRILL", "level": 3}], [{"id": "WRENCH", "level": 2}]], "components": [[["chain", 1]], [["block_and_tackle", 1]], [["nuts_bolts", 2]]], "pre_terrain": "t_strconc_floor", "post_terrain": "f_hoist_chain"}, {"type": "construction", "id": "constr_chain_hoist_thconc", "group": "build_chain_hoist", "category": "TOOL", "time": "30 m", "required_skills": [["fabrication", 3]], "qualities": [[{"id": "DRILL", "level": 2}], [{"id": "WRENCH", "level": 2}]], "components": [[["chain", 1]], [["block_and_tackle", 1]], [["nuts_bolts", 2]]], "pre_terrain": "t_thconc_floor", "post_terrain": "f_hoist_chain"}, {"type": "construction", "id": "constr_clay_kiln", "group": "build_clay_kiln", "category": "FURN", "required_skills": [["fabrication", 4]], "time": "150 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["rock", 40]], [["material_cement", 50], ["mortar_build", 1], ["mortar_lime", 1], ["clay_lump", 12]], [["water", 2], ["water_clean", 2]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_clay_kiln"}, {"type": "construction", "id": "constr_clayoven", "group": "build_clay_oven", "category": "FURN", "required_skills": [["fabrication", 4], ["survival", 2]], "time": "3 h", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "DIG", "level": 2}]], "components": [[["rock", 30]], [["fire_brick", 10]], [["clay_lump", 15]], [["water", 2], ["water_clean", 2]]], "pre_special": "check_empty", "post_terrain": "f_clay_oven"}, {"type": "construction", "id": "constr_compost_empty", "group": "build_compost_tank", "category": "FURN", "required_skills": [["fabrication", 5], ["mechanics", 2]], "time": "2880 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 1}], [{"id": "SMOOTH", "level": 1}]], "using": [["welding_standard", 10]], "components": [[["brick", 600]], [["pipe", 8]], [["pipe_fittings", 8]], [["metal_tank", 1]], [["hose", 2], ["makeshift_hose", 2]], [["mortar_build", 10], ["mortar_lime", 10]], [["water", 80], ["water_clean", 80]]], "pre_terrain": "t_pit", "post_terrain": "f_compost_empty"}, {"type": "construction", "id": "constr_drophammer", "skill": "fabrication", "group": "build_a_drop_hammer", "//": "faction camp workshop recipe", "category": "CONSTRUCT", "difficulty": 3, "time": "120m", "on_display": false, "qualities": [{"id": "HAMMER", "level": 1}], "components": [[["metal_tank", 2]], [["water_faucet", 2]], [["anvil", 1]], [["chain", 4]], [["pipe", 3]]], "pre_flags": ["FLAT"], "post_terrain": "f_drophammer"}, {"type": "construction", "id": "constr_forge", "group": "place_forge", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["char_forge", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_forge", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_forge_rock", "group": "build_rock_forge", "category": "FURN", "required_skills": [["fabrication", 5]], "time": "120 m", "qualities": [[{"id": "DIG", "level": 1}], [{"id": "HAMMER", "level": 2}]], "components": [[["rock", 40]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_forge_rock"}, {"type": "construction", "id": "constr_fvat_empty", "group": "build_fermenting_vat", "category": "FURN", "required_skills": [["fabrication", 2], ["cooking", 3]], "time": "90 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_W", "level": 1}]], "components": [[["2x4", 14]], [["nails", 12, "LIST"]], [["sheet_metal_small", 12]], [["water_faucet", 1]]], "pre_special": "check_empty", "post_terrain": "f_fvat_empty"}, {"type": "construction", "id": "constr_fvat_wood_empty", "group": "build_wooden_fermenting_vat", "category": "FURN", "required_skills": [["fabrication", 2], ["cooking", 3]], "time": "180 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_W", "level": 1}], [{"id": "CHISEL_WOOD", "level": 1}]], "components": [[["2x4", 14]], [["stick_long", 6]], [["rope_natural", 1, "LIST"]], [["splinter", 2]]], "pre_special": "check_empty", "post_terrain": "f_fvat_wood_empty"}, {"type": "construction", "id": "constr_hanging_meathook", "group": "hang_hanging_meathook", "category": "CONSTRUCT", "required_skills": [["fabrication", 1]], "time": "15 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SCREW", "level": 1}]], "components": [[["grip_hook", 1]], [["chain", 1]]], "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "f_hanging_meathook"}, {"type": "construction", "id": "constr_kiln_empty", "group": "build_charcoal_kiln", "category": "FURN", "required_skills": [["fabrication", 3]], "time": "120 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "DIG", "level": 1}]], "components": [[["rock", 40]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_kiln_empty"}, {"type": "construction", "id": "constr_kiln_metal_empty", "group": "build_metal_charcoal_kiln", "category": "FURN", "required_skills": [["fabrication", 3]], "time": "60 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 1}]], "components": [[["metal_tank", 4]], [["pipe", 4]]], "pre_special": "check_empty", "post_terrain": "f_kiln_metal_empty"}, {"type": "construction", "id": "constr_place_screw_press", "group": "place_screw_press", "//": "Set up Screw Press", "category": "CONSTRUCT", "required_skills": [["fabrication", 5]], "time": "45 m", "qualities": [{"id": "HAMMER", "level": 2}], "components": [[["screw_press_tool", 1]], [["rock_large", 2], ["rock", 40]], [["wood_panel", 1], ["sheet_metal", 1]]], "post_terrain": "f_screw_press"}, {"type": "construction", "id": "constr_place_water_mill", "group": "place_water_mill", "//": "Set up Water Mill", "category": "CONSTRUCT", "required_skills": [["fabrication", 2]], "time": "40 m", "qualities": [[{"id": "HAMMER", "level": 2}]], "components": [[["water_mill", 1]]], "pre_terrain": "t_water_moving_sh", "post_terrain": "f_water_mill"}, {"type": "construction", "id": "constr_place_wind_mill", "group": "place_wind_mill", "//": "Set up Wind Mill", "category": "CONSTRUCT", "required_skills": [["fabrication", 2]], "time": "40 m", "qualities": [[{"id": "DIG", "level": 1}]], "components": [[["wind_mill", 1]]], "pre_terrain": "t_pit_shallow", "post_terrain": "f_wind_mill"}, {"type": "construction", "id": "constr_rope_hoist_ceiling", "group": "build_rope_hoist", "category": "TOOL", "time": "5 m", "required_skills": [["fabrication", 1]], "pre_note": "Must be built indoors.", "qualities": [[{"id": "HAMMER", "level": 1}]], "components": [[["rope_30", 1]], [["nails", 6, "LIST"]], [["2x4", 2]]], "pre_flags": ["SUPPORTS_ROOF", "INDOORS"], "post_terrain": "f_hoist_rope"}, {"type": "construction", "id": "constr_rope_hoist_tree", "group": "build_rope_hoist", "category": "TOOL", "time": "30 s", "required_skills": [["fabrication", 0]], "pre_note": "Built in a tree.", "components": [[["rope_30", 1]]], "pre_flags": ["SUPPORTS_ROOF", "TREE"], "post_terrain": "f_hoist_rope"}, {"type": "construction", "id": "constr_rope_tackle_hoist_ceiling", "group": "build_rope_tackle_hoist", "category": "TOOL", "time": "10 m", "required_skills": [["fabrication", 0]], "pre_note": "Must be built indoors.", "qualities": [[{"id": "HAMMER", "level": 1}]], "components": [[["rope_30", 1]], [["block_and_tackle", 1]], [["spike", 1]]], "pre_flags": ["SUPPORTS_ROOF", "INDOORS"], "post_terrain": "f_hoist_pulley"}, {"type": "construction", "id": "constr_rope_tackle_hoist_supported", "group": "build_rope_tackle_hoist", "category": "TOOL", "time": "10 m", "required_skills": [["fabrication", 1]], "qualities": [[{"id": "HAMMER", "level": 1}]], "components": [[["rope_30", 1]], [["block_and_tackle", 1]], [["spike", 1]], [["nails", 6, "LIST"]], [["2x4", 2]]], "pre_note": "Must be supported on at least two sides.", "pre_special": "check_support", "post_terrain": "f_hoist_pulley"}, {"type": "construction", "id": "constr_rope_tackle_hoist_tree", "group": "build_rope_tackle_hoist", "category": "TOOL", "time": "10 m", "required_skills": [["fabrication", 0]], "pre_note": "Built in a tree.", "qualities": [[{"id": "HAMMER", "level": 1}]], "components": [[["rope_30", 1]], [["block_and_tackle", 1]], [["spike", 1]]], "pre_flags": ["SUPPORTS_ROOF", "TREE"], "post_terrain": "f_hoist_pulley"}, {"type": "construction", "id": "constr_scutch_breaker", "group": "build_stationary_scutcher", "category": "FURN", "required_skills": [["fabrication", 4]], "time": "2 h", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_W", "level": 1}], [{"id": "CHISEL_WOOD", "level": 1}]], "components": [[["2x4", 8]], [["nails", 30, "LIST"]]], "pre_special": "check_empty", "post_terrain": "f_scutch_breaker"}, {"type": "construction", "id": "constr_smoking_rack", "group": "build_smoking_rack", "category": "FURN", "required_skills": [["fabrication", 3], ["cooking", 2]], "time": "90 m", "qualities": [[{"id": "DIG", "level": 1}], [{"id": "HAMMER", "level": 2}], [{"id": "CUT", "level": 2}], [{"id": "SAW_W", "level": 1}]], "components": [[["stick", 16], ["stick_long", 8], ["2x4", 16]], [["rock", 8]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_smoking_rack"}, {"type": "construction", "id": "constr_spin_wheel", "group": "build_spinwheel", "category": "FURN", "required_skills": [["fabrication", 3], ["mechanics", 2], ["tailor", 1]], "time": "3 h", "qualities": [[{"id": "CUT", "level": 1}], [{"id": "SAW_W", "level": 1}], [{"id": "HAMMER", "level": 1}]], "components": [[["2x4", 20]], [["string_36", 4]], [["nails", 80, "LIST"]]], "pre_special": "check_empty", "post_terrain": "f_spinwheel"}, {"type": "construction", "id": "constr_spin_wheel_cordage", "group": "build_spinwheel", "category": "FURN", "required_skills": [["fabrication", 3], ["mechanics", 2], ["tailor", 1]], "time": "3 h", "qualities": [[{"id": "CUT", "level": 1}], [{"id": "SAW_W", "level": 1}], [{"id": "HAMMER", "level": 1}]], "components": [[["2x4", 20]], [["cordage_36", 4]], [["nails", 80, "LIST"]]], "pre_special": "check_empty", "post_terrain": "f_spinwheel_cordage"}, {"type": "construction", "id": "constr_spin_wheel_leather", "group": "build_spinwheel", "category": "FURN", "required_skills": [["fabrication", 3], ["mechanics", 2], ["tailor", 1]], "time": "3 h", "qualities": [[{"id": "CUT", "level": 1}], [{"id": "SAW_W", "level": 1}], [{"id": "HAMMER", "level": 1}]], "components": [[["2x4", 20]], [["cordage_36_leather", 4]], [["nails", 80, "LIST"]]], "pre_special": "check_empty", "post_terrain": "f_spinwheel_leather"}, {"type": "construction", "id": "constr_standing_tank", "group": "build_standing_tank", "//": "a freestanding metal tank, useful for holding liquids", "category": "FURN", "required_skills": [["fabrication", 2], ["cooking", 1]], "time": "180 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 1}]], "using": [["welding_standard", 5]], "//1": "5cm of weld to join the faucet to the pipe", "components": [[["metal_tank", 4]], [["water_faucet", 1]]], "pre_special": "check_empty", "post_terrain": "f_standing_tank"}, {"type": "construction", "id": "constr_still", "group": "place_still", "category": "FURN", "required_skills": [["fabrication", 0]], "time": "1 m", "components": [[["still", 1]]], "pre_note": "Can be deconstructed without tools.", "pre_special": "check_empty", "post_terrain": "f_still", "activity_level": "LIGHT_EXERCISE"}, {"type": "construction", "id": "constr_storage_tank", "group": "build_storage_tank", "//": "a huge metal tank, useful for holding very large volumes of liquids", "category": "FURN", "required_skills": [["fabrication", 6]], "time": "300 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 1}]], "using": [["welding_standard", 16]], "components": [[["sheet_metal", 24], ["sheet_metal_small", 576]], [["water_faucet", 1]]], "pre_special": "check_empty", "post_terrain": "f_storage_tank"}, {"type": "construction", "id": "constr_wood_keg", "group": "build_wooden_keg", "category": "FURN", "required_skills": [["fabrication", 2], ["cooking", 2]], "time": "120 m", "qualities": [[{"id": "HAMMER", "level": 2}], [{"id": "SAW_M", "level": 2}], [{"id": "SAW_W", "level": 1}]], "components": [[["2x4", 18]], [["nails", 14, "LIST"]], [["sheet_metal", 1]], [["water_faucet", 1]]], "pre_special": "check_empty", "post_terrain": "f_wood_keg"}]