# This workflow runs at midnight on the 1st day of every month
name: "Pull Tileset Updates"

on:
  schedule:
    - cron: "0 0 1 * *"
  workflow_dispatch:

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  TX_TOKEN: ${{ secrets.TX_TOKEN }}

jobs:
  pull-updates:
    if: github.repository == 'CleverRaven/Cataclysm-DDA'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: "Get current date"
        uses: 1466587594/get-current-time@v2
        id: current-date
        with:
          format: 'DD MMMM YYYY'
      - name: "Update Tilesets"
        shell: bash
        run: |
          gh release download -D gfx/ -p '*.zip' -R I-am-Erk/CDDA-Tilesets
          gh release download -D gfx/ -p '*.zip' -R pixel-32/CDDA-tileset

          find gfx/ -name "*.zip" | xargs -I{} unzip -o {} -d gfx/
          rm -fr gfx/*.zip

          # make sure all files are linted just in case
          # FIXME: make json test check gfx folder instead of using this
          make tools/format/json_formatter.cgi
          find gfx/ -name "*.json" -print0 | xargs -0 -L 1 -P $(nproc) tools/format/json_formatter.cgi || exit 0
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: |
            Routine tileset updates on ${{ steps.current-date.outputs.formattedTime }}
          committer: casswedson <<EMAIL>>
          author: casswedson <<EMAIL>>
          token: ${{ secrets.TX_PR_CREATOR }}
          branch: tileset-updates
          delete-branch: true
          base: master
          title: Routine tileset updates on ${{ steps.current-date.outputs.formattedTime }}
          body: "#### Summary\nNone\nAutomatic tileset updates from:\n\n**https://github.com/I-am-Erk/CDDA-Tilesets/releases**\nAltica, BrownLikeBears, ChibiUltica, HollowMoon, Larwick_Overmap, MshockXotto+, NeoDaysTileset, RetroDaysTileset, SmashButton_iso, SurveyorsMap\n\n---\n\n**https://github.com/pixel-32/CDDA-tileset/releases**\nCuteclysm"
