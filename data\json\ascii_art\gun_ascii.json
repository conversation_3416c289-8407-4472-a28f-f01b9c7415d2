[{"type": "ascii_art", "id": "hk_mp5a2", "picture": ["                        ", "      <color_dark_gray>█                 ", " _▄▄▄▄█                 ", " </color><color_light_gray>^</color><color_dark_gray>^~▒█▓▓                ", "    ░██▓                ", "   ▐░██▒▌               ", "   █░██▒▌               ", "   ▐░███▓               ", "   ▐░██▓▒             _ ", "   ▐░██▓▒▌       _▄▄███▌", "   ▐░▒▓░▓∟▄▄▄▄▄█████▀▀^ ", "   ▐▒▒░░░▒░░███▀▀~      ", "   ▐▒▓░░▐▒▒◙            ", "   ▐░▒░░╟▓██▀°∙         ", "   ▐░▒░░</color><color_red>!@</color><color_dark_gray>██   ▐        ", "   ▐▒░░░</color><color_red>{</color><color_dark_gray>◙█</color><color_light_gray>▓</color><color_dark_gray>__╓`        ", "  ▄>░░░░▐██▓▓█▓▄▄_      ", "  █▒░░▒░▐█☻▀▀███████    ", "   </color><color_white>░</color><color_dark_gray>▓▓█▒▒▓    ~▀██▀     ", "   ▐███▓▐▒              ", "    ▀████▒              ", "     ▐███▒              ", "      ████▓             ", "      </color><color_white>░</color><color_dark_gray>████▓            ", "      </color><color_white>▒</color><color_dark_gray>██</color><color_light_gray>•</color><color_dark_gray>█▒▌           ", "      </color><color_white>▒</color><color_dark_gray>▓█▓▓▓░▌          ", "      </color><color_white>▒</color><color_dark_gray>▓▓▒▒▒▒░          ", "      ▐▓▓▓</color><color_white>•</color><color_dark_gray>▓▒▒▒         ", "      ▐████████▒        ", "      ▐▓█☻☻☻☻▲▀▀        "]}, {"type": "ascii_art", "id": "hk_mp5sd", "picture": ["                        ", "     <color_dark_gray>███▌               ", "     ██▓▌               ", "     ██▓▌               ", "     ██▓▌               ", "     ███▌               ", "     ██▓▌               ", " ▬☻☻▓▒█▒▓               ", "    ▓▒█▒▓               ", "    ▓▒█░▒               ", "  </color><color_light_gray>╓</color><color_dark_gray>▄▒▓█░▒               ", "   ▐▒▒</color><color_light_gray>▓</color><color_dark_gray>▒▓               ", "   ▐▒▒█▒▒               ", "   ▐▓▓█░▒             _ ", "   ▐▓▒█░▒        _▄▄███▌", "   ▐░▒▓░▓∟▄▄▄▄▄█████▀▀^ ", "   ▐▒▒░░░▒░░███▀▀~      ", "   ▐▒▓░░▐▒▒◙            ", "   ▐░▒░░╟▓██▀°∙         ", "   ▐░▒░░</color><color_red>!@</color><color_dark_gray>██   ▐        ", "   ▐▒░░░</color><color_red>{</color><color_dark_gray>◙█</color><color_light_gray>▓</color><color_dark_gray>__╓`        ", "  ▄>░░░░▐██▓▓█▓▄▄_      ", "  █▒░░▒░▐█☻▀▀███████    ", "   </color><color_white>░</color><color_dark_gray>▓▓█▒▒▓    ~▀██▀     ", "   ▐██▓▓▐▌              ", "   `▀█▓░░▒              ", "     █▓▒                ", "     ██▓▄   ○           ", "    ▐▓█▓▓███</color><color_light_gray>▓</color><color_dark_gray>▄          ", "     ▀▀▀▀▀▀▀█/          "]}, {"type": "ascii_art", "id": "hk_mp5-pdw", "picture": ["                       ", "     <color_dark_gray>▄                 ", " ___▐█___              ", " ▀▀▓▓██▓▀°             ", "  ▐░███▓▄▄▄▄▄▄         ", "  ▐░██▓▒▀▀☻☻☻/       _ ", "  ▐░██▓▒▌       _▄▄███▌", "  ▐░▒▓░▓∟▄▄▄▄▄█████▀▀^ ", "  ▐▒▒░░░▒░░███▀▀~      ", "  ▐▒▓░░▐▒▒◙            ", "  ▐░▒░░╟▓██▀°∙         ", "  ▐░▒░░▐███   ▐        ", "  ▐▒░░░▐◙█</color><color_light_gray>▓</color><color_dark_gray>__╓`        ", " ▄>░░░░▐</color><color_red>º</color><color_dark_gray>█▓▓█▓▄▄_      ", " █▒░░▒░▐█☻▀▀███████    ", "  </color><color_white>░</color><color_dark_gray>▓▓█▒▒▓    ~▀██▀     ", "  ▐███▓▐▒              ", "   ▀▓▓▒░▒              ", "    ▐███▒              ", "     ▐█ ▀▌             ", "     ▐█  ▐▄            ", "     ▐█▀▀▀▀▌           ", "     ▐█    </color><color_light_gray>▓</color><color_dark_gray>▌          ", "     ▐█     </color><color_light_gray>▓</color><color_dark_gray>▌         ", "     ▐██▀☻███▌         "]}, {"type": "ascii_art", "id": "hk_ump", "picture": ["                     ", "      <color_light_gray>▓              ", " </color><color_dark_gray>▄▄▄▄███             ", " ^▀█▒▌▓░▓▄▄▄</color><color_light_gray>▓▓▓</color><color_dark_gray>▄     ", "   </color><color_white>░</color><color_dark_gray>▓░▒░}~~▀</color><color_light_gray>▀</color><color_dark_gray>▀▀▀     ", "   </color><color_white>░</color><color_dark_gray>▓▌▒▓}         </color><color_light_gray>▄</color><color_dark_gray>▄ ", "   </color><color_white>░</color><color_dark_gray>▓░▓▓`      _▄███▌", "  ╔</color><color_white>░</color><color_dark_gray>▓▓▓▓▌</color><color_light_gray>__▄</color><color_dark_gray>▄██</color><color_light_gray>▓</color><color_dark_gray>██▀~ ", "  ╣▓▓█▓█▓████</color><color_light_gray>▓</color><color_dark_gray>▀▀^    ", "  ╣█▒██▒▒▒▓▌         ", "  ╣█▒██▒▒█▀►         ", "  ╣█▓▒▒▓▓   </color><color_light_gray>]        ", "  </color><color_dark_gray>╣█▒▒▓▓▒°~_</color><color_light_gray>]        ", "  </color><color_dark_gray>╣▓▒▓▓▓◙</color><color_red>░</color><color_dark_gray>▓█▄▄       ", " ▐◙█▓▓▓▒▓▀▀█████♥    ", "  ▀█░▀░▒Γ    ▀▀▀     ", "   ▐█▄██             ", "    ▐███             ", "     ███▌            ", "     █  ▐▌           ", "     █•▄██           ", "     █   ^█          ", "     █    </color><color_light_gray>▐</color><color_dark_gray>▌         ", "     █     █▌        ", "     ███████▌        "]}, {"type": "ascii_art", "id": "shotgun_pistol", "picture": ["                                        ", "        <color_light_gray>~\\</color><color_dark_gray>▄☻████</color><color_light_gray>▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", "      </color><color_light_red>░░</color><color_brown>▓▒</color><color_dark_gray>░░░░░▒▒▒▒▒▒▒▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓</color><color_light_gray>▓", "     </color><color_brown>▄▒░░░▓</color><color_dark_gray>▓▒▒▓░░░░</color><color_brown>▓▓█▓▓▓▓▓▓█           ", "   ▄█▒▒░██▓██</color><color_dark_gray>▀█▓░▒</color><color_brown>▓███████☻▀^           ", "  ▄▒░░▒█▒▓█▓ </color><color_dark_gray>▼(   ▓                     ", " </color><color_brown>▐█░░█▒█▀▀▀</color><color_dark_gray>▬\\</color><color_light_gray>__▄</color><color_dark_gray>▄♥^                     ", " </color><color_brown>▐▓▒███►      </color><color_light_gray>~</color><color_dark_gray>~                        ", "  </color><color_brown>▀▀██                                  "]}, {"type": "ascii_art", "id": "shotgun_revolver", "picture": ["  <color_dark_gray>▄           ", " ▐█           ", " ▐█           ", " ▐█           ", " ▐█           ", " ▐█           ", " ▐</color><color_light_gray>▓           ", " </color><color_dark_gray>▐</color><color_light_gray>▓           ", " </color><color_dark_gray>▐</color><color_light_gray>▒           ", " </color><color_dark_gray>▐</color><color_light_gray>▒           ", " ▐</color><color_dark_gray>█</color><color_brown>█░         ", " </color><color_dark_gray>▐█</color><color_brown>▓▓         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_brown>▓▒         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_brown>█▒         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_brown>█▒         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_dark_gray>█</color><color_brown>▒         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_dark_gray>█</color><color_brown>░         ", " </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_dark_gray>█</color><color_brown>█▌        ", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>▓</color><color_brown>▒▌</color><color_dark_gray>▬       ", " </color><color_light_gray>▐▓</color><color_dark_gray>█▒</color><color_white>░</color><color_dark_gray>▌       ", " </color><color_light_gray>▐</color><color_dark_gray>█</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█▌       ", " </color><color_light_gray>▐█</color><color_dark_gray>███▌       ", "  </color><color_brown>█</color><color_dark_gray>▒</color><color_brown>▒▓▌       ", "  ▐█▒░        ", "   █▒▒        ", "    ▒░░▄      ", "    ░▓░░░     ", "   ▐▓▓▓░      ", "   ▐██▓▒      ", "   ▐██▓▓▌     ", "    ▓██▓▓▌    ", "    ▓███▓▓    ", "    ▐▓███▓▓   ", "    ▐▓███▓▓▌  ", "    ▐█████▓☻  "]}, {"type": "ascii_art", "id": "remington870mcs", "picture": ["             ", "  <color_light_gray>▓▌█▓       ", "  ▓</color><color_dark_gray>▒▒░╠⌠     ", "  </color><color_light_gray>▓</color><color_dark_gray>▒▓▒╠      ", "  </color><color_light_gray>▓</color><color_dark_gray>▒░░╠      ", "  </color><color_light_gray>▓▌█▓       ", "  ▓▌█▓       ", "  ▓▌█▓       ", " </color><color_dark_gray>╣</color><color_light_gray>▒</color><color_dark_gray>▒▒▒       ", " ╣▐▒▓▒       ", " ╣▐▒▓▒       ", " ╣▐▒▓▒       ", " ╣▓▓▓▓       ", " ╣▓▓▓</color><color_light_gray>▓</color><color_dark_gray>▀┐     ", " ╣▓▓▓▓</color><color_light_gray>~▐     ", "  </color><color_dark_gray>▓▓▓▓▓▓     ", "  </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▄▄▄▄ ", "   ▀▀▓▓▓▓▓▓▓█", "        ▀▀▓▓█"]}, {"type": "ascii_art", "id": "remington870xpress", "picture": ["                ", " <color_dark_gray>█▒ ▒▓          ", " ▐▒ ▒▓          ", " ▐▒░▒▓          ", " ▐▒ ▒▓          ", " ▐▒ ▒▓          ", " ▐▒ ▒▒          ", " ▐░░░░▌\\        ", " ▐░░░░▌]        ", " ▐▓░░░▌}        ", " ▐▓░░░▌}        ", " ▐▓░░░▌}        ", " ▐▓░░░▌}        ", " ▐▓▒░░▌}        ", " ▐▒▓▒░▌}        ", " ▐▒▓░▓          ", " ▐▒▓░▓          ", " ▐▒▓░▓          ", " ▐▒░░▒          ", " ▐▒▒▒▒          ", " ▐▐▒▓▒          ", " ▐▐▒▓▒          ", " ▐▐▒▓▒          ", " ▐▓▓▓▓          ", " ▐▓▓▓</color><color_light_gray>▓</color><color_dark_gray>▀┐        ", " ▐▓▓▓▓</color><color_light_gray>~▐        ", "  </color><color_dark_gray>█▓░░▒▒▄       ", "  ▐█░░▒▒▒▒▒▄▄   ", "  ▐░▒░▒▒▒▒▒▒▒   ", "  ▐▓▓▓▌  ▀█▒█   ", "   ▓█▓▌         ", "   ▐██▌         ", "    █▒▓         ", "    █▓█▌        ", "    █▓░▓        ", "    █▓▓█</color><color_light_gray>█       ", "    </color><color_dark_gray>█▓▓▒▓▄</color><color_light_gray>▄</color><color_dark_gray>__   ", "    █▓▓▓░█▓█░░  ", "    ▐▓▓▓▓▓▓▓▓▓</color><color_light_gray>▓ ", "    ╚</color><color_dark_gray>▓▓▓▓▓▓▀▀▀▀ "]}, {"type": "ascii_art", "id": "remington1100", "picture": ["                ", " <color_dark_gray>╔▓▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌            ", " ╠░▌▄▄          ", " ╠░▌░░          ", " ╠░</color><color_brown>▓▒▒▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠</color><color_dark_gray>░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▒▒░▌         ", " </color><color_dark_gray>╠░</color><color_brown>▓▒░▌         ", " </color><color_dark_gray>▐▒▐</color><color_brown>▒▒▌         ", " </color><color_dark_gray>▐▒○▒▒          ", " ▐▒▐▒▒          ", " ▐▒▐▒░          ", " ▐▒▒▒░▀┐        ", " ▐◙▒▒░</color><color_light_gray>~▐        ", " </color><color_dark_gray>▐▒▒▒░▀</color><color_light_gray>▀        ", "  </color><color_dark_gray>░▒</color><color_brown>█░          ", "   ░▒▒░         ", "    ░▒▒░▄       ", "     ░▒▒░▒      ", "     ░░▒░░      ", "    ▐▒▒▓▒░      ", "    ▐▒█▓▒▒      ", "     ▒▓█▒░░     ", "     ▒▓▓▓▒▒▄    ", "     █▒▓▓▓▒▒▌   ", "     ▐▒▓█▓▓▒░▄  ", "     ▐▒▓▓█▓▒▒░</color><color_dark_gray>° ", "      </color><color_brown>█▓▓▓▓▓▒░░ ", "      </color><color_dark_gray>▒▒▒░░░▀▀▀▀"]}, {"type": "ascii_art", "id": "remington870", "picture": ["                ", " <color_light_gray>╔▓▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌            ", " ╠▒▌</color><color_dark_gray>▄▄          ", " </color><color_light_gray>╠▓▌▒▓          ", " ╠▓</color><color_brown>▓▒▒▌         ", " </color><color_light_gray>╠▓</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠▓</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠▓</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠▓</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠▓</color><color_brown>▒▒░▌         ", " </color><color_light_gray>╠▒</color><color_brown>▓▒░▌         ", " </color><color_light_gray>▐▒▓</color><color_brown>▒▒▌         ", " </color><color_light_gray>▐▒▓▐▒          ", " ▐▒░▐▓          ", " ▐▒▒▒▒          ", " ▐</color><color_dark_gray>░▒</color><color_light_gray>█▒          ", " ▐</color><color_dark_gray>░▒</color><color_light_gray>█▒          ", " ▐</color><color_dark_gray>░▒</color><color_light_gray>█▒          ", " ▐███▓          ", " ▐███▓</color><color_dark_gray>▀┐        ", " </color><color_light_gray>▐◙██▓~▐        ", " ▐███▓</color><color_dark_gray>▀</color><color_light_gray>▀        ", "  </color><color_white>░</color><color_light_gray>█</color><color_brown>█░          ", "   ░▒▒░         ", "    ░▒▒░▄       ", "     ░▒▒░▒      ", "     ░░▒░░      ", "    ▐▒▒▓▒░      ", "    ▐▒█▓▒▒      ", "     ▒▓█▒░░     ", "     ▒▓▓▓▒▒▄    ", "     █▒▓▓▓▒▒▌   ", "     ▐▒▓█▓▓▒░▄  ", "     ▐▒▓▓█▓▒▒░</color><color_dark_gray>° ", "      </color><color_brown>█▓▓▓▓▓▒░░ ", "      </color><color_dark_gray>▒▒▒░░░▀▀▀▀"]}, {"type": "ascii_art", "id": "mossberg930sxp", "picture": ["                   ", "   <color_light_gray>▄</color><color_white>▄ </color><color_light_gray>▄            ", " ○¶</color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▐</color><color_white>▒</color><color_light_gray>▌           ", "   </color><color_white>║▒</color><color_light_gray>▓▓</color><color_dark_gray>▓           ", "   </color><color_white>║▒</color><color_dark_gray>███▌          ", "   </color><color_white>║▒</color><color_dark_gray>██▓▌          ", "   </color><color_white>║▒</color><color_dark_gray>██▒▌          ", "   </color><color_white>║▒</color><color_dark_gray>██▒▌          ", "   </color><color_white>║▒</color><color_dark_gray>██▒▌          ", "   </color><color_white>║▒</color><color_dark_gray>██░▌          ", "   </color><color_white>║▒</color><color_dark_gray>██░▌          ", "   </color><color_white>║▒</color><color_dark_gray>██░▌          ", "   </color><color_white>║▒</color><color_dark_gray>██░▌          ", "   </color><color_light_gray>║</color><color_white>▒</color><color_dark_gray>██░▌          ", "   </color><color_light_gray>║</color><color_white>▒</color><color_dark_gray>███▌          ", "  </color><color_light_gray>╣</color><color_white>▒▒▒▒</color><color_dark_gray>█▀          ", "  </color><color_light_gray>╣</color><color_white>▌</color><color_dark_gray>○</color><color_white>▒◙░           ", " </color><color_dark_gray>╓</color><color_light_gray>█</color><color_white>▌</color><color_dark_gray>░</color><color_white>▒▒░           ", " </color><color_dark_gray>█</color><color_light_gray>█</color><color_white>▌</color><color_dark_gray>▒</color><color_white>▒▒░           ", " </color><color_dark_gray>▀</color><color_light_gray>▓</color><color_white>▒▐▒▒░</color><color_light_gray>▄          ", "   </color><color_white>▒▒▒▒░ </color><color_light_gray>\\         ", "   ▐</color><color_white>▒▒▒░</color><color_light_gray>▄~</color><color_dark_gray>]        ", "   </color><color_light_gray>╙</color><color_white>▒</color><color_light_gray>▓▓</color><color_dark_gray>███▄▄       ", "    </color><color_white>`</color><color_dark_gray>██████▓▓▓█▌   ", "      ███^▀▀█▓▒    ", "      </color><color_light_gray>█</color><color_dark_gray>█▓          ", "      ▐█▒          ", "      ▐██▓         ", "      </color><color_light_gray>▐</color><color_dark_gray>▓█▓▓        ", "       ░████▄</color><color_light_gray>_     ", "       </color><color_dark_gray>▓█████▓▄    ", "       </color><color_light_gray>█</color><color_dark_gray>███████▒▓  ", "       ▐▓███████▒</color><color_light_gray>▌ ", "       </color><color_dark_gray>▐▓██▓█████▌ ", "        ▓██───▀▀▀☻ "]}, {"type": "ascii_art", "id": "mossberg590", "picture": ["                ", " <color_dark_gray>○</color><color_light_gray>◙</color><color_dark_gray>▒▌▄∙         ", "  ▐▒█▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒█▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒▌▒▌         ", "  ▐▒▓</color><color_light_gray>▓</color><color_dark_gray>▒╗        ", "  ▐▒▓██╣        ", "  ▐▒▓</color><color_light_gray>▒</color><color_dark_gray>█╬        ", "  ▐▒▓</color><color_light_gray>▒</color><color_dark_gray>█╬        ", "  ▐▒▓██╬        ", "  ▐▒▓██╬        ", "  ▐▒▓██╬        ", "  ▐▒▓██╩        ", "  ▐▒▓▒▌         ", "  ▐▒░▐▌         ", "  ▐▒░▐▌         ", " _▐▒▒▒▌         ", "  ╣░▒█▌         ", "  ╣░▒█▌         ", "  ╣░▒█▌         ", "  ╣███▓         ", " ▐████▓▀`]      ", "  ▐◙█</color><color_light_gray>@</color><color_dark_gray>▓▬^/      ", "  ▐███▓▀`       ", "   ▀██▒         ", "    ▀██▓        ", "     ▐</color><color_light_gray>▓</color><color_dark_gray>▓▓       ", "     ▐</color><color_light_gray>▓</color><color_dark_gray>██▓▓▄    ", "     ▐</color><color_light_gray>▓</color><color_dark_gray>███▓█    ", "     ▐░███▓▓    ", "     ▐░░███▓▌   ", "      █</color><color_light_gray>▓</color><color_dark_gray>███▓▓   ", "      █</color><color_light_gray>▓</color><color_dark_gray>████▓▓  ", "      ▐█</color><color_light_gray>▓</color><color_dark_gray>████▓▓ ", "      ▐█</color><color_light_gray>▓</color><color_dark_gray>████▓▓▌", "       ▀▀▀▀▀▀▀▀~"]}, {"type": "ascii_art", "id": "mossberg500_sec", "picture": ["                ", "  <color_dark_gray>▒▌            ", "  ▒▌            ", "  ▒▌▄▄          ", "  ▒█▒▓          ", " ▐◙▌▒▓          ", " ▐◙▌▒▓          ", " ▐◙▌▒▓_         ", " ▐◙▓</color><color_light_gray>▓</color><color_dark_gray>▒</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓██</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓</color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓</color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓██▌         ", " ▐◙▓██</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓██</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓██</color><color_light_gray>▌         ", " </color><color_dark_gray>▐◙▓▒▒▀         ", " ▐◙░▐▓          ", " ▐◙░▐▓          ", " ▐▒▒▒▒          ", " ▐░▒█▒          ", " ▐░▒█▒          ", " ▐░▒█▒          ", " ▐███▓          ", " ▐███▓▀`]       ", " ▐◙██▓▬^/       ", " ▐███▓▀`        ", "  </color><color_white>░</color><color_dark_gray>██▒          ", "   ▒▓▓▒         ", "    ▒▓▓▒▄       ", "     ▒▓▓▒█▌     ", "     ▒▓█▒▒      ", "    ▐█▓█▒▒      ", "    ▐▓█▓▓▒      ", "     ███▓▒▒     ", "     ████▓▒▄    ", "     █▓██▓▓▒▌   ", "     ▐▓▓██▓▓▒▄  ", "     ▐▓▓▓█▓▓▒▒° ", "      █▓▓█▓▓▓▒▒ ", "      ▓▓▓▒▒▒▒██▌", "      </color><color_light_gray>^^^^</color><color_dark_gray>~</color><color_light_gray>~    "]}, {"type": "ascii_art", "id": "mossberg500", "picture": ["", " <color_dark_gray>╔▓▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌", " ╠▒▌▄▄", " ╠▒█▒▓", " ╠▒▌▒▓", " ╠▒▌▒▓", " ╠▒▌▒▓_", " ╠▓</color><color_brown>██▒▌", " </color><color_dark_gray>╠▓</color><color_brown>▒▓░▌", " </color><color_dark_gray>╠▓</color><color_brown>▒▓░▌", " </color><color_dark_gray>╠▓</color><color_brown>▒▓░▌", " </color><color_dark_gray>╠▓</color><color_brown>▒▓░▌", " </color><color_dark_gray>╠▓</color><color_brown>▒▓░▌", " </color><color_dark_gray>╠▓</color><color_brown>█▓░▌", " </color><color_dark_gray>╠▒▓</color><color_brown>▒▒▌", " </color><color_dark_gray>▐▒▓▒▒</color><color_brown>▀", " </color><color_dark_gray>▐▒░▐▓", " ▐▒░▐▓", " ▐▒▒▒▒", " ▐░▒█▒", " ▐░▒█▒", " ▐░▒█▒", " ▐███▓", " ▐███▓▀`]", " ▐◙██▓▬^/", " ▐███▓▀`", "  </color><color_white>░</color><color_dark_gray>█</color><color_brown>█▒", "   ▒▒▓▒", "    ░▒▓▒▄", "     ░▒▓▒█▌", "     ▒░█▒▒", "    ▐█▒█▒▒", "    ▐▓█▓▓▒", "     ███▓▒▒", "     ███▓▓▒▄", "     █▓▓█▓▓▒▌", "     ▐▓▓█▓▓▓▒▄", "     ▐▓▓▓█▓▓▒▒</color><color_dark_gray>°", "      </color><color_brown>█▓▓█▓▓▓▒▒", "      </color><color_dark_gray>▓▓▓▒▒▒▒██▌"]}, {"type": "ascii_art", "id": "beretta_m9", "picture": ["", "  <color_light_gray>•</color><color_dark_gray>▄__▄▄▲</color><color_light_gray>▄▄▄▄▄▄▄</color><color_dark_gray>______________</color><color_light_gray>■</color><color_dark_gray>▄</color><color_light_gray>■", "    </color><color_dark_gray>███░██████████████████████</color><color_light_gray>▓</color><color_dark_gray>██]", "   </color><color_light_gray>▄</color><color_dark_gray>███</color><color_red>•</color><color_light_gray>▓▓▓▓▓</color><color_dark_gray>▓▓▒▓▓▒▓▒▓▒▓▓█▓█▓▓███", "  ▀▀</color><color_light_gray>▓▓</color><color_dark_gray>████████░▒███▓▓█▀▀▀▀▀▀▀▀▀▀^", "    </color><color_light_gray>▐█</color><color_dark_gray>█▒▒Θ█</color><color_light_gray>▓</color><color_dark_gray>▌  █   ▀▌", "    </color><color_light_gray>▓</color><color_dark_gray>█▒▒▒▒██</color><color_light_gray>▓</color><color_dark_gray>_  ▀°_/►", "   </color><color_light_gray>▐</color><color_dark_gray>█▒▒▒▒██</color><color_light_gray>▓  </color><color_dark_gray>`~~~", "  </color><color_light_gray>▐</color><color_dark_gray>███▒▒██</color><color_light_gray>█", "  █</color><color_dark_gray>███████▌", " </color><color_light_gray>▐</color><color_dark_gray>████Θ██</color><color_light_gray>▓", "  </color><color_dark_gray>▀▀▀</color><color_light_gray>▓</color><color_dark_gray>███</color><color_light_gray>█"]}, {"type": "ascii_art", "id": "ksg-25", "picture": ["     <color_dark_gray>▄▄", " ∙▄▄█</color><color_light_gray>█▓</color><color_dark_gray>▒█", " </color><color_light_gray>▀</color><color_dark_gray>▀~▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>█▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>█▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>█▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>█▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>█▓▐</color><color_dark_gray>█", "    ▐</color><color_light_gray>▌▓▐</color><color_dark_gray>█", "   _▐</color><color_light_gray>░░░</color><color_dark_gray>█", "   </color><color_light_gray>╬</color><color_dark_gray>▓▒▓</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓", "   ╣</color><color_white>░</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", "   </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", "   </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", "   </color><color_light_gray>╬</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", "   </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", "   </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", "   </color><color_light_gray>╬</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", "   </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>██╠", "   </color><color_light_gray>╣▐</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>██▌", "   </color><color_light_gray>╣▐</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>██▌", "   </color><color_light_gray>╣▐</color><color_dark_gray>█▓█▓█▌", "   </color><color_light_gray>╣</color><color_dark_gray>▐█▓▓▓█▌", "   </color><color_light_gray>╣</color><color_dark_gray>▐█▓▐▓◙▌", "   </color><color_light_gray>╬</color><color_dark_gray>▒▓▓█▓◙░▒▓▬_", " </color><color_light_gray>▄</color><color_dark_gray>◙▀</color><color_light_gray>▓</color><color_dark_gray>█▓</color><color_light_gray>█</color><color_dark_gray>█▐█▒   ▌", "    </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐█▒─▄⌐▌", "    </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐▓▓▓◙▓♦", "    </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐▓▒█</color><color_white>▒</color><color_dark_gray>▒▓▒██▄▄▄", "    </color><color_light_gray>█</color><color_dark_gray>█▓</color><color_light_gray>█</color><color_dark_gray>█▐▓▓█▀█▓█▓▓▒▒▒", "    </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>█▒▓▓    ▀▀██▓▓", "    </color><color_light_gray>█</color><color_dark_gray>█▒</color><color_light_gray>▒</color><color_dark_gray>█▒▓█        ~▀", "    </color><color_light_gray>█</color><color_dark_gray>████░▒█", "    </color><color_light_gray>█</color><color_dark_gray>████░██○", "    </color><color_light_gray>█▓</color><color_dark_gray>███░█`", "    </color><color_light_gray>█▓</color><color_dark_gray>███▒▌", "    </color><color_light_gray>█▓</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█▒</color><color_light_gray>▓", "    █▓▒▓</color><color_dark_gray>█▒◙█▄", "    </color><color_light_gray>█▒</color><color_dark_gray>███▓█\\▀█▄", "    </color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▒▒</color><color_dark_gray>█▓█▄\\</color>█<color_dark_gray>█▌", "    ████████████", "    </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▀^^^^▀▀°"]}, {"type": "ascii_art", "id": "ksg", "picture": ["", " <color_dark_gray>_ ▄▄</color><color_light_gray>■■", " ╬</color><color_dark_gray>▓▒▓</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓", " ╣</color><color_white>░</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", " </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", " </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", " </color><color_light_gray>╬</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", " </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", " </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>█▒╠", " </color><color_light_gray>╬</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>█░╠", " </color><color_light_gray>╣</color><color_dark_gray>▐█</color><color_light_gray>▓▓</color><color_dark_gray>██╠", " </color><color_light_gray>╣▐</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>██▌", " </color><color_light_gray>╣▐</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>██▌", " </color><color_light_gray>╣▐</color><color_dark_gray>█▓█▓█▌", " </color><color_light_gray>╣</color><color_dark_gray>▐█▓▓▓█▌", " </color><color_light_gray>╣</color><color_dark_gray>▐█▓▐▓◙▌", " </color><color_light_gray>╬</color><color_dark_gray>▒▓▓█▓◙░▒▓▬_", " ▀</color><color_light_gray>▓</color><color_dark_gray>█▓</color><color_light_gray>█</color><color_dark_gray>█▐█▒   ▌", "  </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐█▒─▄⌐▌", "  </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐▓▓▓◙▓♦", "  </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█▐▓▒█</color><color_white>▒</color><color_dark_gray>▒▓▒██▄▄▄", "  </color><color_light_gray>█</color><color_dark_gray>█▓</color><color_light_gray>█</color><color_dark_gray>█▐▓▓█▀█▓█▓▓▒▒▒", "  </color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>█▒▓▓    ▀▀██▓▓", "  </color><color_light_gray>█</color><color_dark_gray>█▒</color><color_light_gray>▒</color><color_dark_gray>█▒▓█        ~▀", "  </color><color_light_gray>█</color><color_dark_gray>████░▒█", "  </color><color_light_gray>█</color><color_dark_gray>████░██○", "  </color><color_light_gray>█▓</color><color_dark_gray>███░█`", "  </color><color_light_gray>█▓</color><color_dark_gray>███▒▌", "  </color><color_light_gray>█▓</color><color_dark_gray>███▒▌", "  </color><color_light_gray>█▓</color><color_dark_gray>███▒▌", "  </color><color_light_gray>█▓</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█▒</color><color_light_gray>▓", "  █▓▒▓</color><color_dark_gray>█▒◙█▄", "  </color><color_light_gray>█▒</color><color_dark_gray>███▓█\\▀█▄", "  </color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▒▒</color><color_dark_gray>█▓█▄\\</color>█<color_dark_gray>█▌", "  ████████████", "  </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▀^^^^▀▀°"]}, {"type": "ascii_art", "id": "db_pipe_gun", "picture": ["", "  <color_light_gray>◙</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>▒</color><color_dark_gray>▄ </color><color_light_gray>__∙▄▄", " </color><color_dark_gray>▐▒▓▓█♥☻▀▀▀▀▀", " ▐▓</color><color_white>░</color><color_dark_gray>█", " ▐▓</color><color_white>░</color><color_dark_gray>█", " ▐◙</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_dark_gray>█", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_light_gray>▓", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_white>░</color><color_light_gray>▓</color><color_dark_gray>▒▀▀≡▓▓", " ▐</color><color_light_gray>▓</color><color_white>░</color><color_light_gray>▓", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_brown>▓▓▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_brown>▓▓▒", " </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_brown>▓▓▓▄", " </color><color_dark_gray>▐█</color><color_brown>▓▓▓▒▌", "  </color><color_dark_gray>█</color><color_brown>▓</color><color_light_red>░</color><color_brown>██░▄", "   █</color><color_light_red>░</color><color_brown>██▓▒░▄", "    ████▒▒░"]}, {"type": "ascii_art", "id": "2barrel_shotgun", "picture": ["                    ", " </color><color_light_gray> ▓                 ", " ▐</color><color_dark_gray>█                 ", " </color><color_light_gray>▐</color><color_dark_gray>▓                 ", " </color><color_light_gray>▐</color><color_dark_gray>▓                 ", " </color><color_light_gray>▐</color><color_dark_gray>▓                 ", " </color><color_light_gray>▐</color><color_dark_gray>█                 ", " </color><color_light_gray>▐</color><color_dark_gray>█                 ", " ▐█                 ", " ▐▒                 ", " ▐█▄_               ", " ▐</color><color_brown>█▓▒               ", " </color><color_dark_gray>▐</color><color_brown>█▓▓               ", " </color><color_dark_gray>▐</color><color_brown>█▓▒               ", " </color><color_dark_gray>▐</color><color_light_red>░</color><color_brown>▒▓               ", " </color><color_dark_gray>▐</color><color_light_red>░</color><color_brown>▓▓▌              ", " </color><color_dark_gray>▐</color><color_light_red>▒</color><color_brown>▓▓▌              ", " </color><color_dark_gray>▐</color><color_brown>▓▓▓█              ", " </color><color_dark_gray>▐</color><color_brown>▓▓▓▓              ", " </color><color_dark_gray>▐</color><color_brown>▓░▓▓              ", " </color><color_light_gray>▐</color><color_dark_gray>▒░▒█              ", " </color><color_light_gray>▐</color><color_dark_gray>▒░▒▓              ", " </color><color_light_gray>▐</color><color_dark_gray>▒▒▓▓              ", " </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓</color><color_brown>▌</color><color_light_gray>~°\\          ", "  ▐</color><color_brown>▓▓▒░</color><color_light_gray>▄≈▐          ", "  d</color><color_brown>█▓▓░</color><color_white>▒</color><color_light_gray>▄w          ", "   </color><color_brown>▐██▒░</color><color_light_gray>^           ", "   </color><color_brown>╚</color><color_light_red>░</color><color_brown>█▒░░           ", "    ▀█▒▒░░          ", "     </color><color_light_red>▒░</color><color_brown>▒▒░░▒▒       ", "    ╓</color><color_light_red>░</color><color_brown>▓█▒▒▒▒        ", "    ▐▓▓█▒▓▒░        ", "    ▐▓▓▓██▓▒░       ", "     ▒▓▓██▓▓▒▒      ", "     ▒▓▓██▓▓▓▒█     ", "     ▐▓▓███▓▓▓▒▌    ", "     ▐▓▓▓██▓▓▓▓▒▌   ", "      ▒▓▓▓▒▓▓▓▓▓▒▌  ", "      ▐▓▓▓▓▒▒▓▓▓▓▒▌ ", "      ▐▓▓▓▒▒▒▒▒▓▓▒▒ ", "       █▒▒▒▒█▒▓▒░░▒▓", "       </color><color_dark_gray>▀▀▀▀▀°^</color><color_light_gray>^~</color><color_dark_gray>~~~`"]}, {"type": "ascii_art", "id": "blunderbuss", "picture": ["", "    <color_light_gray>▄▄▄", "   ▐▓</color><color_white>▒</color><color_light_gray>▓</color><color_dark_gray>▌", "    </color><color_light_gray>▓▒█ ▌", "    ▐▓</color><color_dark_gray>▒ </color><color_light_gray>▌", "    ▐▓</color><color_brown>▓▒</color><color_light_gray>▌", "    ▐▓</color><color_light_red>░</color><color_brown>░</color><color_light_gray>▌", "    ▐▓</color><color_brown>▒</color><color_light_red>░</color><color_dark_gray>▌", "    </color><color_light_gray>▐▓</color><color_brown>▒</color><color_light_red>▒</color><color_dark_gray>▌", "    </color><color_light_gray>▐▓</color><color_brown>█▒</color><color_dark_gray>▌", "    </color><color_light_gray>▐▓</color><color_brown>██</color><color_dark_gray>█", "    </color><color_light_gray>▐▓</color><color_brown>██▓", "    </color><color_light_gray>▐▓</color><color_brown>▒█▌", "    </color><color_light_gray>▐▓</color><color_brown>▓▒▌", "    </color><color_light_gray>▐▓</color><color_brown>▒▒▓", "   </color><color_light_gray>_▐▓</color><color_dark_gray>▒</color><color_light_gray>▒</color><color_brown>▓", "  </color><color_light_gray>` </color><color_dark_gray>▐</color><color_light_gray>█</color><color_white>░</color><color_light_gray>▒</color><color_brown>▓</color><color_light_gray>°▬", "   ▄▐▒▒</color><color_dark_gray>░</color><color_brown>▓</color><color_light_gray>─·│", " ∙▀</color><color_dark_gray>▀ </color><color_light_gray>▐</color><color_white>░</color><color_light_gray>▓</color><color_brown>▓</color><color_light_gray>▌</color><color_dark_gray>▬", "      </color><color_brown>▐▒▓▒", "       ▓▒▒▒", "       ▓▓▒█▓", "      ▐▓▓██▒▌", "       ▒▓▒▒▒▒", "       ▒▓▓▒██▒", "       ▒▒▓█▒██▓", "       ▐▒▒▒▓██▓▌", "       ▐▒▒▒▓</color><color_light_red>░</color><color_brown>█</color><color_light_red>░</color><color_brown>▓▌", "       ▐▓▒▒▓▓▓</color><color_light_red>░</color><color_brown>▓▓</color><color_light_gray>⌐", "        </color><color_dark_gray>▀▀</color><color_light_gray>▀▀</color><color_dark_gray>~</color><color_light_gray>~```"]}, {"type": "ascii_art", "id": "sks", "picture": ["", " <color_dark_gray>╓▄▄█▄", " </color><color_light_gray>°</color><color_dark_gray>▀^▓</color><color_white>░</color><color_dark_gray>▄", "    ▓▒▓</color><color_light_gray>▌", "    </color><color_dark_gray>█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "    </color><color_dark_gray>█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "    </color><color_dark_gray>█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "    </color><color_dark_gray>█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "  ⌡</color><color_dark_gray>██▓▐</color><color_light_gray>▌", "  </color><color_dark_gray>▐▌█▓▐</color><color_light_gray>▌", "  </color><color_dark_gray>▐▌█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "  </color><color_dark_gray>▐▌█</color><color_white>░</color><color_dark_gray>▐</color><color_light_gray>▌", "  </color><color_dark_gray>▐███</color><color_light_gray>█▌", "  </color><color_brown>▐██▓▒</color><color_light_gray>▌", "  </color><color_brown>█▐█▓▒", "  █▓█▓▒", "  █▐█▓▒", "  █▓█▓▒", "  █▐█▓▒", "  █▓█▒▓▌", " </color><color_light_gray>▐</color><color_dark_gray>█▓</color><color_brown>█░▓▌", "  </color><color_dark_gray>█▒</color><color_brown>▓▒▓▌", " </color><color_light_gray>▐</color><color_dark_gray>█▒</color><color_brown>▓</color><color_dark_gray>☻</color><color_brown>▓▒", "  </color><color_light_gray>▓</color><color_dark_gray>▓</color><color_brown>█▓▓░</color><color_dark_gray>█", "  </color><color_light_gray>█▓</color><color_dark_gray>█</color><color_brown>▓▓░</color><color_dark_gray>██", "  </color><color_light_gray>█▓▓</color><color_brown>█▓░</color><color_dark_gray>███", "  </color><color_light_gray>█▓</color><color_dark_gray>█</color><color_brown>█▓▒░</color><color_dark_gray>█▀", "  </color><color_light_gray>█</color><color_dark_gray>█</color><color_brown>██▓▓▒", "  </color><color_light_gray>█</color><color_dark_gray>█</color><color_brown>██▓▓▒", "  </color><color_dark_gray>██</color><color_brown>██▓▓▒</color><color_dark_gray>__", "  ██</color><color_brown>██▓▒░  </color><color_dark_gray>ì", "  ██</color><color_brown>██▓▒░</color><color_dark_gray>► ]", "  ██</color><color_brown>██▓▒░</color><color_dark_gray>▀°", "  ▀▀▀</color><color_brown>█▓▒░", "      ▓▓▒░", "      ▐▓▓▒░▄", "      ▐█▓▓▓▒▌", "      ▒██▓▓▒</color><color_dark_gray>▄", "      </color><color_brown>▓▓▓█▓▓▒", "      ▓</color><color_light_red>░</color><color_brown>▓█▓▓▒▌", "      █</color><color_light_red>░</color><color_brown>███▓▓▒", "      █▓</color><color_light_red>░</color><color_brown>█▓▓▓▒▌", "      ▐</color><color_light_red>░</color><color_brown>▓▓▓▓▓▓▒▌", "      ▐█</color><color_light_red>░</color><color_brown>▓▓▓▓▓▓▌", "      ▐█▓█☻▀▀▀▀</color><color_dark_gray>°"]}, {"type": "ascii_art", "id": "mini-14", "picture": ["", " <color_dark_gray>▄▄", " ▀▒▌", "  ▒▌", "  ▒▌", "  ▒▌", "  ▒▌", "  ▒▌", "  ▒▌", "  █</color><color_light_gray>█", " </color><color_dark_gray>▐███▒", " ▐█</color><color_brown>▓▓▒", " </color><color_dark_gray>▐█</color><color_brown>▓▓▒", " </color><color_dark_gray>▐█</color><color_brown>▓▓▒", " </color><color_dark_gray>▐█</color><color_light_gray>▓</color><color_brown>▓▒", " </color><color_dark_gray>▐░▐</color><color_brown>▓▓", " </color><color_dark_gray>▐░▐</color><color_brown>▓▓", " </color><color_dark_gray>▐░▐</color><color_brown>▓▓", " </color><color_dark_gray>▐░▐</color><color_brown>▓▓", " </color><color_dark_gray>▐▌▐</color><color_brown>▓▒     </color><color_dark_gray>__", " ▐░▐</color><color_brown>▓▒▌</color><color_dark_gray>▄▄▄d/☻│", "  █░</color><color_brown>▓▓▒</color><color_dark_gray>▬▬*°_/│", "  █°</color><color_brown>▓▓▒</color><color_dark_gray>▬▬▬*°☼", " ┌▀│</color><color_brown>▓▓▒▌</color><color_dark_gray>~~~", " ▐▄░</color><color_brown>▓▓▒▌", " </color><color_dark_gray>▀</color><color_white>░</color><color_dark_gray>░</color><color_brown>▓▓▒</color><color_dark_gray>◙►\\", "  ▐█</color><color_brown>▓▓▓▌</color><color_dark_gray>↔│", "    </color><color_brown>██▓▌</color><color_dark_gray>~", "     </color><color_light_red>▒</color><color_brown>▓▒", "     </color><color_light_red>░</color><color_brown>▓▓▓▄", "     </color><color_light_red>░</color><color_brown>▒▒▒▓█", "    ▐▓▓█▒▒", "     █▓▓▓▒", "     </color><color_light_red>░</color><color_brown>▓█▓▓▒", "     █▓█▓▓▒▌", "     </color><color_light_red>░</color><color_brown>▓██▓▒▒", "     </color><color_light_red>░</color><color_brown>▓██▓▓▒▒", "     █▓▓█▓▓▒▒▄</color><color_dark_gray>▬", "     </color><color_brown>▐▓███▓▓▒▒", "     ▐▓███▓▓▓▒</color><color_dark_gray>█", "     </color><color_light_gray>▐█▓▓</color><color_dark_gray>██████♦"]}, {"type": "ascii_art", "id": "ruger1022", "picture": ["", " <color_dark_gray>¡</color><color_light_gray>█</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▌", "  </color><color_light_gray>║</color><color_dark_gray>▓", "  </color><color_light_gray>║</color><color_white>░", "  </color><color_light_gray>║</color><color_brown>▓█\\", "  </color><color_light_gray>█</color><color_dark_gray>█▒░▬", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▒", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▒▌", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓▌", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓▌", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓▌", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓▌", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓░", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓░", "  </color><color_light_gray>║</color><color_dark_gray>█</color><color_brown>█▓░", "  </color><color_dark_gray>█▒</color><color_brown>█▓░", "  </color><color_dark_gray>█░</color><color_brown>█▓▒", "  </color><color_dark_gray>█░</color><color_brown>█▓▒</color><color_dark_gray>►", "  █░</color><color_brown>█▓▒</color><color_dark_gray>▒", "  █▒</color><color_brown>█▓</color><color_dark_gray>▌_</color><color_light_gray>▌", "  </color><color_dark_gray>☻▒</color><color_brown>█▓</color><color_dark_gray>▌▄♪", "   </color><color_brown>▒▒▒▒", "    ▒▒░▌", "     ▒▓░░▄_", "     ██▓▒▒░♥", "     █▓▓▒▒░", "     █▓█▒▒░", "     ███▓▒░▌", "     ▐</color><color_light_red>░</color><color_brown>██▓▒░", "     ▐</color><color_light_red>▒</color><color_brown>█▓█▓▒░", "      █</color><color_light_red>░</color><color_brown>█▓▓▒▒▌", "      █</color><color_light_red>▒</color><color_brown>█▓▓▓▒▒", "      █</color><color_light_red>▒░</color><color_brown>█▓█▓▒▒", "      ▐█</color><color_light_red>▒</color><color_brown>██▓█▓▓▌", "       </color><color_dark_gray>☻~~~~ ~~"]}, {"type": "ascii_art", "id": "rem700", "picture": ["", "      <color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌", "      </color><color_white>║</color><color_light_gray>▌</color><color_dark_gray>▄", "      </color><color_white>║</color><color_brown>▓█</color><color_dark_gray>°", "      </color><color_white>║</color><color_brown>▓▓▌", "      </color><color_white>║</color><color_light_red>░</color><color_brown>▓▌", "      </color><color_white>║</color><color_light_red>▒</color><color_brown>▓▌", "      </color><color_white>║</color><color_light_red>░</color><color_brown>▓▌", "      </color><color_white>║</color><color_light_red>░</color><color_brown>▓▓", "      </color><color_white>║</color><color_brown>▓▓▒", "  </color><color_light_gray>▒</color><color_dark_gray>▒░▐</color><color_white>║</color><color_brown>▓▓▒", "  </color><color_light_gray>▒</color><color_dark_gray>▒▌▌</color><color_white>║</color><color_brown>▓▓▒", "  </color><color_light_gray>▐</color><color_dark_gray>▒▐ </color><color_white>║</color><color_brown>█▓▒Γ", "  </color><color_light_gray>▐</color><color_dark_gray>▒▐▄</color><color_white>║</color><color_brown>█▓▒▌", "   </color><color_dark_gray>▒▐ </color><color_white>║</color><color_brown>█▓▒▌", " </color><color_dark_gray>▐░▓│ </color><color_white>║</color><color_light_red>░</color><color_brown>▓▒▌", "  </color><color_dark_gray>~▒▐ </color><color_white>▒</color><color_brown>▓▓▒▒", "  </color><color_light_gray>▐</color><color_dark_gray>▒▐▄</color><color_white>▒</color><color_brown>▓▓▓▒", "  </color><color_light_gray>▐</color><color_dark_gray>▒│ </color><color_white>▒▬■∙</color><color_brown>▒</color><color_light_gray>▬</color><color_white>▲", "  </color><color_light_gray>▒</color><color_dark_gray>▒░\\</color><color_white>▀</color><color_brown>▓██▒</color><color_white>▌</color><color_light_gray>∟]", "  ▒</color><color_dark_gray>▒░▌  </color><color_brown>▓▓▓</color><color_light_gray>►↔`", "  </color><color_dark_gray>▀▀▀`   </color><color_brown>▓▓▌", "        _█▓▒▄_", "        ▓██▓▓▓▓▒", "        ▓▓██▓▓▒", "        ▒▓███▒", "        ▒▓</color><color_light_red>░</color><color_brown>██▓▒", "        ▐▓</color><color_light_red>░</color><color_brown>███▒▌", "        ▐▓</color><color_light_red>░░</color><color_brown>█▓▓▒", "        ▐▓</color><color_light_red>▒░</color><color_brown>█▓▓▒▌", "       </color><color_dark_gray>{░</color><color_brown>▓</color><color_light_red>░░</color><color_brown>█▓▓█▒▄", "       </color><color_dark_gray>{░</color><color_brown>▓▓██▓▓▓█▒", "       </color><color_dark_gray>{░▓░░░░░___▌", "        ▀▀~~^^^^~~~"]}, {"type": "ascii_art", "id": "rem700_3006", "picture": ["", "      <color_dark_gray>█▌", "      █▌", "      █▌", "      █▌", "      █▌", "      █▌", "      █▌", "      </color><color_white>▒</color><color_dark_gray>▌", "      </color><color_light_gray>▓</color><color_dark_gray>▌", "      </color><color_light_gray>▓</color><color_dark_gray>▌", "      </color><color_light_gray>▓</color><color_dark_gray>▌", "      </color><color_light_gray>▓</color><color_dark_gray>▌", "      </color><color_light_gray>▓</color><color_dark_gray>▌▄", "      ██▓°", "      </color><color_light_gray>▓</color><color_dark_gray>█▓▌", "      ██▓▌", "      </color><color_light_gray>▓</color><color_dark_gray>█▓▌", "      ██▓▌", "      </color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>▓", "      </color><color_dark_gray>███▓", "  </color><color_light_gray>▓</color><color_dark_gray>█▓▐</color><color_light_gray>▓</color><color_dark_gray>██▓", "  </color><color_light_gray>▓</color><color_dark_gray>▓▌▌███▓", "  </color><color_light_gray>▐</color><color_dark_gray>█▐ </color><color_light_gray>▓</color><color_dark_gray>██▓Γ", "  </color><color_light_gray>▐</color><color_dark_gray>█▐▄</color><color_light_gray>▓</color><color_dark_gray>██▓▌", "   █▐ █▓█▓▌", " ▐░█│ </color><color_white>▒</color><color_dark_gray>██▓▌", "  ~█▐ </color><color_white>▒</color><color_dark_gray>▓█▓</color><color_light_gray>▓", "  ▐</color><color_dark_gray>█▐▄</color><color_light_gray>▓</color><color_dark_gray>▓██▓", "  </color><color_light_gray>▐</color><color_dark_gray>█│ </color><color_light_gray>▓</color><color_dark_gray>▬■∙▓▬</color><color_light_gray>▲", "  ▓</color><color_dark_gray>▓▌\\</color><color_light_gray>▀▓</color><color_dark_gray>█▓▓</color><color_light_gray>▌∟</color><color_dark_gray>]", "  </color><color_light_gray>▓</color><color_dark_gray>▓▌▌  </color><color_light_gray>▓</color><color_dark_gray>▓▓►↔`", "  ▀▀▀`   ▓▒▌", "        _▓▓▓▄_", "        </color><color_light_gray>▓</color><color_dark_gray>▓██▓▓▒▌", "        </color><color_light_gray>▓</color><color_dark_gray>▓███▒░", "        </color><color_white>▒</color><color_dark_gray>█▓█▓▒", "        </color><color_white>▒</color><color_dark_gray>▓▓██▒</color><color_light_gray>▓", "        </color><color_dark_gray>▐▓███▓▒▌", "        ▐▓▓██▓▒▒", "        ▐▓▓███▓▒▌", "        ▐▓▓███▓▒▒▄", "        ▐▓▓▓███▓▒▒", "        ▐█░░░░░___▌", "        ▀▀~~^^^^~~~"]}, {"type": "ascii_art", "id": "sm_pipe_gun", "picture": ["", "   <color_light_gray>▓", "   ▓", "   ▓", "   ▓", "  ▐</color><color_dark_gray>░▌", "  ▐░▌", "  ▐░▌", "  ▐░▌", "  ▐░▌", "  ▐░▌", "  ▐░▌", "  ▐░▌", "  ▐░▄ </color><color_light_gray>__∙▄▄", "  </color><color_dark_gray>▐░░█♥☻▀▀▀▀▀", "  ▐░█", "  ▐░▌", " </color><color_light_gray>▐</color><color_dark_gray>▓░▌", "  ◙░▌", "  ▐░▌", "  ▐░▌", "  ▐░▒▒▀▀≡▓▓", "  ▐░█", "  ▐░█", "  ▐░█", "  ▐░█", "  ▐░█", "  █░█", "   █▒▄", "    </color><color_light_gray>▒▒</color><color_dark_gray>▄▄▄▄"]}, {"type": "ascii_art", "id": "ps_pipe_gun", "picture": ["", "  <color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▐</color><color_light_gray>▓ ", "  </color><color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▐</color><color_light_gray>▓", "  </color><color_dark_gray>▒░▌", "  ▓░▌ ", "  ▓░▌", "  ◙░▌", "  █░▌", "  █░▄ </color><color_light_gray>__∙▄▄", "  </color><color_dark_gray>█░░█♥☻▀▀▀▀▀", "  █░▌", "  █░▌", "  █░▌", "  █░▌", "  █░▌", "  █░▌", "  █░▌", "  █░▒▒▀▀≡▓▓", "  █░█", "  █░█", "  █░█", "  █░█▒▄", "  █░█  ▌", "  █░█   \\", "   █▒▄   \\", "    </color><color_light_gray>▒▒</color><color_white>▄▄</color><color_dark_gray>▄▄▌"]}, {"type": "ascii_art", "id": "lg_pipe_gun", "picture": ["", "  <color_dark_gray>◙</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓", "  </color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>▄ </color><color_light_gray>__∙▄▄", "  </color><color_dark_gray>▒░░█♥☻▀▀▀▀▀", "  ▓░█", "  ▓░█", "  ◙░█", "  █░█", "  █░█", "  █░█", "  █░█", "  █░█", "  █░█", "  █░▒▒▀▀≡▓▓", "  █░█", "  █</color><color_brown>▓▓▒", "  </color><color_dark_gray>█</color><color_brown>▓▓▒", "  </color><color_dark_gray>█</color><color_brown>▓▓▓▄", "  </color><color_dark_gray>█</color><color_brown>▓▓▓▒▌", "  </color><color_dark_gray>█</color><color_brown>▓</color><color_light_red>░</color><color_brown>██░▄", "   █</color><color_light_red>░</color><color_brown>██▓▒░▄", "    ████▒▒░"]}, {"type": "ascii_art", "id": "marlin_39a", "picture": ["", "  <color_dark_gray>▄", " </color><color_white>▒</color><color_light_gray>▒</color><color_dark_gray>▒", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐</color><color_light_gray>█</color><color_dark_gray>▐", " ▐▌</color><color_light_gray>◙</color><color_dark_gray>▄∙", " ▐</color><color_brown>█</color><color_light_red>░</color><color_brown>▒", " </color><color_dark_gray>▐</color><color_brown>█</color><color_light_red>░</color><color_brown>▒", " </color><color_dark_gray>▐</color><color_brown>█▓▒▌", " </color><color_dark_gray>▐</color><color_brown>░▓░▌", " </color><color_dark_gray>▐</color><color_brown>░░░▌", " </color><color_dark_gray>▐</color><color_brown>░░░▌", " </color><color_dark_gray>▐</color><color_brown>░░░▌", "</color><color_dark_gray>_▐</color><color_brown>░▓░▌", " </color><color_dark_gray>▐</color><color_brown>█▓▒▌", " </color><color_dark_gray>▐</color><color_brown>█</color><color_light_red>░</color><color_brown>▒▌", " </color><color_dark_gray>▐</color><color_light_gray>███</color><color_dark_gray>▌", " ▐</color><color_light_gray>███▓</color><color_dark_gray>▄.", " ▐</color><color_light_gray>██</color><color_dark_gray>█▒  \\", "  </color><color_brown>█▒▒▒▒</color><color_dark_gray>≈∙┐", "  ⌠</color><color_light_gray>^</color><color_brown>█░░▒</color><color_dark_gray>/ °,", "    </color><color_brown>▐▒░░▒▄ </color><color_dark_gray>►", "     </color><color_brown>▐▓░░▒▒▄", "     ▐▓▓░░▒</color><color_dark_gray>▌", "    </color><color_brown>▐▒█▓▓▒▌", "    ▐▒██▓▒▒", "     ▓▓█▓▓▒▌", "     ▓█</color><color_light_red>░</color><color_brown>█▓▒▒▌", "     ▐▓█</color><color_light_red>░</color><color_brown>▓▒▓▒", "     ▐▓█</color><color_light_red>░</color><color_brown>▓▓█▓▒", "     ▐▓█</color><color_light_red>▒</color><color_brown>█▓▓▒▒▌", "     ▐██</color><color_light_red>▒░</color><color_brown>█▓▒█▒</color><color_dark_gray>▌", "      ██☻☻☻☻☻☻☻☻"]}, {"type": "ascii_art", "id": "m249_saw", "picture": ["", "        <color_light_gray>▐▓</color><color_dark_gray>▌", "        </color><color_light_gray>▐</color><color_dark_gray>█</color><color_light_gray>▌", "         ▒", "         ▓", "         ▓", "         ▓", "         ▓", "         ▒  ,", "     </color><color_dark_gray>▄</color><color_light_gray>▄</color><color_dark_gray>▄_</color><color_light_gray>▓▄ █", "     ~▀</color><color_dark_gray>▀</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>█</color><color_white>▒</color><color_light_gray>b               ___▄j</color><color_dark_gray>╕", "        </color><color_light_gray>█</color><color_dark_gray>░</color><color_light_gray>▒▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓█▄▄▄▄▄██</color><color_white>▒</color><color_light_gray>▬▬▀▀█▀°°</color><color_dark_gray>`", "       </color><color_light_gray>▐█▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>Γ</color><color_dark_gray>~~ </color><color_light_gray>~</color><color_dark_gray>~~</color><color_light_gray>°▀▀</color><color_dark_gray>▀</color><color_light_gray>▀</color><color_dark_gray>▀▀▀▀", "       </color><color_white>▒</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>▓▓</color><color_dark_gray>_", "       </color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>█</color><color_white>▒</color><color_light_gray>▌", "       ▓</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>███</color><color_light_gray>▓</color><color_dark_gray>▄</color><color_light_gray>▄", "   ,,  </color><color_white>▒</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>██░</color><color_light_gray>▐▓▌", "  _▓▀∞▄█</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>░</color><color_light_gray>▐▓▌", "  ▓</color><color_dark_gray>▌</color><color_light_gray>` ▐▓</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>▓▓▐▓</color><color_dark_gray>▌", " </color><color_light_gray>▐</color><color_dark_gray>▓   </color><color_light_gray>▐▓</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>░</color><color_light_gray>▐▓</color><color_dark_gray>▌", " </color><color_light_gray>▓</color><color_dark_gray>▌   </color><color_light_gray>▐</color><color_dark_gray>▌█</color><color_light_gray>▓</color><color_dark_gray>██░</color><color_light_gray>▐▓◙", "      ▓</color><color_white>▒</color><color_light_gray>▓█</color><color_dark_gray>▓▓◙▀▀</color><color_light_gray>▀", "      ▓</color><color_dark_gray>▓</color><color_light_gray>▓▓</color><color_dark_gray>░</color><color_light_gray>▌</color><color_dark_gray>█", "     ▐</color><color_light_gray>▓</color><color_dark_gray>▓</color><color_light_gray>▓▓</color><color_dark_gray>░</color><color_light_gray>▌▓", "     </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>▌</color><color_light_gray>◙▓</color><color_dark_gray>▓</color><color_light_gray>▌▓", "     </color><color_dark_gray>▐██</color><color_light_gray>◙▓</color><color_dark_gray>▓█</color><color_light_gray>▓▄▄°\\", "     ▓▓▓▓▓</color><color_dark_gray>▓█</color><color_light_gray>▓▓▓  ▌", "     </color><color_white>░</color><color_dark_gray>█</color><color_light_gray>▓▓▓</color><color_dark_gray>▓█</color><color_light_gray>▓</color><color_dark_gray>▒░☻►</color><color_light_gray>▌", "     </color><color_white>░</color><color_dark_gray>█</color><color_light_gray>▓▓▓</color><color_dark_gray>▓</color><color_light_gray>▓▓</color><color_dark_gray>▒</color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>▓▄▄_", "     ▓▓▓▓▓</color><color_dark_gray>▓</color><color_light_gray>▓▓</color><color_dark_gray>░</color><color_light_gray>▓</color><color_dark_gray>████</color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▓▓", "    ▐▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_white>▒</color><color_light_gray>▓</color><color_dark_gray>▓█</color><color_light_gray>▓</color><color_dark_gray>▒</color><color_light_gray>▓^ </color><color_dark_gray>▀▀</color><color_light_gray>▒▓▓▓", "    ▓▓</color><color_dark_gray>█</color><color_light_gray>▓▓▓</color><color_dark_gray>▓</color><color_light_gray>▓</color><color_dark_gray>█▒", "    </color><color_light_gray>▀</color><color_dark_gray>▀</color><color_light_gray>▓▓</color><color_white>▓</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓▒▓", "       ▀█</color><color_dark_gray>██▓░▌", "        </color><color_light_gray>°█</color><color_dark_gray>██░▌", "          </color><color_light_gray>█</color><color_dark_gray>█░▌", "          </color><color_light_gray>▐</color><color_dark_gray>██▒", "           </color><color_light_gray>▓</color><color_dark_gray>█▓</color><color_light_gray>▌", "          │</color><color_white>▓</color><color_dark_gray>███</color><color_light_gray>▓▓▓", "         ▓▓</color><color_white>▓</color><color_dark_gray>█████</color><color_light_gray>▓", "         ▓</color><color_dark_gray>▒</color><color_light_gray>▓</color><color_dark_gray>█████░", "         </color><color_light_gray>▓</color><color_dark_gray>███████░", "       </color><color_light_gray>/▬▓</color><color_dark_gray>███████░", "       </color><color_light_gray>▀▓</color><color_dark_gray>▀▀▀</color><color_light_gray>▀</color><color_dark_gray>^^^^°"]}, {"type": "ascii_art", "id": "m240", "picture": ["", "         <color_light_gray>_▄", "         █</color><color_dark_gray>▓▌", "         ▐</color><color_light_gray>▒▌", "        ▄♥▓</color><color_dark_gray>▌", "      ▀▀</color><color_light_gray>▓▓▓</color><color_dark_gray>▌", "         </color><color_light_gray>▐▓▌", "         ▐▓▌", "         ▐▓▌", "         ▐▒▌", "         █▓▓▓</color><color_dark_gray>█▌", "        ┌</color><color_light_gray>~█▓▓▓▌         ___▄▄▄▄▄▄</color><color_dark_gray>▓▒▌", "        ^</color><color_light_gray>█▓</color><color_dark_gray>▓████▓</color><color_light_gray>▓███▓██▓███████</color><color_dark_gray>▓▒░>", "         </color><color_light_gray>▓▓</color><color_dark_gray>███</color><color_light_gray>▓ ~ </color><color_dark_gray>`~~▀▀▀▀▀▀█▓░</color><color_light_gray>> ^~", "        ▓▓</color><color_dark_gray>◙██▒▒▌", "        </color><color_light_gray>▓▓</color><color_dark_gray>◙▓▓░▌</color><color_light_gray>▌", "        ▓▓</color><color_dark_gray>◙▓▓░▒▌", "        </color><color_light_gray>▓▓</color><color_dark_gray>◙▓▓░▌</color><color_light_gray>▌", "        ▓▓</color><color_dark_gray>◙▓█░▒</color><color_light_gray>▌", "        ▓▒</color><color_dark_gray>◙▓▐██</color><color_light_gray>`", "        ▓▒</color><color_dark_gray>◙▒██</color><color_light_gray>▓", "        ▓▒</color><color_dark_gray>◙▌██</color><color_light_gray>▓▌", "        ▓▒</color><color_dark_gray>◙▌██</color><color_light_gray>▓▌", "        ▓▓█▓</color><color_dark_gray>▒██</color><color_light_gray>▄", " \\▓</color><color_dark_gray>█▀~▀▀</color><color_white>▒</color><color_light_gray>▓▓▓</color><color_dark_gray>█</color><color_light_gray>▒▓█", " ▐▓</color><color_dark_gray>▓</color><color_light_gray>▌  ▓</color><color_dark_gray>██████▌", " </color><color_light_gray>▐▓</color><color_dark_gray>█</color><color_light_gray>▌  ▓▓▓▓▓</color><color_dark_gray>▓█▌", " </color><color_light_gray>▐▓</color><color_dark_gray>▓</color><color_light_gray>▌  ▓</color><color_dark_gray>▓███▒██", "  </color><color_light_gray>▀</color><color_dark_gray>▀   </color><color_light_gray>▓</color><color_dark_gray>▒▒</color><color_light_gray>▓</color><color_dark_gray>█▓██", "      </color><color_light_gray>▐▓</color><color_dark_gray>░</color><color_light_gray>▓▓</color><color_dark_gray>█▒██", "      </color><color_light_gray>▐</color><color_dark_gray>▓░▓██▓██", "      </color><color_light_gray>▐</color><color_dark_gray>░▒▓</color><color_light_gray>▓▓</color><color_dark_gray>▓██", "      </color><color_light_gray>▐</color><color_dark_gray>▒▓█</color><color_light_gray>▓▓▓▓▓", "      ▐</color><color_dark_gray>▒▓█</color><color_light_gray>▓</color><color_dark_gray>████", "      </color><color_light_gray>▐</color><color_dark_gray>▒▒█</color><color_light_gray>▓▓</color><color_dark_gray>██</color><color_light_gray>▓", "      ▐</color><color_dark_gray>▒▒█</color><color_light_gray>▓▒</color><color_dark_gray>███</color><color_light_gray>▓▓▌▀</color><color_dark_gray>┐", "      </color><color_light_gray>▐</color><color_dark_gray>▒▓▓</color><color_light_gray>▓▓</color><color_dark_gray>███▒▓</color><color_light_gray>▓</color><color_dark_gray>▄┤", "      </color><color_light_gray>▐▓▓▒▒</color><color_dark_gray>████▓██</color><color_light_gray>▓</color><color_dark_gray>▌", "      ▐</color><color_light_gray>▓█</color><color_dark_gray>█</color><color_light_gray>▓▓▓▒▒</color><color_dark_gray>▓█████▄▄▄", "      ▐</color><color_light_gray>▓█</color><color_dark_gray>█</color><color_light_gray>▓▓▒▒</color><color_dark_gray>██▀</color><color_light_gray>~▀</color><color_dark_gray>▀████", "      </color><color_light_gray>▀</color><color_dark_gray>~</color><color_light_gray>█▒▓▒▒</color><color_dark_gray>█</color><color_light_gray>▒       ~", "        </color><color_dark_gray>╙██████", "         </color><color_light_gray>^</color><color_dark_gray>█████", "           ████", "           </color><color_light_gray>▓▓</color><color_dark_gray>██</color><color_light_gray>█", "           </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>███</color><color_light_gray>▓▓", "           ▓▓</color><color_dark_gray>█████</color><color_light_gray>Γ", "           ▓▓</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>█▌", "          ▐</color><color_light_gray>█</color><color_dark_gray>███</color><color_light_gray>▓▓</color><color_dark_gray>█</color><color_light_gray>▓", "          ▓</color><color_dark_gray>███</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>██", "         /███▀▀▀▀▀~", "        ☻☻</color><color_light_gray>▀"]}, {"type": "ascii_art", "id": "m27_iar", "picture": ["", "       <color_light_gray>▄g", "       ▓▓", "       ▓▌", "       █</color><color_dark_gray>▌", "       </color><color_light_gray>█</color><color_dark_gray>▌", "       </color><color_light_gray>█</color><color_dark_gray>▌", "       </color><color_light_gray>▓█▄", "     ▄▄██▒</color><color_dark_gray>_", "    </color><color_light_gray>█▓▐</color><color_white>░</color><color_light_gray>▌</color><color_dark_gray>▓</color><color_light_gray>▓", " ■■▐▓▒</color><color_dark_gray>▓</color><color_light_gray>▒</color><color_dark_gray>▓▓</color><color_light_gray>▓", "    ▓▒▐</color><color_white>░</color><color_light_gray>▌</color><color_dark_gray>▓</color><color_light_gray>▓", "    ▓▒▐</color><color_white>░</color><color_light_gray>▌</color><color_dark_gray>▓</color><color_light_gray>▓", "    </color><color_dark_gray>{</color><color_light_gray>▒</color><color_dark_gray>▓</color><color_light_gray>▓</color><color_dark_gray>▓▓█", "    {▓</color><color_light_gray>▐</color><color_white>░</color><color_light_gray>▌▓▓", "    </color><color_dark_gray>{</color><color_light_gray>▒▐</color><color_white>░</color><color_light_gray>▌▓</color><color_dark_gray>█", "    {▓▓</color><color_light_gray>▓</color><color_dark_gray>▓▓</color><color_light_gray>▓", "    </color><color_dark_gray>{</color><color_light_gray>▒▐</color><color_white>░</color><color_light_gray>▌▓▓", "    </color><color_dark_gray>{█</color><color_light_gray>▐</color><color_white>░</color><color_light_gray>▌</color><color_dark_gray>█</color><color_light_gray>▓", "    </color><color_dark_gray>{█</color><color_light_gray>▓</color><color_dark_gray>▓▓</color><color_light_gray>▓▓", "    </color><color_dark_gray>{█</color><color_light_gray>▐</color><color_white>░</color><color_light_gray>▌▓▓", "    </color><color_dark_gray>{██▓▓▓▒", "    {██</color><color_white>░░</color><color_dark_gray>▓</color><color_light_gray>▓  _  _____</color><color_dark_gray>▄▄▄</color><color_light_gray>▄☻", "    </color><color_dark_gray>{██</color><color_white>▒░</color><color_dark_gray>▓█</color><color_light_gray>▓▓</color><color_dark_gray>███</color><color_light_gray>▬__,∙*╛°~</color><color_dark_gray>█▌", "    {██</color><color_white>▒░</color><color_dark_gray>▓▓</color><color_light_gray>▓▓</color><color_dark_gray>███</color><color_light_gray>▬▬▬▲∙═</color><color_dark_gray>─</color><color_white>~ </color><color_dark_gray>█/", "    {██</color><color_white>▒░</color><color_dark_gray>▒▒</color><color_light_gray>▓▓</color><color_dark_gray>███</color><color_light_gray>∞∞∞∞°°</color><color_dark_gray>/▀▀", "    {█████</color><color_light_gray>▓▓▓</color><color_dark_gray>~~</color><color_light_gray>│", "   ╓</color><color_dark_gray>███</color><color_light_gray>▓</color><color_dark_gray>███</color><color_white>▒</color><color_light_gray>▓~`⌡", " ▀▀▀▓</color><color_dark_gray>◙█</color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓▓▓</color><color_dark_gray>▄</color><color_light_gray>▄█", "    </color><color_dark_gray>▐██</color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓▓▓▓▓▓█▄▓▄_", "     ▓▓█</color><color_dark_gray>███</color><color_light_gray>▓▀▀▀███▓█▓▓▓", "     ▀▓█</color><color_dark_gray>███Γ    </color><color_light_gray>▀█</color><color_white>░</color><color_light_gray>██▓▓", "      ▓</color><color_dark_gray>██▄         </color><color_light_gray>~▀▀°", "      ▓▓▓▓</color><color_dark_gray>▌", "      </color><color_light_gray>▓▓▓█</color><color_dark_gray>█</color><color_light_gray>█▓", "      ███▓</color><color_dark_gray>█</color><color_light_gray>█▓", "      ████</color><color_dark_gray>█</color><color_light_gray>█▓_", "      ▓▓▓█</color><color_dark_gray>█</color><color_light_gray>█▓█▄_", "      ████▓</color><color_white>▒</color><color_light_gray>████▓", "      ▓</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█</color><color_white>▒</color><color_light_gray>███▓█</color><color_dark_gray>█", "     ▐██</color><color_light_gray>██▓▓▓▓▓▓▓</color><color_dark_gray>█", "      </color><color_light_gray>▀▀▀</color><color_dark_gray>▀</color><color_light_gray>█▓▓</color><color_dark_gray>▀▀▀▀</color><color_light_gray>▀"]}, {"type": "ascii_art", "id": "m1_garand", "picture": ["", "  <color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>▄", "  ▐▓</color><color_white>░", "  </color><color_dark_gray>▐▌</color><color_light_gray>▓</color><color_dark_gray>▌", "  ▐▌</color><color_light_gray>▓</color><color_dark_gray>▌", "  ▐▌░▌", "  ▐▌█▌", "  ▐</color><color_light_gray>▒▒</color><color_dark_gray>▓", "  </color><color_brown>▒█</color><color_light_red>░</color><color_brown>░</color><color_dark_gray>○", "  </color><color_brown>██</color><color_light_red>░</color><color_brown>░", "  ███░", "  ███░", "  ▒█▓░", "  ▓</color><color_light_red>░</color><color_brown>▓░", "  </color><color_dark_gray>█▓▓░", "  </color><color_brown>▒█▓▓░</color><color_dark_gray>°", "  </color><color_brown>▒▓▓▓▌", "  ▒▓▓▓░", "  ░▒▓▓▒", "  ░</color><color_dark_gray>▐</color><color_brown>█▓░", "  ░</color><color_dark_gray>▐</color><color_brown>█▓░▌", "  ░░█▓░▌", "  ░</color><color_dark_gray>▐</color><color_brown>▓▓░▌", "  ░</color><color_dark_gray>▐</color><color_brown>▓█▓░", " </color><color_dark_gray>▐█▐</color><color_brown>██▓░", "  </color><color_dark_gray>»▐</color><color_brown>▓█▓░</color><color_dark_gray>▄", "  ▐█</color><color_brown>██▓░</color><color_dark_gray>█", "   █</color><color_brown>██▓░</color><color_dark_gray>█", "  ▐█</color><color_brown>██▓░</color><color_dark_gray>█", " ▐░█</color><color_brown>██▓░</color><color_dark_gray>▒", "_█▒█</color><color_brown>██▓░</color><color_dark_gray>▒█", " ~▀█</color><color_brown>█▓▓░</color><color_dark_gray>▌ ▌", "   █</color><color_brown>▓▓▓░</color><color_dark_gray>▌/`", "   ▀█</color><color_brown>▓▓░░", "     ▐▓▓░▄", "      ░█▓░░▄", "      ▒██▓▒░", "      ▒██▓▓░", "      ████▓▓▌", "      ▓███▓▓░", "      ▓███▓▓▒░", "      ▓████▓▓░▌", "      ▓█████</color><color_light_red>░</color><color_brown>▓░", "      ▓█████</color><color_light_red>░</color><color_brown>▓░▌</color><color_dark_gray>○", "      </color><color_brown>███████</color><color_light_red>░</color><color_brown>▓░▌", "      ▒▓▓▓████▓░░", "      </color><color_dark_gray>²▀▀</color><color_light_gray>▀~~</color><color_dark_gray>~</color><color_light_gray>▀▀▀^"]}, {"type": "ascii_art", "id": "modular_m4a1", "picture": ["                           ", "                           ", "       <color_dark_gray>▐█                  ", "       ▐█                  ", "       ▐</color><color_light_gray>▓                  ", "       </color><color_dark_gray>▐█                  ", "       ▐█                  ", "       ▐█                  ", "       ▐█                  ", "       ▐█                  ", "  ▄▄▄▄▄█</color><color_light_gray>▒▒                 ", "  </color><color_dark_gray>▀█■  ◙█▌                 ", "     ▀▄</color><color_light_gray>▒</color><color_dark_gray>██▓                ", "     ▐█</color><color_light_gray>▓</color><color_dark_gray>███▌               ", "     ▐█</color><color_light_gray>▓▒</color><color_dark_gray>██▌               ", "     ▐█</color><color_light_gray>▒▒</color><color_dark_gray>▓█▌               ", "     ▐█</color><color_light_gray>▒▓</color><color_dark_gray>██▌               ", "     ▐█</color><color_white>░</color><color_light_gray>▓</color><color_dark_gray>███               ", "     ▐█</color><color_white>░</color><color_light_gray>▒</color><color_dark_gray>▓██               ", "     ▐█</color><color_white>░</color><color_light_gray>█</color><color_dark_gray>███               ", "      ▐█</color><color_white>░</color><color_dark_gray>▓█▌               ", "   </color><color_light_gray>⌂▄</color><color_dark_gray>▄»</color><color_light_gray>▒▓</color><color_dark_gray>▓█          __▄▄  ", "  ▐</color><color_light_gray>▀█▒</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>▓▓</color><color_dark_gray>■█▄</color><color_light_gray>▄</color><color_dark_gray>▄■■█</color><color_light_gray>▒</color><color_dark_gray>████ ", "  </color><color_light_gray>▓  ▒</color><color_dark_gray>██</color><color_light_gray>▒</color><color_dark_gray>██</color><color_light_gray>▓▒▓</color><color_dark_gray>███</color><color_light_gray>▓▓▒</color><color_dark_gray>██</color><color_light_gray>▒▒</color><color_dark_gray>██▌", "  </color><color_light_gray>▓  ▒</color><color_dark_gray>██</color><color_light_gray>▒</color><color_dark_gray>████████</color><color_light_gray>▓▓▒▒▒▒▒</color><color_dark_gray>██▌", "  </color><color_light_gray>▓  ▒</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>██████████▀▀▀▀~~  ", " </color><color_light_gray>▐▓  ▓</color><color_dark_gray>██</color><color_white>░</color><color_dark_gray>████▌ ▐           ", " </color><color_light_gray>▐▓  ▓</color><color_dark_gray>██</color><color_light_gray>▒</color><color_dark_gray>████▄≈▐           ", " </color><color_light_gray>▐█▓▓▒</color><color_dark_gray>▓█</color><color_light_gray>▒</color><color_dark_gray>█████▄█▄          ", " </color><color_light_gray>◙▓</color><color_dark_gray>▓▓</color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>▒</color><color_dark_gray>████████▄▄        ", "    </color><color_light_gray>▓▓</color><color_dark_gray>███▓██████████▄▄▄    ", "      ▒▒███~▓ ^▀███████    ", "       █</color><color_light_gray>▒</color><color_dark_gray>███     ~▀███▌    ", "       ▐</color><color_light_gray>▒</color><color_dark_gray>░▐█       ^▀      ", "       ▐</color><color_light_gray>▒</color><color_dark_gray>▌▐▒█▄             ", "       ▐</color><color_light_gray>▒</color><color_dark_gray>▒▒▒███            ", "       ▓█░░▒███            ", "       ▓█░▓▒███▌           ", "      [▓█░▓▒████           ", "      [▒█▒██████▌          ", "      [▒█░▓▒▓▓███          ", "        ▀▀▀▀▀▀^~           "]}, {"type": "ascii_art", "id": "scar-H", "picture": ["", "        <color_light_gray>█▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "       </color><color_dark_gray>◄</color><color_light_gray>◛</color><color_dark_gray>█▌", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓</color><color_dark_gray>Γ", "      </color><color_light_gray>░◛▓█▌", " </color><color_dark_gray>▓</color><color_light_gray>☻☻</color><color_dark_gray>▄</color><color_light_gray>▒░▒██</color><color_dark_gray>▌", "     ▐</color><color_white>▒</color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>█▄", "     ▐██</color><color_light_gray>▓▓</color><color_dark_gray>██▌", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>███▌", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>███▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>◛</color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>▌", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█▒▒</color><color_white>▲</color><color_dark_gray>▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█▓</color><color_dark_gray>██▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>▓▒██", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>▒██████</color><color_light_gray>▓</color><color_dark_gray>▌██████ █", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌██████</color><color_light_gray>▓</color><color_dark_gray>█▐█████!█", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌█▓▓████</color><color_light_gray>▓</color><color_dark_gray>░███▓▓░█", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌██▓░▓▀▀▀~~</color><color_light_gray>▀▀▀</color><color_dark_gray>▀</color><color_light_gray>▀", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█▒█▓▓▓   Γ", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>██████►∙⌐Γ", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█▓ </color><color_dark_gray>██Ñ▓██◛▌", "     </color><color_white>☻</color><color_dark_gray>██</color><color_light_gray>█▓ </color><color_dark_gray>▓▓▓è▒▒█</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>_", "    ▐▒▐█</color><color_light_gray>█▓</color><color_dark_gray>███░-▀</color><color_light_gray>█</color><color_dark_gray>█████▄▄", " </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▌▐▒</color><color_light_gray>▓▓</color><color_dark_gray>██▌</color><color_light_gray>▓   ▀█▓</color><color_dark_gray>███▌", "     ▐███████</color><color_light_gray>▓      ▀█</color><color_dark_gray>█", "      </color><color_light_gray>█▓</color><color_dark_gray>▓▒▒▒▒</color><color_light_gray>▓", "      </color><color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>█████▌", "      </color><color_light_gray>▐▓</color><color_dark_gray>█████▌", "       </color><color_light_gray>▓▓</color><color_dark_gray>████▌", "       </color><color_light_gray>▓▓</color><color_dark_gray>████▌", "       ▐</color><color_light_gray>▓</color><color_dark_gray>████▌", "        █████▌", "        </color><color_light_gray>▐▓</color><color_dark_gray>███", "        </color><color_light_gray>▐▓</color><color_dark_gray>███▌", "        </color><color_light_gray>▐▓</color><color_dark_gray>████▄_", "        </color><color_light_gray>▐</color><color_white>▒</color><color_dark_gray>███████▄▄", "        ▄</color><color_light_gray>▓</color><color_dark_gray>██████████", "        ▀</color><color_light_gray>▀</color><color_dark_gray>▀~~~</color><color_light_gray>~~~</color><color_dark_gray>^^▀"]}, {"type": "ascii_art", "id": "scar-L", "picture": ["", "        <color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "        </color><color_white>▐</color><color_light_gray>▓", "       </color><color_dark_gray>◄</color><color_light_gray>◛</color><color_dark_gray>█▌", "        </color><color_white>▐</color><color_light_gray>▓</color><color_dark_gray>Γ", "      </color><color_light_gray>░◛▓█▌", " </color><color_dark_gray>▓</color><color_light_gray>☻☻</color><color_dark_gray>▄</color><color_light_gray>▒░▒██</color><color_dark_gray>▌", "     ▐</color><color_white>▒</color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>█▄", "     ▐██</color><color_light_gray>▓▓</color><color_dark_gray>██▌", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>███▌", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>███▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>◛</color><color_light_gray>█</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>▌", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█▒▒▓</color><color_dark_gray>▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█▓</color><color_dark_gray>█</color><color_white>▲</color><color_dark_gray>▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>▓▓█▌", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>▒███_    </color><color_light_gray>_▄</color><color_dark_gray>▄▄▄▄", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌████████▓▓████</color><color_light_gray>▌", "     ▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌░▓▓</color><color_light_gray>▓</color><color_dark_gray>▒███████▀▀▀", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█</color><color_dark_gray>▌▐█▓▓▀▀▀~</color><color_light_gray>▀▀</color><color_dark_gray>▀</color><color_light_gray>▀", "     ▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█▒█▓▓   Γ", "     </color><color_light_gray>▐</color><color_white>╢</color><color_dark_gray>█</color><color_light_gray>█</color><color_dark_gray>█████►∙⌐Γ", "     </color><color_light_gray>▐</color><color_dark_gray>██</color><color_light_gray>█▓</color><color_dark_gray>▌██▓██◛▌", "     </color><color_white>☻</color><color_dark_gray>██</color><color_light_gray>█▓</color><color_dark_gray>▌▓▓è▒▒█</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>_", "    ▐▒▐█</color><color_light_gray>█▓</color><color_dark_gray>███-▀</color><color_light_gray>█</color><color_dark_gray>█████▄▄", " </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▌▐▒</color><color_light_gray>▓▓</color><color_dark_gray>██▌</color><color_light_gray>▓  ▀█▓</color><color_dark_gray>███▌", "     ▐███████</color><color_light_gray>▓     ▀█</color><color_dark_gray>█", "      </color><color_light_gray>█▓</color><color_dark_gray>▓▒▒▒▒</color><color_light_gray>▓", "      ▐▓</color><color_dark_gray>█████▌", "       </color><color_light_gray>▓▓</color><color_dark_gray>████▌", "       ▐</color><color_light_gray>▓</color><color_dark_gray>████▌", "        █████▌", "        </color><color_light_gray>▐▓</color><color_dark_gray>███▌", "        </color><color_light_gray>▐▓</color><color_dark_gray>████▄_", "        </color><color_light_gray>▐</color><color_white>▒</color><color_dark_gray>███████▄▄", "        ▄</color><color_light_gray>▓</color><color_dark_gray>██████████", "        ▀</color><color_light_gray>▀</color><color_dark_gray>▀~~~</color><color_light_gray>~~~</color><color_dark_gray>^^▀"]}, {"type": "ascii_art", "id": "ppk", "picture": ["      <color_light_gray>╓▄▄▄▄▄▄</color><color_dark_gray>▄▄--▄</color><color_light_gray>▄▄</color><color_dark_gray>▄▄▄▄▄▄", "    </color><color_light_gray>▒▀</color><color_dark_gray>█</color><color_white>░</color><color_dark_gray>▓▓▒▀▀▀▀▀▀▀▀▀▀▓▓▀▀▀▌", "  _▬░ É</color><color_white>░</color><color_dark_gray>░☺░≈</color><color_light_gray>•*∟***∟**</color><color_dark_gray>∞</color><color_light_gray>↔</color><color_dark_gray>▄</color><color_light_gray>▄▀", " </color><color_dark_gray>▀▀▬░ ¢◛▒∞⌐ ┐</color><color_light_gray>▄</color><color_dark_gray>-█~ ▀◛▀~", "    ▐</color><color_light_gray>▒</color><color_dark_gray>███▓⌐  | ╘▬ _¿", "   _█▓▒███:▀</color><color_light_gray>~ </color><color_dark_gray>~~^~", "   ▓▒▒j██▌</color><color_light_gray>▐", "  </color><color_dark_gray>▓▓█████•</color><color_light_gray>Γ", "  </color><color_dark_gray>\\__   =</color><color_light_gray>▒", "     </color><color_dark_gray>`^▀▀▀"]}, {"type": "ascii_art", "id": "sig_p320", "picture": ["     <color_dark_gray>▄▄_____________________▄▄▄", "    ▐█▓▓▒◙█◙▒▓▓▓░░░░░█████▒████", "    ▒░░░▓²²▓▓░▒▒▒░▒#▓▓▓▓▓▓////▓", " _▄▓▒░T░░▒▒◙▒▓████▒░▒▒▒▒▒▒░▒▒▒▓", "  ~▀██▒▒▒#▒▓█▀▀◙▀▀▀▀▀▄▀°°`▀ °</color><color_light_gray>▀</color><color_dark_gray>`", "    ▐███☻▒░██   ▲    ▌", "    </color><color_light_gray>▓</color><color_dark_gray>░####░██▀∞∙∙∙▲▲☻°", "   ▐█◙####██", "  </color><color_light_gray>▐</color><color_dark_gray>█▓###▒▓█▌", "  </color><color_light_gray>▓</color><color_dark_gray>█▒####▓█Γ", "  ██▓▓░░██▌", "  `▀▀▀▀▀▀▀°"]}, {"type": "ascii_art", "id": "sig_pro_40", "picture": ["      <color_white>_", "    </color><color_light_gray>▄</color><color_dark_gray>▄█</color><color_light_gray>▄▄▄▄▄▄▄▄▄▄▄▄▄_▄▄▄▄▄▄▄▄▄▄♥◙", "  </color><color_dark_gray>♥█</color><color_light_gray>▓</color><color_dark_gray>█████@██████▒▓▓▓☻███████████▌", "   ▓▓▓░▒▒▒▒▓▒▒▒▒◙▀▀◙◙▓▓█▓▓▓▓▓▓▒▓▓▌", "   ▀▀░░░r▒░░░▒▒▒▒█▒▒▒▓▓▓Pⁿ▒░▒▀▀▀ƒ`", "   ▀▄`☺ `  #⌂▓▀█░▀▀▀▀██</color><color_light_gray>▀~ </color><color_dark_gray>° ~</color><color_light_gray>`</color><color_dark_gray>^~", "    ▐#   `  ⌂▌  ▐╥   ▐▌", "    ▌⌂ `  #▓█▀♥▬▬▬▬▲♥▀", "   /#   ` #╬Γ", "  ▐#  ` /#⌂▌", "  ▒⌂`   #░░Γ", "  ░░# ` #⌂</color><color_light_gray>▓", "    </color><color_dark_gray>▀▀▀███</color><color_light_gray>▓"]}, {"type": "ascii_art", "id": "sig_mosquito", "picture": ["      <color_dark_gray>▄▄▄▄▄▄▄▄·    ·▄▄▄▄▄▄▄▄▄▄", "   </color><color_light_gray>♠▓</color><color_dark_gray>███▒▒▒███░░░░░░██████████▌", "    ▄▒▒▒▒▒▒▒▓▒▒▒▒▒▓▒███████▌▒▒▌", "  </color><color_light_gray>•</color><color_dark_gray>☻███████░██▒☻████░██ ▀▀▀ ▀▀", "     ▐█████▒░``█</color><color_light_gray>Γ  </color><color_dark_gray>█", "     </color><color_light_gray>▓</color><color_dark_gray>██</color><color_light_gray>☻</color><color_dark_gray>█▒█░■</color><color_light_gray>__</color><color_white>▒</color><color_dark_gray>__▌", "    ▐████▒░▌", "   ▐█████░▒</color><color_light_gray>Γ", "   ▒</color><color_dark_gray>██████▌", "   ███</color><color_light_gray>☻</color><color_dark_gray>█░░Γ", "   ▀</color><color_light_gray>▓</color><color_dark_gray>▓▓▓░░♠"]}, {"type": "ascii_art", "id": "sw_610", "picture": ["            <color_dark_gray>☻■▄▄▄▄▄▄</color><color_white>▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄</color><color_light_gray>■▒▓", "        </color><color_dark_gray>°</color><color_light_gray>¶</color><color_dark_gray>▄</color><color_white>▓█</color><color_light_gray>▒▒▒▒▒▒░</color><color_white>░▓▒▒▒▒▒▒░</color><color_light_gray>█</color><color_white>░░</color><color_light_gray>█</color><color_white>░▒▒▒▒▒</color><color_light_gray>█", "       </color><color_dark_gray>▄</color><color_light_gray>/</color><color_white>▒███</color><color_light_gray>▒█████▓</color><color_white>█▓</color><color_light_gray>▓▓▓▒</color><color_white>░</color><color_light_gray>▒▓▓▓▓</color><color_white>░</color><color_light_gray>▓</color><color_white>░</color><color_light_gray>▓</color><color_white>░</color><color_light_gray>▓▒</color><color_dark_gray>♥", "     </color><color_light_gray>▐</color><color_dark_gray>░█</color><color_light_gray>▒</color><color_white>████▓</color><color_light_gray>░░░░░</color><color_white>▒▓√", "     </color><color_dark_gray>▒████</color><color_white>▒█▓▒</color><color_light_gray>▒▓▓</color><color_white>▓▒▒</color><color_light_gray>▓</color><color_white>⌡", "   </color><color_dark_gray>╓▓▒▒▒▒▒░░</color><color_light_gray>▓^</color><color_dark_gray>▐▌ </color><color_light_gray>▐▌", "  ▐</color><color_dark_gray>▒▒░▒▒█▀~^▀*∙√■~", "  </color><color_light_gray>▓</color><color_dark_gray>▒▒▒▒▒</color><color_light_gray>▓", " </color><color_dark_gray>▐▒▒▒▒▒▒▌", " ▀█████▒", "   </color><color_light_gray>~^^"]}, {"type": "ascii_art", "id": "sw_22a", "picture": [" <color_dark_gray>▐▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄∙", " █▓▓▓██▓▓▓▓▓█▄▄▄▄▄██▄▄▄▄▄█▌", "▐▓▓▓███████▓</color><color_white>▒▒▒▒▒▒</color><color_dark_gray>▀▀▀▀▀▀▀▀`", "^▀~▀▒██████</color><color_light_gray>▒</color><color_white>▀▀█▀</color><color_dark_gray>▀", "   ▒▓████▒</color><color_light_gray>☻</color><color_white>▒_∙╜", "  </color><color_dark_gray>▐▒████▌", "  ▒▓███▓", " ▐▒███▓Γ", "  ▀▀▀█☻"]}, {"type": "ascii_art", "id": "redhawk", "picture": ["          <color_dark_gray>∙                          _▄", "       </color><color_light_gray>▬_▄▒█▒▒▓▓▓▓</color><color_white>▒</color><color_light_gray>█</color><color_white>▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒</color><color_light_gray>██", "        ▐▓</color><color_white>░</color><color_light_gray>█▓</color><color_white>▒▓▓▓</color><color_light_gray>▌██▓▓▓▓▓▓▓▀▀▀▀▀▀▀▀▀▀▀▀▀", "     ▄███▓</color><color_white>░</color><color_light_gray>▒░░░░░</color><color_white>░</color><color_light_gray>█▌°°~~~", "    </color><color_brown>▄█▓░</color><color_light_gray>████▒▒▒▒▒██", "   </color><color_brown>█▓</color><color_light_red>░</color><color_brown>▒░</color><color_light_gray>█</color><color_white>▒</color><color_light_gray>█▀▀  ▐Γ", "  </color><color_brown>▓▓</color><color_white>Θ</color><color_brown>▒▒</color><color_light_gray>^  ╘_</color><color_dark_gray>_</color><color_light_gray>◙_/", " </color><color_brown>▐█</color><color_light_red>░▒</color><color_brown>▓</color><color_light_gray>▌", " </color><color_brown>█</color><color_light_red>▒█░</color><color_brown>▓▓"]}, {"type": "ascii_art", "id": "ruger_LCP", "picture": ["   <color_dark_gray>▄▄▄▄▄▄▄▄</color><color_light_gray>▄▄</color><color_dark_gray>▄▄▄▄▄▄▄", " ╓▒▒</color><color_light_gray>////</color><color_dark_gray>▒▒▒▒▒▒▒▒▒▒░▒", " ▒▓▓▓▒▓▒▓▓▓▓▒</color><color_light_gray>☺</color><color_dark_gray>▒▒▀▀▀▀", "  \\▒#○#░▓</color><color_light_gray>▓ </color><color_dark_gray>`▌  </color><color_light_gray>▒", "   </color><color_dark_gray>▒░▒#▒▓</color><color_light_gray>▓</color><color_dark_gray>▬_,∙*°", "  /##</color><color_light_gray>Θ</color><color_dark_gray>#▒</color><color_light_gray>▓", "  </color><color_dark_gray>#####█▌", "  </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▀</color><color_light_gray>▀`"]}, {"type": "ascii_art", "id": "m1911", "picture": ["", "     <color_dark_gray>▄▄▄▄▄▄▄▄▄▄</color><color_light_gray>▄▄▄▄▄</color><color_dark_gray>▄▄▄▄▄▄▄▄▄▄▄█▄", "  ~▀███║║║║█████▓████████████▓██▒", "    </color><color_light_gray>▒</color><color_dark_gray>▒▒║║║║███████████████▓▓▓░☻█▓", " ■☻</color><color_light_gray>▓</color><color_dark_gray>█</color><color_brown>▓▓▓▓▓▓</color><color_light_red>░</color><color_dark_gray>█▀░▀▀▒▒</color><color_light_gray>▓</color><color_dark_gray>▀▀▀▀▀▀▀▀</color><color_light_gray>▀▀</color><color_dark_gray>~~~", "    ▐</color><color_brown>▓▓▓</color><color_light_gray>Θ</color><color_brown>▓</color><color_light_red>░</color><color_dark_gray>▓▓ </color><color_light_gray>▌   </color><color_dark_gray>│", "    </color><color_light_gray>}</color><color_brown>▓▓▓▓</color><color_light_red>▒▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>▀♥</color><color_light_gray>▲▲◄^", "   </color><color_dark_gray>▐▓</color><color_brown>▓▓▓▓</color><color_light_red>░</color><color_dark_gray>▓Γ", "  ▄▓</color><color_brown>▓▓▓▓</color><color_light_red>▒</color><color_dark_gray>▓</color><color_light_gray>▒", " </color><color_dark_gray>▐▓</color><color_brown>▓▓</color><color_light_gray>Θ</color><color_brown>▓▓</color><color_light_red>░</color><color_dark_gray>░</color><color_light_gray>▌", " </color><color_dark_gray>▒</color><color_brown>▓▓▓▓▓</color><color_light_red>░</color><color_brown>▓</color><color_dark_gray>▌", " ╙</color><color_light_gray>▀▀</color><color_dark_gray>▀▀▀▀▀</color><color_light_gray>°"]}, {"type": "ascii_art", "id": "P3AT", "picture": ["   <color_dark_gray>▄▄▄▄▄▄▄▄</color><color_light_gray>▄▄</color><color_dark_gray>▄▄▄▄▄▄▄", " ╓▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒░▒", " ▒▓▓▓▒▓▒▓▓▓▓▒▒▒▒▀▀▀▀", "  \\▒#○#░▓</color><color_light_gray>▓ </color><color_dark_gray>`▌  </color><color_light_gray>▒", "   </color><color_dark_gray>▒░▒#▒▓</color><color_light_gray>▓</color><color_dark_gray>▬_,∙*°", "  /##▒#▒</color><color_light_gray>▓", "  </color><color_dark_gray>#####█▌", "  </color><color_light_gray>▀</color><color_dark_gray>▀▀▀▀</color><color_light_gray>▀`"]}, {"type": "ascii_art", "id": "glock_40mos", "picture": ["                                      ", "   <color_light_gray>__</color><color_dark_gray>█</color><color_light_gray>______________________________</color><color_dark_gray>⌂</color><color_light_gray>_", "   ▓▌</color><color_dark_gray>▌▌▌▌█████░░░░████████████████████", "   </color><color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌█████████████████████████████", "   </color><color_light_gray>▀▀▓▓▓▓▓▓▓▓▓▓</color><color_dark_gray>@</color><color_light_gray>▓▓▒▒Θ▓▓████</color><color_dark_gray>▀▀▀▀▀▀▀    ", "     </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▓▓█▀██   </color><color_light_gray>▒                 ", "     </color><color_dark_gray>█▓▓▓▓▓▓██▄_◛█</color><color_light_gray>▄</color><color_dark_gray>▄█                 ", "    </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓░                         ", "   </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                          ", " ╓▒</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>█                           ", " ▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                            ", " </color><color_dark_gray>▀█▒▒▒▒▒</color><color_light_gray>▒░                            ", "   </color><color_dark_gray>▀▀▀▀▀</color><color_light_gray>▀                             "]}, {"type": "ascii_art", "id": "glock_20", "picture": ["                                 ", "   <color_light_gray>__⌂_________________________∙_", "   ▓▌</color><color_dark_gray>▌▌▌▌█████░░░░███████████████", "   </color><color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌████████████████████████", "   </color><color_light_gray>▀█▓▓▓▓▓▓▓▓▓▓▓▓▓▒▒▒▓▓██████</color><color_dark_gray>████", "   </color><color_light_gray>▀▀▓▓▓▓▓▓▓▓▓▓</color><color_dark_gray>@</color><color_light_gray>▓▓▒▒Θ▓▓██████</color><color_dark_gray>▀☻▀ ", "     </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▓▓█▀██   </color><color_light_gray>▒            ", "     </color><color_dark_gray>█▓▓▓▓▓▓██▄_◛█</color><color_light_gray>▄</color><color_dark_gray>▄█            ", "    </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓░                    ", "   </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                     ", "   ▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                     ", " ╓▒</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>█                      ", " ▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                       ", " </color><color_dark_gray>▀█▒▒▒▒▒</color><color_light_gray>▒░                       ", "   </color><color_dark_gray>▀▀▀▀▀</color><color_light_gray>▀                        "]}, {"type": "ascii_art", "id": "glock_19", "picture": ["   <color_light_gray>__</color><color_dark_gray>∙</color><color_light_gray>____________________</color><color_dark_gray>.</color><color_light_gray>_</color>", "   <color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌█████░░░░██████████</color>", "   <color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌██████████████████▌</color>", "   <color_light_gray>▀▀▓▓▓▓▓▓▓▓▓▓</color><color_dark_gray>@</color><color_light_gray>▓▓▒▒Θ▓▓██</color><color_dark_gray>▀☻⌡</color>", "     <color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▓▓█▀██   </color><color_light_gray>▒", "     </color><color_dark_gray>█▓▓▓▓▓▓██▄_▄█</color><color_light_gray>▄</color><color_dark_gray>▄⌡", "    </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓░", "   </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓", "   </color><color_dark_gray>█▒▒▒▒▒</color><color_light_gray>▒░", "    </color><color_dark_gray>▀▀▀▀▀</color><color_light_gray>▀"]}, {"type": "ascii_art", "id": "glock_18c", "picture": ["                                 ", "     <color_light_gray>__⌂_______________________∙_", "     ▓▌</color><color_dark_gray>▌▌▌▌█████░░░░█████████████", "     </color><color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌██████████████████████", "     </color><color_light_gray>▀▀▓▓▓▓▓▓▓▓▓▓</color><color_dark_gray>@</color><color_light_gray>▓▓▒▒Θ▓▓████</color><color_dark_gray>▀☻▀▀", "       </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▓▓█▀██   </color><color_light_gray>▒          ", "       </color><color_dark_gray>█▓▓▓▓▓▓██▄_◛█</color><color_light_gray>▄</color><color_dark_gray>▄█          ", "      </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓░                  ", "     </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                   ", "   ╓▒</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>█                    ", "   ▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓                     ", "   </color><color_dark_gray>▒▒▒▒▒▒▒</color><color_light_gray>▒░                     ", "   </color><color_dark_gray>▀░░░░░░</color><color_light_gray>▀                      ", "    </color><color_dark_gray>▒░░░░▌                       ", "   ▐░░░░▒                        ", "   ▒░░░▒                         ", "  ▒░░░░▌                         ", " ▐░░░░▒                          ", "  ▀▀☻▒▒▌                         "]}, {"type": "ascii_art", "id": "glock_17", "picture": ["   <color_light_gray>__⌂_______________________∙_", "   ▓▌</color><color_dark_gray>▌▌▌▌█████░░░░█████████████", "   </color><color_light_gray>▓▌</color><color_dark_gray>▌▌▌▌██████████████████████", "   </color><color_light_gray>▀▀▓▓▓▓▓▓▓▓▓▓</color><color_dark_gray>@</color><color_light_gray>▓▓▒▒Θ▓▓████</color><color_dark_gray>▀☻▀▀", "     </color><color_light_gray>▐</color><color_dark_gray>▓▓▓▓▓▓▓█▀██   </color><color_light_gray>▒", "     </color><color_dark_gray>█▓▓▓▓▓▓██▄_▄█</color><color_light_gray>▄</color><color_dark_gray>▄█", "    </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓░", "   </color><color_light_gray>▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓", " ╓▒</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>█", " ▓</color><color_dark_gray>▓▓▓▓▓▓▓</color><color_light_gray>▓", " </color><color_dark_gray>▀█▒▒▒▒▒</color><color_light_gray>▒░", "   </color><color_dark_gray>▀▀▀▀▀</color><color_light_gray>▀"]}, {"type": "ascii_art", "id": "flaregunb", "picture": ["           <color_dark_gray>♥▄</color><color_light_red>__-▄____________", "            </color><color_dark_gray>▐</color><color_brown>▄▄</color><color_red>▒▓▒▒▒▒▓▒░▒▓▒▒▒", "        </color><color_dark_gray>⌂⌂</color><color_brown>▄</color><color_brown>█</color><color_red>░░▓▓▓▓@▒▓▒▒▓▀▀▀▀▀", "      </color><color_dark_gray>⌂░</color><color_brown>░░░░░</color><color_light_red>▲</color><color_red>▒▒▓▓▒▓▒▓▒░", "     </color><color_dark_gray>▄░</color><color_brown>░░░░░░</color><color_red>░░▀█▀▀▀░▀</color><color_brown>↔~", "    </color><color_dark_gray>█░</color><color_brown>░░░π▀^▀▀▀ </color><color_dark_gray>Γ   </color><color_light_red>⌡", "   </color><color_brown>▐</color><color_light_gray>░</color><color_brown>░░░░</color><color_light_gray>▌     </color><color_brown>~↔</color><color_light_red>°</color><color_brown>°</color><color_light_red>~", "    </color><color_red>▌</color><color_dark_gray>░</color><color_brown>░░</color><color_brown>►</color><color_light_gray>Γ", "     </color><color_brown>▀</color><color_light_red>█</color><color_red>☻♥"]}, {"type": "ascii_art", "id": "coilgun", "picture": ["           <color_white>(</color><color_light_green>A</color><color_light_gray>)______</color><color_red>▄</color><color_light_gray>_____</color><color_red>▄</color><color_light_gray>__</color><color_dark_gray>_</color><color_light_gray>_____</color><color_dark_gray>_</color><color_light_gray>____</color><color_light_green>·", "</color><color_light_gray>║║║║║║║████</color><color_dark_gray>░☺</color><color_light_gray>███████</color><color_red>▐</color><color_light_gray>█████</color><color_red>▐</color><color_light_gray>██████▓███▄▄▄█", "</color><color_dark_gray>▒▓▓▓█@▀▀▓▓▒▒▒█</color><color_light_gray>██████</color><color_red>▐</color><color_light_gray>█████</color><color_red>▐</color><color_light_gray>██▒█▓█◙◙◙◙◙◙◙◙", "</color><color_dark_gray>░░░▀▓▓▓▓▓▓▀██▓▓▓██▓▌</color><color_red>▀</color><color_dark_gray>▐█▓▓▌</color><color_red>▀</color><color_dark_gray>▐</color><color_light_gray>█/█/█/█/█/█/▓", "</color><color_dark_gray>▀▓▓▓▓▓▓▓▓▓▓▓▓█████████▓█████</color><color_light_gray>█/█/█/█</color><color_dark_gray>/</color><color_light_gray>█/█/ </color>", "         <color_dark_gray>░▒▒▓▀^~▀███████│   </color><color_light_gray>█▄█▄█▄█▄█/</color>", "        <color_dark_gray>▐░░▒▓▓☻▼☻▌■╧╧╧╧☻╛          </color>", "        <color_dark_gray>▒░░░▒▌                          </color>", "       <color_dark_gray>▐░░░▒▒                           </color>", "       <color_dark_gray>░░░░░▌     </color>"]}, {"type": "ascii_art", "id": "1918_BAR", "picture": ["", "     <color_dark_gray>__", "     </color><color_light_gray>▓</color><color_dark_gray>▓", "     </color><color_light_gray>▓</color><color_dark_gray>▓", "     </color><color_light_gray>▓</color><color_dark_gray>▓", "     █▓", "   ▄▄</color><color_light_gray>▓▓", "   </color><color_dark_gray>▀▀</color><color_light_gray>▓▓", "     ▒</color><color_dark_gray>█", " </color>    <color_light_gray>▓</color><color_dark_gray>█_▄▬", "   </color> <color_dark_gray>▐</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▐</color><color_dark_gray>█", "     </color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▐</color><color_dark_gray>█", "     </color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▐</color><color_dark_gray>█", "     </color><color_light_gray>▒</color><color_dark_gray>█</color><color_light_gray>▐</color><color_dark_gray>█", "     </color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▐▒", "     ▓</color><color_dark_gray>█</color><color_light_gray>▐▓", "     ▓</color><color_dark_gray>█</color><color_light_gray>▐▓", "     ▓</color><color_dark_gray>█</color><color_light_gray>▐</color><color_dark_gray>█ </color><color_light_gray>○", "     </color><color_brown>▒████", "     ▒</color><color_light_red>▒</color><color_brown>███▌", "     ▒</color><color_light_red>▒</color><color_brown>██▓▌", "     ▒</color><color_light_red>▒</color><color_brown>███▌", "     ▒██▓▓▌", "     ▒</color><color_light_red>▒</color><color_brown>█▓▓▌", "     ▒</color><color_light_red>▒</color><color_brown>█▓▓▌", "     ▒</color><color_light_red>▒</color><color_brown>█▓▓▌", "     ▒</color><color_light_red>▒</color><color_brown>███▌", "     ▒███▓▌", "    </color><color_dark_gray>▐█████◛", "    ▐</color><color_light_gray>▓</color><color_dark_gray>████</color><color_light_gray>▓</color><color_dark_gray>▄▄▄▄▄▄▄", "    ▐▌</color><color_light_gray>▌</color><color_dark_gray>████■■■■■■■█", "    ▐█</color><color_light_gray>▓</color><color_dark_gray>████</color><color_white>▒</color><color_light_gray>█▓▓</color><color_dark_gray>████</color><color_light_gray>▌", "    </color><color_dark_gray>▐</color><color_light_gray>◛</color><color_dark_gray>████</color><color_light_gray>█</color><color_white>▒</color><color_dark_gray>▀▀▀▀▀▀▀</color><color_light_gray>▐", "    </color><color_dark_gray>█</color><color_light_gray>◙▒</color><color_dark_gray>████</color><color_white>===</color><color_dark_gray>▀▀▀▀▀▀", "    ███</color><color_light_gray>▒</color><color_dark_gray>███▓▓▓", "   </color><color_light_gray>╓</color><color_dark_gray>▄██████</color><color_white>` </color><color_dark_gray>▐", "   ▐▓█</color><color_light_gray>▒▒</color><color_dark_gray>███▄■J", "   ▓▓██████▀~", "  </color><color_light_gray>°</color><color_dark_gray>▀████████", "      ▀████</color><color_brown>█▄", "       </color><color_dark_gray>▐</color><color_light_gray>▒</color><color_dark_gray>█</color><color_brown>█▓▒", "       ▐███▒▌", "       ▐</color><color_light_red>▒</color><color_brown>██▓▒", "       </color> <color_brown>█</color><color_light_red>▒</color><color_brown>██▒▌", "        █</color><color_light_red>▒</color><color_brown>███▒▌", "        █████▓█▒", "        ▐</color><color_light_red>▒</color><color_brown>█████▒", "        ▐</color><color_light_red>▒</color><color_brown>█████▓▒", "        ▐█</color><color_light_red>▒</color><color_brown>█████▒▌", "        </color> <color_brown>█</color><color_light_red>▒</color><color_brown>█████▓▒", "         ██▓▓▓█▓☻▀", "         </color>"]}, {"type": "ascii_art", "id": "FN_five_seven", "picture": ["    <color_light_green>_                               __", "  </color><color_dark_gray>▄▒▒▓▄▄▄▄▄▄▄▄▄▄▄▬▬▄▬▬▬▬▄▄▄▄▄▄▄▄▄▄▄▄▒▒▄", " ▓▒░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░", "</color><color_brown>▐█ ▒▒▒█▒▒▒▒▒███◙█</color><color_dark_gray>▀◙▀</color><color_brown>█◙██◙██◙███████████", "▓▒████████████████☻█@█████▄▄▄▄▄▄▄▄▄▌▀▀▀", "^▀☻█☻☻☻◙◙◙░░░╚ </color><color_dark_gray>‼▓◙</color><color_brown>↔-=≡▓◙▀~ ▀°~''~°▀", "    ▒▒▒▓▓▓█▌▒▌  </color><color_dark_gray>`▓    </color><color_brown>▐▌", "   ▐█▒▓░░░██</color><color_dark_gray>▓▄   ▀►</color><color_brown>_.▄Å", "   █████████▓█~▀°°~~", "   ▓▓◙█◙◙◙◙◙☻▒", "  ▐▓████◙◙◙◙☻▌", "  ▓▒██▓█◙◙◙☻▒", " ▐◙████◙◙◙◙☻▌", " ▐▓█▓▓▓</color><color_dark_gray>◙◙◙☻▓▲", " `▀▀▀▀▀▀▀°°°▀"]}, {"type": "ascii_art", "id": "ashot", "picture": ["                        <color_dark_gray>▒", "▄▄▄▄▓█▒██▒▓▒▒█▓▓▓▓▒■▀▒░▓</color><color_light_gray>._", "</color><color_dark_gray>░▓▓▌▄▒░▓░</color><color_light_gray>▄▄▄███</color><color_white>▒▒▒</color><color_light_gray>▌▒</color><color_dark_gray>▒▓▓</color><color_light_gray>▒▓█▓►─·", "</color><color_dark_gray>░</color><color_light_gray>▓▓</color><color_dark_gray>▌█▒██████░▓▓▀▒▓█▓▓█▓▓██▓█▓▓</color><color_light_gray>'</color>'", "<color_light_gray>▓</color><color_dark_gray>▄▄█▓██▓▓ ▄▄ █▓▓▌╣███████▒█</color><color_yellow>░</color><color_light_gray>▒▒</color><color_dark_gray>█</color><color_brown>▒", "</color><color_dark_gray>╙▐</color><color_brown>▒</color><color_dark_gray>k▓████♥═▓▓███</color><color_light_gray>▒</color><color_dark_gray>▒▓▓███</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▓</color><color_dark_gray>▓▓</color><color_yellow>░</color><color_brown>▓▓</color><color_light_gray>▓</color><color_brown>██", " </color><color_dark_gray>`</color><color_brown>░▌</color><color_dark_gray>▒▓_÷▒ ░▒█████■▐▀▀</color><color_white>∙</color><color_light_gray>▓▓</color><color_dark_gray>██</color><color_light_gray>▓</color><color_dark_gray>█</color><color_light_gray>▒▓█</color><color_brown>▓</color><color_light_gray>▓</color><color_brown>▒░░", "   ▀</color><color_dark_gray>▀▀▀▀    </color><color_light_gray>▄▄▄</color><color_dark_gray>▄ ▀°  </color><color_white>▐</color><color_light_gray>▒</color><color_dark_gray>▒▀▀▀ ▒▀█▓▒▌▀</color><color_brown>░░", "             </color><color_light_gray>`ª█</color><color_dark_gray>▄___</color><color_white>_</color><color_light_gray>▓</color><color_dark_gray>▀      ▌'☻▒░{▐</color><color_brown>█░", "                </color><color_white>`</color><color_dark_gray>~^^         ▐'▐▒4▓</color><color_brown>▓▒</color><color_dark_gray>▌", "                              ▌░~─</color><color_brown>÷╜</color><color_dark_gray>°", "                               ▀'·'"]}, {"type": "ascii_art", "id": "2_shot_special", "picture": ["", "             <color_white>________</color><color_light_gray>∙", "    `°▄█▓▓▓▓▓</color><color_dark_gray>█████████", "   </color><color_light_gray>▄◙▓</color><color_dark_gray>█</color><color_light_gray>▓▐▒</color><color_dark_gray>▄▄▄▄▄▄▄▄▄▄▄▓", "  </color><color_light_gray>▓</color><color_brown>░▒▒</color><color_dark_gray>██▄▄▄▄▄▄───────▀", " ▐</color><color_brown>██</color><color_light_red>▓</color><color_brown>▓░</color><color_dark_gray>█▀▀▀▒▒▌", " █</color><color_brown>█</color><color_light_gray>▓▓</color><color_brown>▒░</color><color_dark_gray>▌    ▀", " ╚█</color><color_brown>█░█</color><color_dark_gray>░▀", "   </color><color_light_gray>▀</color><color_dark_gray>▀▀"]}]